{"backend/tests/test_api/test_project_routes.py::TestProjectRoutes::test_create_project_success": true, "backend/tests/test_api/test_project_routes.py::TestProjectRoutes::test_create_project_validation_error": true, "backend/tests/test_api/test_project_routes.py::TestProjectRoutes::test_create_project_duplicate_error": true, "backend/tests/test_api/test_project_routes.py::TestProjectRoutes::test_get_project_by_id_success": true, "backend/tests/test_api/test_project_routes.py::TestProjectRoutes::test_get_project_by_code_success": true, "backend/tests/test_api/test_project_routes.py::TestProjectRoutes::test_get_project_not_found": true, "backend/tests/test_api/test_project_routes.py::TestProjectRoutes::test_update_project_success": true, "backend/tests/test_api/test_project_routes.py::TestProjectRoutes::test_update_project_not_found": true, "backend/tests/test_api/test_project_routes.py::TestProjectRoutes::test_delete_project_success": true, "backend/tests/test_api/test_project_routes.py::TestProjectRoutes::test_delete_project_not_found": true, "backend/tests/test_api/test_project_routes.py::TestProjectRoutes::test_list_projects_success": true, "backend/tests/test_api/test_project_routes.py::TestProjectRoutes::test_list_projects_with_pagination": true, "backend/tests/test_api/test_project_routes.py::TestProjectRoutes::test_list_projects_include_deleted": true, "backend/tests/test_api/test_project_routes.py::TestProjectRoutes::test_invalid_pagination_parameters": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_create_project": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_get_by_id_existing": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_get_by_id_nonexistent": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_get_by_code_existing": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_get_by_code_nonexistent": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_get_by_name_existing": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_get_by_name_nonexistent": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_get_all_projects": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_get_all_with_pagination": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_get_active_projects": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_get_active_projects_with_pagination": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_update_project": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_update_nonexistent_project": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_soft_delete_project": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_soft_delete_nonexistent_project": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_soft_delete_already_deleted_project": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_search_projects_by_name": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_search_projects_by_project_number": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_search_projects_by_description": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_search_projects_case_insensitive": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_search_projects_excludes_deleted": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_search_projects_with_pagination": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_count_active_projects": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_count_active_projects_empty_db": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_get_project_with_related_data": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_get_project_with_related_data_nonexistent": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_unique_constraint_name": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_unique_constraint_project_number": true, "backend/tests/test_services/test_project_service.py::TestProjectService::test_create_project_success": true, "backend/tests/test_services/test_project_service.py::TestProjectService::test_create_project_validation_error": true, "backend/tests/test_services/test_project_service.py::TestProjectService::test_create_project_duplicate_name_error": true, "backend/tests/test_services/test_project_service.py::TestProjectService::test_get_project_details_by_id": true, "backend/tests/test_services/test_project_service.py::TestProjectService::test_get_project_details_by_code": true, "backend/tests/test_services/test_project_service.py::TestProjectService::test_get_project_details_not_found": true, "backend/tests/test_services/test_project_service.py::TestProjectService::test_update_project_success": true, "backend/tests/test_services/test_project_service.py::TestProjectService::test_delete_project_success": true, "backend/tests/test_services/test_project_service.py::TestProjectService::test_get_projects_list_success": true, "backend/tests/test_schemas/test_component_schemas.py::TestComponentCreateSchema::test_valid_component_creation": true, "backend/tests/test_schemas/test_component_schemas.py::TestComponentCreateSchema::test_minimal_component_creation": true, "backend/tests/test_schemas/test_component_schemas.py::TestComponentCreateSchema::test_name_validation_empty": true, "backend/tests/test_schemas/test_component_schemas.py::TestComponentCreateSchema::test_name_validation_whitespace": true, "backend/tests/test_schemas/test_component_schemas.py::TestComponentCreateSchema::test_name_normalization": true, "backend/tests/test_schemas/test_component_schemas.py::TestComponentCreateSchema::test_category_id_validation": true, "backend/tests/test_schemas/test_component_schemas.py::TestComponentCreateSchema::test_specific_data_json_validation_valid": true, "backend/tests/test_schemas/test_component_schemas.py::TestComponentCreateSchema::test_specific_data_json_validation_invalid": true, "backend/tests/test_schemas/test_component_schemas.py::TestComponentCreateSchema::test_specific_data_empty_string": true, "backend/tests/test_schemas/test_component_schemas.py::TestComponentCreateSchema::test_specific_data_whitespace_only": true, "backend/tests/test_schemas/test_component_schemas.py::TestComponentUpdateSchema::test_valid_partial_update": true, "backend/tests/test_schemas/test_component_schemas.py::TestComponentUpdateSchema::test_empty_update": true, "backend/tests/test_schemas/test_component_schemas.py::TestComponentUpdateSchema::test_update_validation_same_as_create": true, "backend/tests/test_schemas/test_component_schemas.py::TestComponentCategoryCreateSchema::test_valid_category_creation": true, "backend/tests/test_schemas/test_component_schemas.py::TestComponentCategoryCreateSchema::test_minimal_category_creation": true, "backend/tests/test_schemas/test_component_schemas.py::TestComponentCategoryCreateSchema::test_category_with_parent": true, "backend/tests/test_schemas/test_component_schemas.py::TestComponentCategoryCreateSchema::test_category_name_validation_empty": true, "backend/tests/test_schemas/test_component_schemas.py::TestComponentCategoryCreateSchema::test_category_name_validation_whitespace": true, "backend/tests/test_schemas/test_component_schemas.py::TestComponentCategoryCreateSchema::test_category_name_normalization": true, "backend/tests/test_schemas/test_component_schemas.py::TestComponentCategoryUpdateSchema::test_valid_partial_category_update": true, "backend/tests/test_schemas/test_component_schemas.py::TestComponentCategoryUpdateSchema::test_empty_category_update": true, "backend/tests/test_schemas/test_component_schemas.py::TestComponentCategoryUpdateSchema::test_category_update_validation_same_as_create": true, "backend/tests/test_schemas/test_component_schemas.py::TestComponentReadSchema::test_read_schema_includes_all_fields": true, "backend/tests/test_schemas/test_component_schemas.py::TestComponentListResponseSchema::test_list_response_structure": true, "backend/tests/test_schemas/test_component_schemas.py::TestComponentListResponseSchema::test_empty_list_response": true, "backend/tests/test_api/test_heat_tracing_routes.py::TestPipeEndpoints::test_create_pipe_success": true, "backend/tests/test_api/test_heat_tracing_routes.py::TestPipeEndpoints::test_create_pipe_validation_error": true, "backend/tests/test_api/test_heat_tracing_routes.py::TestPipeEndpoints::test_create_pipe_duplicate_error": true, "backend/tests/test_api/test_heat_tracing_routes.py::TestPipeEndpoints::test_get_pipe_success": true, "backend/tests/test_api/test_heat_tracing_routes.py::TestPipeEndpoints::test_get_pipe_not_found": true, "backend/tests/test_api/test_heat_tracing_routes.py::TestPipeEndpoints::test_update_pipe_success": true, "backend/tests/test_api/test_heat_tracing_routes.py::TestPipeEndpoints::test_delete_pipe_success": true, "backend/tests/test_api/test_heat_tracing_routes.py::TestPipeEndpoints::test_list_pipes_success": true, "backend/tests/test_api/test_heat_tracing_routes.py::TestVesselEndpoints::test_create_vessel_success": true, "backend/tests/test_api/test_heat_tracing_routes.py::TestVesselEndpoints::test_get_vessel_success": true, "backend/tests/test_api/test_heat_tracing_routes.py::TestCalculationEndpoints::test_calculate_pipe_heat_loss_success": true, "backend/tests/test_api/test_heat_tracing_routes.py::TestCalculationEndpoints::test_validate_standards_compliance_success": true, "backend/tests/test_api/test_heat_tracing_routes.py::TestDesignWorkflowEndpoints::test_execute_design_workflow_success": true, "backend/tests/test_schemas/test_heat_tracing_schemas.py::TestHeatLossCalculationSchemas::test_heat_loss_calculation_input_valid": true, "backend/tests/test_schemas/test_heat_tracing_schemas.py::TestHeatLossCalculationSchemas::test_heat_loss_calculation_input_fluid_temp_validation": true, "backend/tests/test_schemas/test_heat_tracing_schemas.py::TestHeatLossCalculationSchemas::test_heat_loss_calculation_result_valid": true, "backend/tests/test_schemas/test_heat_tracing_schemas.py::TestPipeSchemas::test_pipe_create_schema_valid": true, "backend/tests/test_schemas/test_heat_tracing_schemas.py::TestPipeSchemas::test_pipe_create_schema_name_validation": true, "backend/tests/test_schemas/test_heat_tracing_schemas.py::TestPipeSchemas::test_pipe_create_schema_diameter_validation": true, "backend/tests/test_schemas/test_heat_tracing_schemas.py::TestPipeSchemas::test_pipe_update_schema_partial": true, "backend/tests/test_schemas/test_heat_tracing_schemas.py::TestPipeSchemas::test_pipe_read_schema_from_dict": true, "backend/tests/test_schemas/test_heat_tracing_schemas.py::TestVesselSchemas::test_vessel_create_schema_valid": true, "backend/tests/test_schemas/test_heat_tracing_schemas.py::TestVesselSchemas::test_vessel_create_schema_dimensions_validation": true, "backend/tests/test_schemas/test_heat_tracing_schemas.py::TestVesselSchemas::test_vessel_create_schema_dimensions_type_validation": true, "backend/tests/test_schemas/test_heat_tracing_schemas.py::TestHTCircuitSchemas::test_htcircuit_create_schema_valid_pipe": true, "backend/tests/test_schemas/test_heat_tracing_schemas.py::TestHTCircuitSchemas::test_htcircuit_create_schema_valid_vessel": true, "backend/tests/test_schemas/test_heat_tracing_schemas.py::TestHTCircuitSchemas::test_htcircuit_create_schema_pipe_or_vessel_validation": true, "backend/tests/test_schemas/test_heat_tracing_schemas.py::TestHTCircuitSchemas::test_htcircuit_create_schema_both_pipe_and_vessel_validation": true, "backend/tests/test_schemas/test_heat_tracing_schemas.py::TestControlCircuitSchemas::test_control_circuit_create_schema_valid": true, "backend/tests/test_schemas/test_heat_tracing_schemas.py::TestControlCircuitSchemas::test_control_circuit_limiting_setpoint_validation": true, "backend/tests/test_schemas/test_heat_tracing_schemas.py::TestDesignWorkflowSchemas::test_design_input_schema_valid": true, "backend/tests/test_schemas/test_heat_tracing_schemas.py::TestDesignWorkflowSchemas::test_design_input_schema_pipe_or_vessel_validation": true, "backend/tests/test_services/test_heat_tracing_service.py::TestHeatTracingServicePipeOperations::test_create_pipe_success": true, "backend/tests/test_services/test_heat_tracing_service.py::TestHeatTracingServicePipeOperations::test_create_pipe_validation_error": true, "backend/tests/test_services/test_heat_tracing_service.py::TestHeatTracingServicePipeOperations::test_create_pipe_duplicate_error": true, "backend/tests/test_services/test_heat_tracing_service.py::TestHeatTracingServicePipeOperations::test_get_pipe_details_success": true, "backend/tests/test_services/test_heat_tracing_service.py::TestHeatTracingServicePipeOperations::test_get_pipe_details_not_found": true, "backend/tests/test_services/test_heat_tracing_service.py::TestHeatTracingServicePipeOperations::test_get_pipe_details_deleted": true, "backend/tests/test_services/test_heat_tracing_service.py::TestHeatTracingServicePipeOperations::test_update_pipe_success": true, "backend/tests/test_services/test_heat_tracing_service.py::TestHeatTracingServicePipeOperations::test_update_pipe_not_found": true, "backend/tests/test_services/test_heat_tracing_service.py::TestHeatTracingServicePipeOperations::test_delete_pipe_success": true, "backend/tests/test_services/test_heat_tracing_service.py::TestHeatTracingServicePipeOperations::test_get_pipes_list_success": true, "backend/tests/test_services/test_heat_tracing_service.py::TestHeatTracingServiceVesselOperations::test_create_vessel_success": true, "backend/tests/test_services/test_heat_tracing_service.py::TestHeatTracingServiceVesselOperations::test_create_vessel_invalid_dimensions": true, "backend/tests/test_services/test_heat_tracing_service.py::TestHeatTracingServiceVesselOperations::test_get_vessel_details_success": true, "backend/tests/test_services/test_heat_tracing_service.py::TestHeatTracingServiceCalculations::test_calculate_pipe_heat_loss_success": true, "backend/tests/test_services/test_heat_tracing_service.py::TestHeatTracingServiceCalculations::test_calculate_pipe_heat_loss_pipe_not_found": true, "backend/tests/test_services/test_heat_tracing_service.py::TestHeatTracingServiceCalculations::test_validate_standards_compliance_success": true, "backend/tests/test_services/test_heat_tracing_service.py::TestHeatTracingServiceDesignWorkflow::test_execute_design_workflow_success": true, "backend/tests/test_repositories/test_heat_tracing_repository.py::TestPipeRepository::test_create_pipe_success": true, "backend/tests/test_repositories/test_heat_tracing_repository.py::TestPipeRepository::test_get_pipe_by_id_success": true, "backend/tests/test_repositories/test_heat_tracing_repository.py::TestPipeRepository::test_get_pipe_by_id_not_found": true, "backend/tests/test_repositories/test_heat_tracing_repository.py::TestPipeRepository::test_get_pipes_by_project_id": true, "backend/tests/test_repositories/test_heat_tracing_repository.py::TestPipeRepository::test_get_pipe_by_line_tag": true, "backend/tests/test_repositories/test_heat_tracing_repository.py::TestPipeRepository::test_get_pipe_by_line_tag_not_found": true, "backend/tests/test_repositories/test_heat_tracing_repository.py::TestPipeRepository::test_get_pipes_without_circuits": true, "backend/tests/test_repositories/test_heat_tracing_repository.py::TestPipeRepository::test_update_heat_loss_calculation": true, "backend/tests/test_repositories/test_heat_tracing_repository.py::TestPipeRepository::test_update_heat_loss_calculation_not_found": true, "backend/tests/test_repositories/test_heat_tracing_repository.py::TestPipeRepository::test_count_by_project": true, "backend/tests/test_repositories/test_heat_tracing_repository.py::TestPipeRepository::test_get_with_heat_loss_calculations": true, "backend/tests/test_repositories/test_heat_tracing_repository.py::TestVesselRepository::test_create_vessel_success": true, "backend/tests/test_repositories/test_heat_tracing_repository.py::TestVesselRepository::test_get_vessel_by_equipment_tag": true, "backend/tests/test_repositories/test_heat_tracing_repository.py::TestVesselRepository::test_get_vessels_without_circuits": true, "backend/tests/test_repositories/test_heat_tracing_repository.py::TestVesselRepository::test_update_heat_loss_calculation": true, "backend/tests/test_repositories/test_heat_tracing_repository.py::TestHTCircuitRepository::test_create_htcircuit_success": true, "backend/tests/test_repositories/test_heat_tracing_repository.py::TestHTCircuitRepository::test_get_by_feeder_id": true, "backend/tests/test_repositories/test_heat_tracing_repository.py::TestHTCircuitRepository::test_get_by_pipe_id": true, "backend/tests/test_repositories/test_heat_tracing_repository.py::TestHTCircuitRepository::test_update_load_calculation": true, "backend/tests/test_repositories/test_heat_tracing_repository.py::TestHTCircuitRepository::test_get_total_feeder_load": true, "backend/tests/test_repositories/test_heat_tracing_repository.py::TestControlCircuitRepository::test_create_control_circuit_success": true, "backend/tests/test_repositories/test_heat_tracing_repository.py::TestControlCircuitRepository::test_get_by_switchboard_id": true, "backend/tests/test_repositories/test_heat_tracing_repository.py::TestControlCircuitRepository::test_get_with_limiting_function": true, "backend/tests/test_repositories/test_heat_tracing_repository.py::TestHeatTracingRepository::test_initialization": true, "backend/tests/test_repositories/test_heat_tracing_repository.py::TestHeatTracingRepository::test_get_project_summary_empty": true, "backend/tests/test_repositories/test_heat_tracing_repository.py::TestHeatTracingRepository::test_get_design_readiness_empty": true, "backend/tests/test_switchboard_api.py": true, "backend/tests/test_user_api.py": true, "backend/tests/test_user_service.py": true, "backend/tests/test_switchboard_service.py::TestSwitchboardService::test_create_switchboard_success": true, "backend/tests/test_switchboard_service.py::TestSwitchboardService::test_create_switchboard_invalid_voltage": true, "backend/tests/test_switchboard_service.py::TestSwitchboardService::test_get_switchboard_success": true, "backend/tests/test_switchboard_service.py::TestSwitchboardService::test_get_switchboard_not_found": true, "backend/tests/test_switchboard_service.py::TestSwitchboardService::test_update_switchboard_success": true, "backend/tests/test_switchboard_service.py::TestSwitchboardService::test_update_switchboard_invalid_voltage": true, "backend/tests/test_switchboard_service.py::TestSwitchboardService::test_delete_switchboard_success": true, "backend/tests/test_switchboard_service.py::TestSwitchboardService::test_delete_switchboard_not_found": true, "backend/tests/test_switchboard_service.py::TestSwitchboardService::test_create_feeder_success": true, "backend/tests/test_switchboard_service.py::TestSwitchboardService::test_create_feeder_switchboard_not_found": true, "backend/tests/test_switchboard_service.py::TestSwitchboardService::test_add_switchboard_component_success": true, "backend/tests/test_switchboard_service.py::TestSwitchboardService::test_add_switchboard_component_switchboard_not_found": true, "backend/tests/test_switchboard_service.py::TestSwitchboardService::test_add_switchboard_component_component_not_found": true, "backend/tests/test_switchboard_service.py::TestSwitchboardService::test_get_switchboard_load_summary_success": true, "backend/tests/test_switchboard_service.py::TestSwitchboardService::test_get_switchboard_capacity_analysis_success": true, "backend/tests/test_electrical_service.py::TestElectricalService::test_cable_sizing_calculation_success": true, "backend/tests/test_electrical_service.py::TestElectricalService::test_voltage_drop_calculation_success": true, "backend/tests/test_electrical_service.py::TestElectricalService::test_electrical_standards_validation_success": true, "backend/tests/test_electrical_service.py::TestElectricalService::test_calculate_load_for_electrical_node_success": true, "backend/tests/test_electrical_service.py::TestElectricalService::test_calculate_load_for_nonexistent_node": true, "backend/tests/test_electrical_service.py::TestElectricalService::test_cable_sizing_calculation_error_handling": true, "backend/tests/test_electrical_service.py::TestElectricalService::test_voltage_drop_calculation_error_handling": true, "backend/tests/test_electrical_service.py::TestElectricalService::test_generate_recommendations": true, "backend/tests/test_minimal_integration.py::TestMinimalIntegration::test_switchboard_schema_with_enum": true, "backend/tests/test_minimal_integration.py::TestMinimalIntegration::test_user_schema_validation": true, "backend/tests/test_minimal_integration.py::TestMinimalIntegration::test_schema_validation_errors": true, "backend/tests/test_minimal_integration.py::TestMinimalIntegration::test_enum_values": true, "backend/tests/test_minimal_integration.py::TestMinimalIntegration::test_schema_serialization": true, "backend/tests/test_minimal_integration.py::TestMinimalIntegration::test_optional_fields": true, "backend/tests/test_minimal_integration.py::TestMinimalIntegration::test_default_values": true, "backend/tests/test_switchboard_schemas.py::TestSwitchboardSchemas::test_switchboard_create_schema_valid": true, "backend/tests/test_switchboard_schemas.py::TestSwitchboardSchemas::test_switchboard_create_schema_invalid_voltage": true, "backend/tests/test_switchboard_schemas.py::TestSwitchboardSchemas::test_switchboard_create_schema_invalid_phases": true, "backend/tests/test_switchboard_schemas.py::TestSwitchboardSchemas::test_switchboard_create_schema_empty_name": true, "backend/tests/test_switchboard_schemas.py::TestSwitchboardSchemas::test_switchboard_update_schema_partial": true, "backend/tests/test_switchboard_schemas.py::TestSwitchboardSchemas::test_feeder_create_schema_valid": true, "backend/tests/test_switchboard_schemas.py::TestSwitchboardSchemas::test_feeder_create_schema_empty_name": true, "backend/tests/test_switchboard_schemas.py::TestSwitchboardSchemas::test_switchboard_component_create_schema_valid": true, "backend/tests/test_switchboard_schemas.py::TestSwitchboardSchemas::test_switchboard_component_create_schema_invalid_quantity": true, "backend/tests/test_switchboard_schemas.py::TestSwitchboardSchemas::test_feeder_component_create_schema_valid": true, "backend/tests/test_switchboard_schemas.py::TestSwitchboardSchemas::test_switchboard_load_summary_schema_valid": true, "backend/tests/test_switchboard_schemas.py::TestSwitchboardSchemas::test_switchboard_capacity_analysis_schema_valid": true, "backend/tests/test_switchboard_schemas.py::TestSwitchboardSchemas::test_switchboard_capacity_analysis_schema_overloaded": true, "backend/tests/test_switchboard_schemas.py::TestSwitchboardSchemaEdgeCases::test_switchboard_minimum_voltage": true, "backend/tests/test_switchboard_schemas.py::TestSwitchboardSchemaEdgeCases::test_switchboard_maximum_voltage": true, "backend/tests/test_switchboard_schemas.py::TestSwitchboardSchemaEdgeCases::test_component_maximum_quantity": true, "backend/tests/test_switchboard_schemas.py::TestSwitchboardSchemaEdgeCases::test_position_whitespace_handling": true, "backend/tests/test_switchboard_schemas.py::TestSwitchboardSchemaEdgeCases::test_optional_fields_none": true, "backend/tests/test_user_schemas.py::TestUserSchemas::test_user_create_schema_valid": true, "backend/tests/test_user_schemas.py::TestUserSchemas::test_user_create_schema_password_validation": true, "backend/tests/test_user_schemas.py::TestUserSchemas::test_user_create_schema_password_no_uppercase": true, "backend/tests/test_user_schemas.py::TestUserSchemas::test_user_create_schema_password_no_digit": true, "backend/tests/test_user_schemas.py::TestUserSchemas::test_user_create_schema_empty_name": true, "backend/tests/test_user_schemas.py::TestUserSchemas::test_user_create_schema_invalid_email": true, "backend/tests/test_user_schemas.py::TestUserSchemas::test_user_update_schema_partial": true, "backend/tests/test_user_schemas.py::TestUserSchemas::test_login_request_schema_valid": true, "backend/tests/test_user_schemas.py::TestUserSchemas::test_login_request_schema_invalid_email": true, "backend/tests/test_user_schemas.py::TestUserSchemas::test_password_change_request_schema_valid": true, "backend/tests/test_user_schemas.py::TestUserSchemas::test_password_change_request_schema_weak_new_password": true, "backend/tests/test_user_schemas.py::TestUserSchemas::test_password_reset_confirm_schema_valid": true, "backend/tests/test_user_schemas.py::TestUserPreferenceSchemas::test_user_preference_create_schema_valid": true, "backend/tests/test_user_schemas.py::TestUserPreferenceSchemas::test_user_preference_create_schema_invalid_theme": true, "backend/tests/test_user_schemas.py::TestUserPreferenceSchemas::test_user_preference_create_schema_invalid_temperature_range": true, "backend/tests/test_user_schemas.py::TestUserPreferenceSchemas::test_user_preference_create_schema_temperature_boundaries": true, "backend/tests/test_user_schemas.py::TestUserPreferenceSchemas::test_user_preference_create_schema_invalid_safety_margin": true, "backend/tests/test_user_schemas.py::TestUserPreferenceSchemas::test_user_preference_update_schema_partial": true, "backend/tests/test_user_schemas.py::TestUserSchemaEdgeCases::test_user_name_whitespace_handling": true, "backend/tests/test_user_schemas.py::TestUserSchemaEdgeCases::test_user_preference_theme_case_sensitivity": true, "backend/tests/test_user_schemas.py::TestUserSchemaEdgeCases::test_user_preference_equal_temperatures": true, "backend/tests/test_user_schemas.py::TestUserSchemaEdgeCases::test_user_optional_email": true, "backend/tests/test_user_schemas.py::TestUserSchemaEdgeCases::test_password_minimum_length": true, "backend/tests/test_repositories/test_electrical_repository.py::TestElectricalNodeRepository::test_get_by_project_id_success": true, "backend/tests/test_repositories/test_electrical_repository.py::TestElectricalNodeRepository::test_get_by_project_id_database_error": true, "backend/tests/test_repositories/test_electrical_repository.py::TestElectricalNodeRepository::test_get_by_node_type_success": true, "backend/tests/test_repositories/test_electrical_repository.py::TestElectricalNodeRepository::test_get_nodes_with_capacity_success": true, "backend/tests/test_repositories/test_electrical_repository.py::TestElectricalNodeRepository::test_count_by_project_success": true, "backend/tests/test_repositories/test_electrical_repository.py::TestElectricalNodeRepository::test_count_by_project_none_result": true, "backend/tests/test_repositories/test_electrical_repository.py::TestCableRouteRepository::test_get_routes_by_node_both_directions": true, "backend/tests/test_repositories/test_electrical_repository.py::TestCableRouteRepository::test_get_routes_by_node_invalid_direction": true, "backend/tests/test_repositories/test_electrical_repository.py::TestCableRouteRepository::test_get_routes_by_installation_method_success": true, "backend/tests/test_repositories/test_electrical_repository.py::TestCableRouteRepository::test_get_routes_with_high_voltage_drop_success": true, "backend/tests/test_repositories/test_electrical_repository.py::TestLoadCalculationRepository::test_get_by_electrical_node_id_success": true, "backend/tests/test_repositories/test_electrical_repository.py::TestLoadCalculationRepository::test_get_by_load_type_success": true, "backend/tests/test_repositories/test_electrical_repository.py::TestLoadCalculationRepository::test_get_total_power_by_node_success": true, "backend/tests/test_repositories/test_electrical_repository.py::TestLoadCalculationRepository::test_get_total_power_by_node_none_result": true, "backend/tests/test_repositories/test_electrical_repository.py::TestVoltageDropCalculationRepository::test_get_by_cable_route_id_success": true, "backend/tests/test_repositories/test_electrical_repository.py::TestVoltageDropCalculationRepository::test_get_non_compliant_calculations_success": true, "backend/tests/test_repositories/test_electrical_repository.py::TestVoltageDropCalculationRepository::test_get_average_voltage_drop_by_project_success": true, "backend/tests/test_repositories/test_electrical_repository.py::TestVoltageDropCalculationRepository::test_count_by_compliance_status_success": true, "backend/tests/test_schemas/test_document_schemas.py::TestImportedDataRevisionSchemas::test_valid_imported_data_revision_creation": true, "backend/tests/test_schemas/test_document_schemas.py::TestImportedDataRevisionSchemas::test_filename_validation_empty": true, "backend/tests/test_schemas/test_document_schemas.py::TestImportedDataRevisionSchemas::test_filename_validation_invalid_extension": true, "backend/tests/test_schemas/test_document_schemas.py::TestImportedDataRevisionSchemas::test_filename_validation_valid_extensions": true, "backend/tests/test_schemas/test_document_schemas.py::TestImportedDataRevisionSchemas::test_revision_identifier_validation_invalid_chars": true, "backend/tests/test_schemas/test_document_schemas.py::TestImportedDataRevisionSchemas::test_revision_identifier_validation_valid_chars": true, "backend/tests/test_schemas/test_document_schemas.py::TestImportedDataRevisionSchemas::test_update_schema_partial_fields": true, "backend/tests/test_schemas/test_document_schemas.py::TestExportedDocumentSchemas::test_valid_exported_document_creation": true, "backend/tests/test_schemas/test_document_schemas.py::TestExportedDocumentSchemas::test_document_filename_validation_empty": true, "backend/tests/test_schemas/test_document_schemas.py::TestExportedDocumentSchemas::test_document_filename_validation_invalid_extension": true, "backend/tests/test_schemas/test_document_schemas.py::TestExportedDocumentSchemas::test_document_filename_validation_valid_extensions": true, "backend/tests/test_schemas/test_document_schemas.py::TestCalculationStandardSchemas::test_valid_calculation_standard_creation": true, "backend/tests/test_schemas/test_document_schemas.py::TestCalculationStandardSchemas::test_standard_name_validation_empty": true, "backend/tests/test_schemas/test_document_schemas.py::TestCalculationStandardSchemas::test_standard_code_validation_empty": true, "backend/tests/test_schemas/test_document_schemas.py::TestCalculationStandardSchemas::test_standard_code_normalization": true, "backend/tests/test_schemas/test_document_schemas.py::TestCalculationStandardSchemas::test_standard_code_validation_invalid_chars": true, "backend/tests/test_schemas/test_document_schemas.py::TestCalculationStandardSchemas::test_parameters_json_validation_invalid_json": true, "backend/tests/test_schemas/test_document_schemas.py::TestCalculationStandardSchemas::test_parameters_json_validation_not_object": true, "backend/tests/test_schemas/test_document_schemas.py::TestCalculationStandardSchemas::test_parameters_json_validation_valid_object": true, "backend/tests/test_schemas/test_document_schemas.py::TestEnums::test_import_type_enum_values": true, "backend/tests/test_schemas/test_document_schemas.py::TestEnums::test_document_type_enum_values": true, "backend/tests/test_schemas/test_document_schemas.py::TestEnums::test_file_format_enum_values": true, "backend/tests/test_schemas/test_electrical_schemas.py::TestCalculationIntegrationSchemas::test_cable_sizing_calculation_input_valid": true, "backend/tests/test_schemas/test_electrical_schemas.py::TestCalculationIntegrationSchemas::test_cable_sizing_calculation_input_invalid_power": true, "backend/tests/test_schemas/test_electrical_schemas.py::TestCalculationIntegrationSchemas::test_voltage_drop_calculation_input_valid": true, "backend/tests/test_schemas/test_electrical_schemas.py::TestCalculationIntegrationSchemas::test_cable_sizing_calculation_result_valid": true, "backend/tests/test_schemas/test_electrical_schemas.py::TestElectricalNodeSchemas::test_electrical_node_create_schema_valid": true, "backend/tests/test_schemas/test_electrical_schemas.py::TestElectricalNodeSchemas::test_electrical_node_create_schema_name_validation": true, "backend/tests/test_schemas/test_electrical_schemas.py::TestElectricalNodeSchemas::test_electrical_node_update_schema_partial": true, "backend/tests/test_schemas/test_electrical_schemas.py::TestElectricalNodeSchemas::test_electrical_node_read_schema_from_dict": true, "backend/tests/test_schemas/test_electrical_schemas.py::TestCableRouteSchemas::test_cable_route_create_schema_valid": true, "backend/tests/test_schemas/test_electrical_schemas.py::TestCableRouteSchemas::test_cable_route_create_schema_same_nodes_validation": true, "backend/tests/test_schemas/test_electrical_schemas.py::TestCableRouteSchemas::test_cable_route_temperature_range_validation": true, "backend/tests/test_schemas/test_electrical_schemas.py::TestLoadCalculationSchemas::test_load_calculation_create_schema_valid": true, "backend/tests/test_schemas/test_electrical_schemas.py::TestLoadCalculationSchemas::test_load_calculation_electrical_parameters_validation": true, "backend/tests/test_schemas/test_electrical_schemas.py::TestDesignWorkflowSchemas::test_electrical_design_input_schema_valid": true, "backend/tests/test_schemas/test_electrical_schemas.py::TestDesignWorkflowSchemas::test_electrical_design_input_schema_node_or_route_validation": true, "backend/tests/test_schemas/test_project_schemas.py::TestProjectCreateSchema::test_valid_project_creation": true, "backend/tests/test_schemas/test_project_schemas.py::TestProjectCreateSchema::test_project_number_normalization": true, "backend/tests/test_schemas/test_project_schemas.py::TestProjectCreateSchema::test_name_validation_empty": true, "backend/tests/test_schemas/test_project_schemas.py::TestProjectCreateSchema::test_name_validation_whitespace": true, "backend/tests/test_schemas/test_project_schemas.py::TestProjectCreateSchema::test_project_number_validation_empty": true, "backend/tests/test_schemas/test_project_schemas.py::TestProjectCreateSchema::test_project_number_validation_invalid_chars": true, "backend/tests/test_schemas/test_project_schemas.py::TestProjectCreateSchema::test_temperature_range_validation": true, "backend/tests/test_schemas/test_project_schemas.py::TestProjectCreateSchema::test_temperature_bounds_validation": true, "backend/tests/test_schemas/test_project_schemas.py::TestProjectCreateSchema::test_wind_speed_validation": true, "backend/tests/test_schemas/test_project_schemas.py::TestProjectCreateSchema::test_voltages_json_validation_valid": true, "backend/tests/test_schemas/test_project_schemas.py::TestProjectCreateSchema::test_voltages_json_validation_invalid_json": true, "backend/tests/test_schemas/test_project_schemas.py::TestProjectCreateSchema::test_voltages_json_validation_not_array": true, "backend/tests/test_schemas/test_project_schemas.py::TestProjectCreateSchema::test_voltages_json_validation_negative_voltage": true, "backend/tests/test_schemas/test_project_schemas.py::TestProjectUpdateSchema::test_valid_partial_update": true, "backend/tests/test_schemas/test_project_schemas.py::TestProjectUpdateSchema::test_empty_update": true, "backend/tests/test_schemas/test_project_schemas.py::TestProjectUpdateSchema::test_update_validation_same_as_create": true, "backend/tests/test_schemas/test_project_schemas.py::TestProjectReadSchema::test_from_orm_conversion": true, "backend/tests/test_schemas/test_project_schemas.py::TestProjectReadSchema::test_read_schema_includes_all_fields": true, "backend/tests/test_schemas/test_project_schemas.py::TestProjectSummarySchema::test_summary_includes_essential_fields": true, "backend/tests/test_schemas/test_project_schemas.py::TestProjectSummarySchema::test_summary_excludes_detailed_fields": true, "backend/tests/test_schemas/test_project_schemas.py::TestProjectListResponseSchema::test_list_response_structure": true, "backend/tests/test_schemas/test_project_schemas.py::TestProjectListResponseSchema::test_empty_list_response": true, "backend/tests/test_services/test_component_service.py::TestComponentService::test_create_component_success": true, "backend/tests/test_services/test_component_service.py::TestComponentService::test_create_component_category_not_found": true, "backend/tests/test_services/test_component_service.py::TestComponentService::test_create_component_duplicate_name": true, "backend/tests/test_services/test_component_service.py::TestComponentService::test_create_component_integrity_error": true, "backend/tests/test_services/test_component_service.py::TestComponentService::test_get_component_details_success": true, "backend/tests/test_services/test_component_service.py::TestComponentService::test_get_component_details_not_found": true, "backend/tests/test_services/test_component_service.py::TestComponentService::test_get_component_details_deleted": true, "backend/tests/test_services/test_component_service.py::TestComponentService::test_update_component_success": true, "backend/tests/test_services/test_component_service.py::TestComponentService::test_update_component_not_found": true, "backend/tests/test_services/test_component_service.py::TestComponentService::test_delete_component_success": true, "backend/tests/test_services/test_component_service.py::TestComponentService::test_delete_component_not_found": true, "backend/tests/test_services/test_component_service.py::TestComponentService::test_get_components_list_success": true, "backend/tests/test_services/test_component_service.py::TestComponentService::test_get_components_list_with_category_filter": true, "backend/tests/test_services/test_component_service.py::TestComponentService::test_get_components_list_with_search": true, "backend/tests/test_activity_log_repository.py::TestActivityLogRepository::test_repository_initialization": true, "backend/tests/test_activity_log_repository.py::TestActivityLogRepository::test_get_by_user_id": true, "backend/tests/test_activity_log_repository.py::TestActivityLogRepository::test_get_by_entity": true, "backend/tests/test_activity_log_repository.py::TestActivityLogRepository::test_get_by_event_type": true, "backend/tests/test_activity_log_repository.py::TestActivityLogRepository::test_get_by_date_range": true, "backend/tests/test_activity_log_repository.py::TestActivityLogRepository::test_search_by_details": true, "backend/tests/test_activity_log_repository.py::TestActivityLogRepository::test_filter_activity_logs": true, "backend/tests/test_activity_log_repository.py::TestActivityLogRepository::test_count_filtered_activity_logs": true, "backend/tests/test_activity_log_repository.py::TestActivityLogRepository::test_get_security_events": true, "backend/tests/test_activity_log_repository.py::TestActivityLogRepository::test_get_user_activity_summary": true, "backend/tests/test_activity_log_repository.py::TestActivityLogRepository::test_get_daily_activity_counts": true, "backend/tests/test_activity_log_repository.py::TestActivityLogRepository::test_get_recent_activity": true, "backend/tests/test_activity_log_repository.py::TestActivityLogRepository::test_delete_old_logs": true, "backend/tests/test_activity_log_repository.py::TestActivityLogRepository::test_get_unique_users_count": true, "backend/tests/test_activity_log_repository.py::TestActivityLogRepository::test_get_unique_entities_count": true, "backend/tests/test_activity_log_repository.py::TestActivityLogRepository::test_database_error_handling": true, "backend/tests/test_activity_log_repository.py::TestActivityLogRepository::test_empty_results": true, "backend/tests/test_activity_log_service.py::TestActivityLogService::test_service_initialization": true, "backend/tests/test_activity_log_service.py::TestActivityLogService::test_log_event_success": true, "backend/tests/test_activity_log_service.py::TestActivityLogService::test_log_event_system_event": true, "backend/tests/test_activity_log_service.py::TestActivityLogService::test_log_event_invalid_user": true, "backend/tests/test_activity_log_service.py::TestActivityLogService::test_log_user_action_success": true, "backend/tests/test_activity_log_service.py::TestActivityLogService::test_log_user_action_invalid_action": true, "backend/tests/test_activity_log_service.py::TestActivityLogService::test_log_user_action_invalid_entity_type": true, "backend/tests/test_activity_log_service.py::TestActivityLogService::test_log_authentication_event_success": true, "backend/tests/test_activity_log_service.py::TestActivityLogService::test_log_authentication_event_failed_login": true, "backend/tests/test_activity_log_service.py::TestActivityLogService::test_log_system_event_success": true, "backend/tests/test_activity_log_service.py::TestActivityLogService::test_get_activity_log_success": true, "backend/tests/test_activity_log_service.py::TestActivityLogService::test_get_activity_log_not_found": true, "backend/tests/test_activity_log_service.py::TestActivityLogService::test_get_user_activity_logs_success": true, "backend/tests/test_activity_log_service.py::TestActivityLogService::test_get_user_activity_logs_user_not_found": true, "backend/tests/test_activity_log_service.py::TestActivityLogService::test_log_security_event_success": true, "backend/tests/test_activity_log_service.py::TestActivityLogService::test_log_security_event_invalid_severity": true, "backend/tests/test_activity_log_service.py::TestActivityLogService::test_log_security_event_invalid_threat_level": true, "backend/tests/test_activity_log_service.py::TestActivityLogService::test_update_activity_log_success": true, "backend/tests/test_activity_log_service.py::TestActivityLogService::test_update_activity_log_not_found": true, "backend/tests/test_activity_log_service.py::TestActivityLogService::test_delete_old_activity_logs_success": true, "backend/tests/test_activity_log_service.py::TestActivityLogService::test_delete_old_activity_logs_invalid_days": true, "backend/tests/test_activity_log_service.py::TestActivityLogService::test_generate_audit_report_success": true, "backend/tests/test_activity_log_service.py::TestActivityLogService::test_get_user_activity_summary_success": true, "backend/tests/test_activity_log_service.py::TestActivityLogService::test_get_user_activity_summary_invalid_date_range": true, "backend/tests/test_activity_log_service.py::TestActivityLogService::test_database_error_handling": true, "backend/tests/test_activity_log_service.py::TestActivityLogService::test_transaction_rollback_on_error": true, "backend/tests/test_api/test_switchboard_api.py::TestSwitchboardAPI::test_create_switchboard_success": true, "backend/tests/test_api/test_switchboard_api.py::TestSwitchboardAPI::test_create_switchboard_invalid_data": true, "backend/tests/test_api/test_switchboard_api.py::TestSwitchboardAPI::test_get_switchboard_success": true, "backend/tests/test_api/test_switchboard_api.py::TestSwitchboardAPI::test_get_switchboard_not_found": true, "backend/tests/test_api/test_switchboard_api.py::TestSwitchboardAPI::test_list_switchboards_success": true, "backend/tests/test_api/test_switchboard_api.py::TestSwitchboardAPI::test_update_switchboard_success": true, "backend/tests/test_api/test_switchboard_api.py::TestSwitchboardAPI::test_delete_switchboard_success": true, "backend/tests/test_api/test_switchboard_api.py::TestSwitchboardAPI::test_create_feeder_success": true, "backend/tests/test_api/test_switchboard_api.py::TestSwitchboardAPI::test_get_feeder_success": true, "backend/tests/test_api/test_switchboard_api.py::TestSwitchboardAPI::test_add_switchboard_component_success": true, "backend/tests/test_api/test_switchboard_api.py::TestSwitchboardAPI::test_get_load_summary_success": true, "backend/tests/test_api/test_switchboard_api.py::TestSwitchboardAPI::test_get_capacity_analysis_success": true, "backend/tests/test_api/test_switchboard_api.py::TestSwitchboardAPI::test_business_logic_error_handling": true, "backend/tests/test_api/test_user_api.py::TestUserAuthenticationAPI::test_login_success": true, "backend/tests/test_api/test_user_api.py::TestUserAuthenticationAPI::test_login_invalid_credentials": true, "backend/tests/test_api/test_user_api.py::TestUserAuthenticationAPI::test_logout_success": true, "backend/tests/test_api/test_user_api.py::TestUserAuthenticationAPI::test_change_password_success": true, "backend/tests/test_api/test_user_api.py::TestUserAuthenticationAPI::test_change_password_invalid_current": true, "backend/tests/test_api/test_user_api.py::TestUserManagementAPI::test_create_user_success": true, "backend/tests/test_api/test_user_api.py::TestUserManagementAPI::test_create_user_email_exists": true, "backend/tests/test_api/test_user_api.py::TestUserManagementAPI::test_get_user_success": true, "backend/tests/test_api/test_user_api.py::TestUserManagementAPI::test_get_user_not_found": true, "backend/tests/test_api/test_user_api.py::TestUserManagementAPI::test_list_users_success": true, "backend/tests/test_api/test_user_api.py::TestUserManagementAPI::test_list_users_with_search": true, "backend/tests/test_api/test_user_api.py::TestUserManagementAPI::test_update_user_success": true, "backend/tests/test_api/test_user_api.py::TestUserManagementAPI::test_deactivate_user_success": true, "backend/tests/test_api/test_user_api.py::TestUserPreferencesAPI::test_get_user_preferences_success": true, "backend/tests/test_api/test_user_api.py::TestUserPreferencesAPI::test_get_user_preferences_not_found": true, "backend/tests/test_api/test_user_api.py::TestUserPreferencesAPI::test_update_user_preferences_success": true, "backend/tests/test_api/test_user_api.py::TestUserPreferencesAPI::test_delete_user_preferences_success": true, "backend/tests/test_services/test_user_service.py::TestUserService::test_create_user_success": true, "backend/tests/test_services/test_user_service.py::TestUserService::test_create_user_email_already_exists": true, "backend/tests/test_services/test_user_service.py::TestUserService::test_get_user_success": true, "backend/tests/test_services/test_user_service.py::TestUserService::test_get_user_not_found": true, "backend/tests/test_services/test_user_service.py::TestUserService::test_get_user_inactive": true, "backend/tests/test_services/test_user_service.py::TestUserService::test_login_success": true, "backend/tests/test_services/test_user_service.py::TestUserService::test_login_invalid_email": true, "backend/tests/test_services/test_user_service.py::TestUserService::test_login_invalid_password": true, "backend/tests/test_services/test_user_service.py::TestUserService::test_login_inactive_user": true, "backend/tests/test_services/test_user_service.py::TestUserService::test_change_password_success": true, "backend/tests/test_services/test_user_service.py::TestUserService::test_change_password_invalid_current": true, "backend/tests/test_services/test_user_service.py::TestUserService::test_update_user_success": true, "backend/tests/test_services/test_user_service.py::TestUserService::test_deactivate_user_success": true, "backend/tests/test_services/test_user_service.py::TestUserService::test_get_user_preferences_success": true, "backend/tests/test_services/test_user_service.py::TestUserService::test_get_user_preferences_not_found": true, "backend/tests/test_services/test_user_service.py::TestUserService::test_create_or_update_user_preferences_success": true, "backend/tests/test_services/test_user_service.py::TestUserService::test_search_users_success": true, "backend/tests/test_services/test_user_service.py::TestUserService::test_count_active_users_success": true, "backend/tests/test_services/test_user_service.py::TestUserService::test_hash_password": true, "backend/tests/test_services/test_user_service.py::TestUserService::test_verify_password": true, "backend/tests/test_api/test_electrical_routes.py::TestElectricalNodeRoutes::test_create_electrical_node_success": true, "backend/tests/test_api/test_electrical_routes.py::TestElectricalNodeRoutes::test_create_electrical_node_validation_error": true, "backend/tests/test_api/test_electrical_routes.py::TestElectricalNodeRoutes::test_get_electrical_node_success": true, "backend/tests/test_api/test_electrical_routes.py::TestElectricalNodeRoutes::test_get_electrical_node_not_found": true, "backend/tests/test_api/test_electrical_routes.py::TestElectricalNodeRoutes::test_list_electrical_nodes_success": true, "backend/tests/test_api/test_electrical_routes.py::TestElectricalNodeRoutes::test_update_electrical_node_success": true, "backend/tests/test_api/test_electrical_routes.py::TestElectricalNodeRoutes::test_delete_electrical_node_success": true, "backend/tests/test_api/test_electrical_routes.py::TestCableRouteRoutes::test_create_cable_route_success": true, "backend/tests/test_api/test_electrical_routes.py::TestCableRouteRoutes::test_get_cable_route_success": true, "backend/tests/test_api/test_electrical_routes.py::TestCableRouteRoutes::test_list_cable_routes_success": true, "backend/tests/test_api/test_electrical_routes.py::TestLoadCalculationRoutes::test_create_load_calculation_success": true, "backend/tests/test_api/test_electrical_routes.py::TestLoadCalculationRoutes::test_get_load_calculation_success": true, "backend/tests/test_api/test_electrical_routes.py::TestLoadCalculationRoutes::test_list_load_calculations_success": true, "backend/tests/test_api/test_electrical_routes.py::TestElectricalCalculationRoutes::test_cable_sizing_calculation_success": true, "backend/tests/test_api/test_electrical_routes.py::TestElectricalCalculationRoutes::test_voltage_drop_calculation_success": true, "backend/tests/test_api/test_electrical_routes.py::TestElectricalCalculationRoutes::test_standards_validation_success": true, "backend/tests/test_api/test_electrical_routes.py::TestElectricalBusinessLogicRoutes::test_node_load_summary_success": true, "backend/tests/test_api/test_electrical_routes.py::TestElectricalBusinessLogicRoutes::test_optimize_cable_route_success": true, "backend/tests/test_api/test_electrical_routes.py::TestElectricalBusinessLogicRoutes::test_design_workflow_success": true, "backend/tests/test_calculations/test_calculation_service.py::TestCalculationService::test_initialization": true, "backend/tests/test_calculations/test_calculation_service.py::TestCalculationService::test_calculate_heat_tracing_system_valid_input": true, "backend/tests/test_calculations/test_calculation_service.py::TestCalculationService::test_calculate_heat_tracing_system_single_circuit": true, "backend/tests/test_calculations/test_calculation_service.py::TestCalculationService::test_calculate_electrical_system_valid_input": true, "backend/tests/test_calculations/test_calculation_service.py::TestCalculationService::test_calculate_heat_tracing_system_invalid_input": true, "backend/tests/test_calculations/test_calculation_service.py::TestCalculationService::test_calculate_heat_tracing_system_empty_circuits": true, "backend/tests/test_calculations/test_calculation_service.py::TestCalculationService::test_calculate_system_summary": true, "backend/tests/test_calculations/test_calculation_service.py::TestCalculationService::test_calculate_with_different_safety_factors": true, "backend/tests/test_calculations/test_calculation_service.py::TestCalculationService::test_calculate_with_optimization_options": true, "backend/tests/test_calculations/test_calculation_service.py::TestCalculationService::test_calculate_parallel_processing": true, "backend/tests/test_calculations/test_calculation_service.py::TestCalculationService::test_calculate_with_caching": true, "backend/tests/test_calculations/test_calculation_service.py::TestCalculationService::test_calculate_with_validation_errors": true, "backend/tests/test_calculations/test_calculation_service.py::TestCalculationService::test_calculate_load_analysis": true, "backend/tests/test_calculations/test_calculation_service.py::TestCalculationService::test_calculate_cost_estimation": true, "backend/tests/test_calculations/test_calculation_service.py::TestCalculationService::test_calculate_energy_analysis": true, "backend/tests/test_calculations/test_calculation_service.py::TestCalculationService::test_error_handling_and_recovery": true, "backend/tests/test_calculations/test_calculation_service.py::TestCalculationService::test_logging_functionality": true, "backend/tests/test_calculations/test_calculation_service.py::TestCalculationService::test_performance_monitoring": true, "backend/tests/test_services/test_electrical_service.py::TestElectricalService::test_cable_sizing_calculation_success": true, "backend/tests/test_services/test_electrical_service.py::TestElectricalService::test_voltage_drop_calculation_success": true, "backend/tests/test_services/test_electrical_service.py::TestElectricalService::test_electrical_standards_validation_success": true, "backend/tests/test_services/test_electrical_service.py::TestElectricalService::test_calculate_load_for_electrical_node_success": true, "backend/tests/test_services/test_electrical_service.py::TestElectricalService::test_calculate_load_for_nonexistent_node": true, "backend/tests/test_services/test_electrical_service.py::TestElectricalService::test_cable_sizing_calculation_error_handling": true, "backend/tests/test_services/test_electrical_service.py::TestElectricalService::test_voltage_drop_calculation_error_handling": true, "backend/tests/test_services/test_electrical_service.py::TestElectricalService::test_generate_recommendations": true, "backend/tests/test_services/test_switchboard_service.py::TestSwitchboardService::test_create_switchboard_success": true, "backend/tests/test_services/test_switchboard_service.py::TestSwitchboardService::test_create_switchboard_invalid_voltage": true, "backend/tests/test_services/test_switchboard_service.py::TestSwitchboardService::test_get_switchboard_success": true, "backend/tests/test_services/test_switchboard_service.py::TestSwitchboardService::test_get_switchboard_not_found": true, "backend/tests/test_services/test_switchboard_service.py::TestSwitchboardService::test_update_switchboard_success": true, "backend/tests/test_services/test_switchboard_service.py::TestSwitchboardService::test_update_switchboard_invalid_voltage": true, "backend/tests/test_services/test_switchboard_service.py::TestSwitchboardService::test_delete_switchboard_success": true, "backend/tests/test_services/test_switchboard_service.py::TestSwitchboardService::test_delete_switchboard_not_found": true, "backend/tests/test_services/test_switchboard_service.py::TestSwitchboardService::test_create_feeder_success": true, "backend/tests/test_services/test_switchboard_service.py::TestSwitchboardService::test_create_feeder_switchboard_not_found": true, "backend/tests/test_services/test_switchboard_service.py::TestSwitchboardService::test_add_switchboard_component_success": true, "backend/tests/test_services/test_switchboard_service.py::TestSwitchboardService::test_add_switchboard_component_switchboard_not_found": true, "backend/tests/test_services/test_switchboard_service.py::TestSwitchboardService::test_add_switchboard_component_component_not_found": true, "backend/tests/test_services/test_switchboard_service.py::TestSwitchboardService::test_get_switchboard_load_summary_success": true, "backend/tests/test_services/test_switchboard_service.py::TestSwitchboardService::test_get_switchboard_capacity_analysis_success": true, "backend/tests/test_utils.py::TestUUIDUtils::test_generate_uuid7": true, "backend/tests/test_utils.py::TestUUIDUtils::test_generate_uuid7_str": true, "backend/tests/test_utils.py::TestUUIDUtils::test_is_valid_uuid": true, "backend/tests/test_utils.py::TestUUIDUtils::test_uuid_conversion": true, "backend/tests/test_utils.py::TestUUIDUtils::test_invalid_uuid_conversion": true, "backend/tests/test_utils.py::TestDateTimeUtils::test_utcnow_aware": true, "backend/tests/test_utils.py::TestDateTimeUtils::test_format_datetime": true, "backend/tests/test_utils.py::TestDateTimeUtils::test_parse_datetime": true, "backend/tests/test_utils.py::TestDateTimeUtils::test_calculate_time_difference": true, "backend/tests/test_utils.py::TestStringUtils::test_slugify": true, "backend/tests/test_utils.py::TestStringUtils::test_slugify_with_options": true, "backend/tests/test_utils.py::TestStringUtils::test_sanitize_text": true, "backend/tests/test_utils.py::TestStringUtils::test_hash_string": true, "backend/tests/test_utils.py::TestStringUtils::test_truncate_string": true, "backend/tests/test_utils.py::TestStringUtils::test_pad_string": true, "backend/tests/test_utils.py::TestFileIOUtils::test_temporary_file": true, "backend/tests/test_utils.py::TestFileIOUtils::test_temporary_directory": true, "backend/tests/test_utils.py::TestFileIOUtils::test_json_file_operations": true, "backend/tests/test_utils.py::TestFileIOUtils::test_file_io_error_handling": true, "backend/tests/test_utils.py::TestJSONValidation::test_validate_json_data_success": true, "backend/tests/test_utils.py::TestJSONValidation::test_validate_json_data_failure": true, "backend/tests/test_utils.py::TestPaginationUtils::test_pagination_params_validation": true, "backend/tests/test_utils.py::TestPaginationUtils::test_parse_pagination_params": true, "backend/tests/test_utils.py::TestIntegration::test_uuid_datetime_combination": true, "backend/tests/test_utils.py::TestIntegration::test_file_json_validation_combination": true, "backend/tests/test_api/test_activity_log_routes.py::TestActivityLogRoutes::test_create_activity_log_success": true, "backend/tests/test_api/test_activity_log_routes.py::TestActivityLogRoutes::test_get_activity_log_success": true, "backend/tests/test_api/test_activity_log_routes.py::TestActivityLogRoutes::test_get_activity_log_not_found": true, "backend/tests/test_api/test_activity_log_routes.py::TestActivityLogRoutes::test_list_activity_logs_success": true, "backend/tests/test_api/test_activity_log_routes.py::TestActivityLogRoutes::test_list_activity_logs_with_filters": true, "backend/tests/test_api/test_activity_log_routes.py::TestActivityLogRoutes::test_update_activity_log_success": true, "backend/tests/test_api/test_activity_log_routes.py::TestActivityLogRoutes::test_get_user_activity_logs_success": true, "backend/tests/test_api/test_activity_log_routes.py::TestActivityLogRoutes::test_get_user_activity_summary_success": true, "backend/tests/test_api/test_activity_log_routes.py::TestActivityLogRoutes::test_get_entity_activity_logs_success": true, "backend/tests/test_api/test_activity_log_routes.py::TestActivityLogRoutes::test_get_security_events_success": true, "backend/tests/test_api/test_activity_log_routes.py::TestActivityLogRoutes::test_log_security_event_success": true, "backend/tests/test_api/test_activity_log_routes.py::TestActivityLogRoutes::test_get_recent_activity_success": true, "backend/tests/test_api/test_activity_log_routes.py::TestActivityLogRoutes::test_search_activity_logs_success": true, "backend/tests/test_api/test_activity_log_routes.py::TestActivityLogRoutes::test_delete_old_activity_logs_success": true, "backend/tests/test_api/test_activity_log_routes.py::TestActivityLogRoutes::test_invalid_input_error_handling": true, "backend/tests/test_api/test_activity_log_routes.py::TestActivityLogRoutes::test_database_error_handling": true, "backend/tests/test_api/test_activity_log_routes.py::TestActivityLogRoutes::test_validation_error_handling": true, "backend/tests/test_api/test_component_routes.py::TestComponentRoutes::test_create_component_success": true, "backend/tests/test_api/test_component_routes.py::TestComponentRoutes::test_create_component_duplicate_error": true, "backend/tests/test_api/test_component_routes.py::TestComponentRoutes::test_create_component_validation_error": true, "backend/tests/test_api/test_component_routes.py::TestComponentRoutes::test_get_component_success": true, "backend/tests/test_api/test_component_routes.py::TestComponentRoutes::test_get_component_not_found": true, "backend/tests/test_api/test_component_routes.py::TestComponentRoutes::test_update_component_success": true, "backend/tests/test_api/test_component_routes.py::TestComponentRoutes::test_delete_component_success": true, "backend/tests/test_api/test_component_routes.py::TestComponentRoutes::test_list_components_success": true, "backend/tests/test_api/test_component_routes.py::TestComponentRoutes::test_list_components_with_filters": true, "backend/tests/test_api/test_component_routes.py::TestComponentCategoryRoutes::test_create_category_success": true, "backend/tests/test_api/test_component_routes.py::TestComponentCategoryRoutes::test_get_category_success": true, "backend/tests/test_api/test_component_routes.py::TestComponentCategoryRoutes::test_list_categories_success": true, "backend/tests/test_api/test_component_routes_error_handling.py::TestComponentErrorHandling::test_create_component_database_error": true, "backend/tests/test_api/test_component_routes_error_handling.py::TestComponentErrorHandling::test_create_component_integrity_constraint_violation": true, "backend/tests/test_api/test_component_routes_error_handling.py::TestComponentErrorHandling::test_create_component_malformed_json_data": true, "backend/tests/test_api/test_component_routes_error_handling.py::TestComponentErrorHandling::test_create_component_extremely_large_payload": true, "backend/tests/test_api/test_component_routes_error_handling.py::TestComponentErrorHandling::test_get_component_service_unavailable": true, "backend/tests/test_api/test_component_routes_error_handling.py::TestComponentErrorHandling::test_update_component_concurrent_modification": true, "backend/tests/test_api/test_component_routes_error_handling.py::TestComponentErrorHandling::test_delete_component_with_dependencies": true, "backend/tests/test_api/test_component_routes_error_handling.py::TestComponentErrorHandling::test_list_components_database_timeout": true, "backend/tests/test_api/test_component_routes_error_handling.py::TestComponentErrorHandling::test_search_components_with_sql_injection_attempt": true, "backend/tests/test_api/test_component_routes_error_handling.py::TestComponentCategoryErrorHandling::test_create_category_circular_dependency": true, "backend/tests/test_api/test_component_routes_error_handling.py::TestComponentCategoryErrorHandling::test_delete_category_with_subcategories": true, "backend/tests/test_api/test_component_routes_error_handling.py::TestComponentCategoryErrorHandling::test_update_category_invalid_parent_hierarchy": true, "backend/tests/test_api/test_component_routes_error_handling.py::TestComponentValidationEdgeCases::test_component_name_unicode_characters": true, "backend/tests/test_api/test_component_routes_error_handling.py::TestComponentValidationEdgeCases::test_component_specific_data_edge_cases": true, "backend/tests/test_api/test_document_routes.py::TestCalculationStandardRoutes::test_create_calculation_standard": true, "backend/tests/test_api/test_document_routes.py::TestCalculationStandardRoutes::test_get_calculation_standard_by_id": true, "backend/tests/test_api/test_document_routes.py::TestCalculationStandardRoutes::test_get_calculation_standard_by_code": true, "backend/tests/test_api/test_document_routes.py::TestCalculationStandardRoutes::test_list_calculation_standards": true, "backend/tests/test_api/test_document_routes.py::TestCalculationStandardRoutes::test_update_calculation_standard": true, "backend/tests/test_api/test_document_routes.py::TestCalculationStandardRoutes::test_delete_calculation_standard": true, "backend/tests/test_api/test_document_routes.py::TestCalculationStandardRoutes::test_create_calculation_standard_duplicate_code": true, "backend/tests/test_api/test_document_routes.py::TestCalculationStandardRoutes::test_get_nonexistent_calculation_standard": true, "backend/tests/test_api/test_document_routes.py::TestCalculationStandardRoutes::test_calculation_standard_validation_errors": true, "backend/tests/test_api/test_document_routes.py::TestImportedDataRevisionRoutes::test_create_imported_data_revision": true, "backend/tests/test_api/test_document_routes.py::TestImportedDataRevisionRoutes::test_list_imported_data_revisions_by_project": true, "backend/tests/test_api/test_document_routes.py::TestExportedDocumentRoutes::test_create_exported_document": true, "backend/tests/test_api/test_document_routes.py::TestExportedDocumentRoutes::test_list_exported_documents_by_project": true, "backend/tests/test_api/test_electrical_routes_edge_cases.py::TestElectricalNodeEdgeCases::test_create_electrical_node_boundary_values": true, "backend/tests/test_api/test_electrical_routes_edge_cases.py::TestElectricalNodeEdgeCases::test_create_electrical_node_extreme_string_lengths": true, "backend/tests/test_api/test_electrical_routes_edge_cases.py::TestElectricalNodeEdgeCases::test_create_electrical_node_invalid_enum_values": true, "backend/tests/test_api/test_electrical_routes_edge_cases.py::TestElectricalNodeEdgeCases::test_get_electrical_node_invalid_ids": true, "backend/tests/test_api/test_electrical_routes_edge_cases.py::TestElectricalNodeEdgeCases::test_list_electrical_nodes_pagination_edge_cases": true, "backend/tests/test_api/test_electrical_routes_edge_cases.py::TestCableRouteEdgeCases::test_create_cable_route_same_nodes": true, "backend/tests/test_api/test_electrical_routes_edge_cases.py::TestCableRouteEdgeCases::test_create_cable_route_extreme_lengths": true, "backend/tests/test_api/test_electrical_routes_edge_cases.py::TestElectricalCalculationEdgeCases::test_cable_sizing_calculation_extreme_values": true, "backend/tests/test_integration/test_electrical_integration_enhanced.py::TestElectricalSystemIntegration::test_create_complete_electrical_system": true, "backend/tests/test_integration/test_electrical_integration_enhanced.py::TestElectricalSystemIntegration::test_electrical_calculations_integration": true, "backend/tests/test_integration/test_electrical_integration_enhanced.py::TestElectricalSystemIntegration::test_electrical_system_validation_workflow": true, "backend/tests/test_integration/test_electrical_integration_enhanced.py::TestElectricalSystemIntegration::test_electrical_load_analysis_integration": true, "backend/tests/test_integration/test_electrical_integration_enhanced.py::TestCrossModuleIntegration::test_electrical_component_integration": true, "backend/tests/test_integration/test_electrical_integration_enhanced.py::TestCrossModuleIntegration::test_project_electrical_integration": true, "backend/tests/test_integration/test_minimal_integration.py::TestMinimalIntegration::test_switchboard_schema_with_enum": true, "backend/tests/test_integration/test_minimal_integration.py::TestMinimalIntegration::test_user_schema_validation": true, "backend/tests/test_integration/test_minimal_integration.py::TestMinimalIntegration::test_schema_validation_errors": true, "backend/tests/test_integration/test_minimal_integration.py::TestMinimalIntegration::test_enum_values": true, "backend/tests/test_integration/test_minimal_integration.py::TestMinimalIntegration::test_schema_serialization": true, "backend/tests/test_integration/test_minimal_integration.py::TestMinimalIntegration::test_optional_fields": true, "backend/tests/test_integration/test_minimal_integration.py::TestMinimalIntegration::test_default_values": true, "backend/tests/test_integration/test_simple_document_integration.py::TestDocumentIntegration::test_calculation_standard_repository_crud": true, "backend/tests/test_integration/test_simple_document_integration.py::TestDocumentIntegration::test_calculation_standard_service_operations": true, "backend/tests/test_integration/test_simple_document_integration.py::TestDocumentIntegration::test_calculation_standard_schema_validation": true, "backend/tests/test_integration/test_simple_document_integration.py::TestDocumentIntegration::test_database_schema_creation": true, "backend/tests/test_integration/test_simple_document_integration.py::TestDocumentIntegration::test_document_models_relationships": true, "backend/tests/test_performance/test_api_performance.py::TestAPIPerformance::test_electrical_node_creation_performance": true, "backend/tests/test_performance/test_api_performance.py::TestAPIPerformance::test_electrical_node_list_performance": true, "backend/tests/test_performance/test_api_performance.py::TestAPIPerformance::test_component_search_performance": true, "backend/tests/test_performance/test_api_performance.py::TestAPIPerformance::test_cable_sizing_calculation_performance": true, "backend/tests/test_performance/test_api_performance.py::TestAPIPerformance::test_concurrent_request_performance[5]": true, "backend/tests/test_performance/test_api_performance.py::TestAPIPerformance::test_concurrent_request_performance[10]": true, "backend/tests/test_performance/test_api_performance.py::TestAPIPerformance::test_concurrent_request_performance[20]": true, "backend/tests/test_performance/test_api_performance.py::TestAPIPerformance::test_memory_usage_during_large_operations": true, "backend/tests/test_performance/test_api_performance.py::TestDatabasePerformance::test_bulk_insert_performance": true, "backend/tests/test_performance/test_api_performance.py::TestDatabasePerformance::test_complex_query_performance": true, "backend/tests/test_performance/test_load_testing.py::TestAdvancedLoadTesting::test_electrical_nodes_concurrent_creation": true, "backend/tests/test_performance/test_load_testing.py::TestAdvancedLoadTesting::test_component_search_load": true, "backend/tests/test_performance/test_load_testing.py::TestAdvancedLoadTesting::test_calculation_engine_stress": true, "backend/tests/test_reports/test_document_generator.py::TestDocumentGenerator::test_initialization": true, "backend/tests/test_reports/test_document_generator.py::TestDocumentGenerator::test_generate_pdf_report_valid_input": true, "backend/tests/test_reports/test_document_generator.py::TestDocumentGenerator::test_generate_html_report_valid_input": true, "backend/tests/test_reports/test_document_generator.py::TestDocumentGenerator::test_generate_excel_report_valid_input": true, "backend/tests/test_reports/test_document_generator.py::TestDocumentGenerator::test_generate_cable_schedule_report": true, "backend/tests/test_reports/test_document_generator.py::TestDocumentGenerator::test_generate_dashboard_report": true, "backend/tests/test_reports/test_document_generator.py::TestDocumentGenerator::test_generate_multi_format_report_package": true, "backend/tests/test_reports/test_document_generator.py::TestDocumentGenerator::test_generate_report_with_custom_template": true, "backend/tests/test_reports/test_document_generator.py::TestDocumentGenerator::test_generate_report_invalid_output_format": true, "backend/tests/test_reports/test_document_generator.py::TestDocumentGenerator::test_generate_report_missing_required_data": true, "backend/tests/test_reports/test_document_generator.py::TestDocumentGenerator::test_validate_report_data_valid": true, "backend/tests/test_reports/test_document_generator.py::TestDocumentGenerator::test_validate_report_data_invalid": true, "backend/tests/test_reports/test_document_generator.py::TestDocumentGenerator::test_generate_report_with_charts": true, "backend/tests/test_reports/test_document_generator.py::TestDocumentGenerator::test_generate_report_with_images": true, "backend/tests/test_reports/test_document_generator.py::TestDocumentGenerator::test_generate_report_performance": true, "backend/tests/test_reports/test_document_generator.py::TestDocumentGenerator::test_get_available_templates": true, "backend/tests/test_reports/test_document_generator.py::TestDocumentGenerator::test_generate_report_with_watermark": true, "backend/tests/test_reports/test_document_generator.py::TestDocumentGenerator::test_logging_functionality": true, "backend/tests/test_reports/test_report_service.py::TestReportService::test_initialization": true, "backend/tests/test_reports/test_report_service.py::TestReportService::test_generate_calculation_report_valid_input": true, "backend/tests/test_reports/test_report_service.py::TestReportService::test_generate_calculation_report_html_format": true, "backend/tests/test_reports/test_report_service.py::TestReportService::test_generate_calculation_report_excel_format": true, "backend/tests/test_reports/test_report_service.py::TestReportService::test_generate_cable_schedule_valid_input": true, "backend/tests/test_reports/test_report_service.py::TestReportService::test_generate_dashboard_valid_input": true, "backend/tests/test_reports/test_report_service.py::TestReportService::test_generate_report_package_valid_input": true, "backend/tests/test_reports/test_report_service.py::TestReportService::test_export_data_csv_format": true, "backend/tests/test_reports/test_report_service.py::TestReportService::test_export_data_json_format": true, "backend/tests/test_reports/test_report_service.py::TestReportService::test_export_data_excel_format": true, "backend/tests/test_reports/test_report_service.py::TestReportService::test_generate_calculation_report_with_caching": true, "backend/tests/test_reports/test_report_service.py::TestReportService::test_generate_calculation_report_invalid_format": true, "backend/tests/test_reports/test_report_service.py::TestReportService::test_generate_calculation_report_missing_data": true, "backend/tests/test_reports/test_report_service.py::TestReportService::test_export_data_invalid_format": true, "backend/tests/test_reports/test_report_service.py::TestReportService::test_export_data_with_filters": true, "backend/tests/test_reports/test_report_service.py::TestReportService::test_get_generation_history": true, "backend/tests/test_reports/test_report_service.py::TestReportService::test_get_generation_history_filtered": true, "backend/tests/test_reports/test_report_service.py::TestReportService::test_clear_cache_all": true, "backend/tests/test_reports/test_report_service.py::TestReportService::test_clear_cache_specific_project": true, "backend/tests/test_reports/test_report_service.py::TestReportService::test_get_available_templates": true, "backend/tests/test_reports/test_report_service.py::TestReportService::test_generate_report_performance": true, "backend/tests/test_reports/test_report_service.py::TestReportService::test_logging_functionality": true, "backend/tests/test_repositories/test_activity_log_repository.py::TestActivityLogRepository::test_repository_initialization": true, "backend/tests/test_repositories/test_activity_log_repository.py::TestActivityLogRepository::test_get_by_user_id": true, "backend/tests/test_repositories/test_activity_log_repository.py::TestActivityLogRepository::test_get_by_entity": true, "backend/tests/test_repositories/test_activity_log_repository.py::TestActivityLogRepository::test_get_by_event_type": true, "backend/tests/test_repositories/test_activity_log_repository.py::TestActivityLogRepository::test_get_by_date_range": true, "backend/tests/test_repositories/test_activity_log_repository.py::TestActivityLogRepository::test_search_by_details": true, "backend/tests/test_repositories/test_activity_log_repository.py::TestActivityLogRepository::test_filter_activity_logs": true, "backend/tests/test_repositories/test_activity_log_repository.py::TestActivityLogRepository::test_count_filtered_activity_logs": true, "backend/tests/test_repositories/test_activity_log_repository.py::TestActivityLogRepository::test_get_security_events": true, "backend/tests/test_repositories/test_activity_log_repository.py::TestActivityLogRepository::test_get_user_activity_summary": true, "backend/tests/test_repositories/test_activity_log_repository.py::TestActivityLogRepository::test_get_daily_activity_counts": true, "backend/tests/test_repositories/test_activity_log_repository.py::TestActivityLogRepository::test_get_recent_activity": true, "backend/tests/test_repositories/test_activity_log_repository.py::TestActivityLogRepository::test_delete_old_logs": true, "backend/tests/test_repositories/test_activity_log_repository.py::TestActivityLogRepository::test_get_unique_users_count": true, "backend/tests/test_repositories/test_activity_log_repository.py::TestActivityLogRepository::test_get_unique_entities_count": true, "backend/tests/test_repositories/test_activity_log_repository.py::TestActivityLogRepository::test_database_error_handling": true, "backend/tests/test_repositories/test_activity_log_repository.py::TestActivityLogRepository::test_empty_results": true, "backend/tests/test_repositories/test_document_repository.py::TestImportedDataRevisionRepository::test_create_imported_data_revision": true, "backend/tests/test_repositories/test_document_repository.py::TestImportedDataRevisionRepository::test_get_by_project_id": true, "backend/tests/test_repositories/test_document_repository.py::TestImportedDataRevisionRepository::test_get_active_revision_by_filename": true, "backend/tests/test_repositories/test_document_repository.py::TestImportedDataRevisionRepository::test_deactivate_other_revisions": true, "backend/tests/test_repositories/test_document_repository.py::TestExportedDocumentRepository::test_create_exported_document": true, "backend/tests/test_repositories/test_document_repository.py::TestExportedDocumentRepository::test_get_latest_by_document_type": true, "backend/tests/test_repositories/test_document_repository.py::TestExportedDocumentRepository::test_mark_others_as_not_latest": true, "backend/tests/test_repositories/test_document_repository.py::TestCalculationStandardRepository::test_create_calculation_standard": true, "backend/tests/test_repositories/test_document_repository.py::TestCalculationStandardRepository::test_get_by_standard_code": true, "backend/tests/test_repositories/test_document_repository.py::TestCalculationStandardRepository::test_get_by_name": true, "backend/tests/test_repositories/test_document_repository.py::TestCalculationStandardRepository::test_search_by_name_or_code": true, "backend/tests/test_schemas/test_activity_log_schemas.py::TestEventEnums::test_event_type_enum_values": true, "backend/tests/test_schemas/test_activity_log_schemas.py::TestEventEnums::test_entity_type_enum_values": true, "backend/tests/test_schemas/test_activity_log_schemas.py::TestEventEnums::test_event_category_enum_values": true, "backend/tests/test_schemas/test_activity_log_schemas.py::TestActivityLogBaseSchema::test_valid_activity_log_base": true, "backend/tests/test_schemas/test_activity_log_schemas.py::TestActivityLogBaseSchema::test_minimal_activity_log_base": true, "backend/tests/test_schemas/test_activity_log_schemas.py::TestActivityLogBaseSchema::test_json_details_validation": true, "backend/tests/test_schemas/test_activity_log_schemas.py::TestActivityLogBaseSchema::test_invalid_json_details_validation": true, "backend/tests/test_schemas/test_activity_log_schemas.py::TestActivityLogBaseSchema::test_entity_consistency_validation": true, "backend/tests/test_schemas/test_activity_log_schemas.py::TestActivityLogCreateSchema::test_valid_activity_log_create": true, "backend/tests/test_schemas/test_activity_log_schemas.py::TestActivityLogCreateSchema::test_system_event_without_user": true, "backend/tests/test_schemas/test_activity_log_schemas.py::TestActivityLogFilterSchema::test_valid_filter_schema": true, "backend/tests/test_schemas/test_activity_log_schemas.py::TestActivityLogFilterSchema::test_date_range_validation": true, "backend/tests/test_schemas/test_activity_log_schemas.py::TestActivityLogFilterSchema::test_minimal_filter_schema": true, "backend/tests/test_schemas/test_activity_log_schemas.py::TestAuditReportRequestSchema::test_valid_audit_report_request": true, "backend/tests/test_schemas/test_activity_log_schemas.py::TestAuditReportRequestSchema::test_invalid_report_type": true, "backend/tests/test_schemas/test_activity_log_schemas.py::TestAuditReportRequestSchema::test_date_range_too_large": true, "backend/tests/test_schemas/test_activity_log_schemas.py::TestSecurityEventSchema::test_valid_security_event": true, "backend/tests/test_schemas/test_activity_log_schemas.py::TestSecurityEventSchema::test_invalid_severity": true, "backend/tests/test_schemas/test_activity_log_schemas.py::TestSecurityEventSchema::test_invalid_threat_level": true, "backend/tests/test_schemas/test_activity_log_schemas.py::TestEventCategoryMappingSchema::test_event_category_mapping": true, "backend/tests/test_schemas/test_switchboard_schemas.py::TestSwitchboardSchemas::test_switchboard_create_schema_valid": true, "backend/tests/test_schemas/test_switchboard_schemas.py::TestSwitchboardSchemas::test_switchboard_create_schema_invalid_voltage": true, "backend/tests/test_schemas/test_switchboard_schemas.py::TestSwitchboardSchemas::test_switchboard_create_schema_invalid_phases": true, "backend/tests/test_schemas/test_switchboard_schemas.py::TestSwitchboardSchemas::test_switchboard_create_schema_empty_name": true, "backend/tests/test_schemas/test_switchboard_schemas.py::TestSwitchboardSchemas::test_switchboard_update_schema_partial": true, "backend/tests/test_schemas/test_switchboard_schemas.py::TestSwitchboardSchemas::test_feeder_create_schema_valid": true, "backend/tests/test_schemas/test_switchboard_schemas.py::TestSwitchboardSchemas::test_feeder_create_schema_empty_name": true, "backend/tests/test_schemas/test_switchboard_schemas.py::TestSwitchboardSchemas::test_switchboard_component_create_schema_valid": true, "backend/tests/test_schemas/test_switchboard_schemas.py::TestSwitchboardSchemas::test_switchboard_component_create_schema_invalid_quantity": true, "backend/tests/test_schemas/test_switchboard_schemas.py::TestSwitchboardSchemas::test_feeder_component_create_schema_valid": true, "backend/tests/test_schemas/test_switchboard_schemas.py::TestSwitchboardSchemas::test_switchboard_load_summary_schema_valid": true, "backend/tests/test_schemas/test_switchboard_schemas.py::TestSwitchboardSchemas::test_switchboard_capacity_analysis_schema_valid": true, "backend/tests/test_schemas/test_switchboard_schemas.py::TestSwitchboardSchemas::test_switchboard_capacity_analysis_schema_overloaded": true, "backend/tests/test_schemas/test_switchboard_schemas.py::TestSwitchboardSchemaEdgeCases::test_switchboard_minimum_voltage": true, "backend/tests/test_schemas/test_switchboard_schemas.py::TestSwitchboardSchemaEdgeCases::test_switchboard_maximum_voltage": true, "backend/tests/test_schemas/test_switchboard_schemas.py::TestSwitchboardSchemaEdgeCases::test_component_maximum_quantity": true, "backend/tests/test_schemas/test_switchboard_schemas.py::TestSwitchboardSchemaEdgeCases::test_position_whitespace_handling": true, "backend/tests/test_schemas/test_switchboard_schemas.py::TestSwitchboardSchemaEdgeCases::test_optional_fields_none": true, "backend/tests/test_schemas/test_user_schemas.py::TestUserSchemas::test_user_create_schema_valid": true, "backend/tests/test_schemas/test_user_schemas.py::TestUserSchemas::test_user_create_schema_password_validation": true, "backend/tests/test_schemas/test_user_schemas.py::TestUserSchemas::test_user_create_schema_password_no_uppercase": true, "backend/tests/test_schemas/test_user_schemas.py::TestUserSchemas::test_user_create_schema_password_no_digit": true, "backend/tests/test_schemas/test_user_schemas.py::TestUserSchemas::test_user_create_schema_empty_name": true, "backend/tests/test_schemas/test_user_schemas.py::TestUserSchemas::test_user_create_schema_invalid_email": true, "backend/tests/test_schemas/test_user_schemas.py::TestUserSchemas::test_user_update_schema_partial": true, "backend/tests/test_schemas/test_user_schemas.py::TestUserSchemas::test_login_request_schema_valid": true, "backend/tests/test_schemas/test_user_schemas.py::TestUserSchemas::test_login_request_schema_invalid_email": true, "backend/tests/test_schemas/test_user_schemas.py::TestUserSchemas::test_password_change_request_schema_valid": true, "backend/tests/test_schemas/test_user_schemas.py::TestUserSchemas::test_password_change_request_schema_weak_new_password": true, "backend/tests/test_schemas/test_user_schemas.py::TestUserSchemas::test_password_reset_confirm_schema_valid": true, "backend/tests/test_schemas/test_user_schemas.py::TestUserPreferenceSchemas::test_user_preference_create_schema_valid": true, "backend/tests/test_schemas/test_user_schemas.py::TestUserPreferenceSchemas::test_user_preference_create_schema_invalid_theme": true, "backend/tests/test_schemas/test_user_schemas.py::TestUserPreferenceSchemas::test_user_preference_create_schema_invalid_temperature_range": true, "backend/tests/test_schemas/test_user_schemas.py::TestUserPreferenceSchemas::test_user_preference_create_schema_temperature_boundaries": true, "backend/tests/test_schemas/test_user_schemas.py::TestUserPreferenceSchemas::test_user_preference_create_schema_invalid_safety_margin": true, "backend/tests/test_schemas/test_user_schemas.py::TestUserPreferenceSchemas::test_user_preference_update_schema_partial": true, "backend/tests/test_schemas/test_user_schemas.py::TestUserSchemaEdgeCases::test_user_name_whitespace_handling": true, "backend/tests/test_schemas/test_user_schemas.py::TestUserSchemaEdgeCases::test_user_preference_theme_case_sensitivity": true, "backend/tests/test_schemas/test_user_schemas.py::TestUserSchemaEdgeCases::test_user_preference_equal_temperatures": true, "backend/tests/test_schemas/test_user_schemas.py::TestUserSchemaEdgeCases::test_user_optional_email": true, "backend/tests/test_schemas/test_user_schemas.py::TestUserSchemaEdgeCases::test_password_minimum_length": true, "backend/tests/test_security/test_security_validation.py::TestInputValidationSecurity::test_sql_injection_protection": true, "backend/tests/test_security/test_security_validation.py::TestInputValidationSecurity::test_xss_protection": true, "backend/tests/test_security/test_security_validation.py::TestInputValidationSecurity::test_path_traversal_protection": true, "backend/tests/test_security/test_security_validation.py::TestInputValidationSecurity::test_command_injection_protection": true, "backend/tests/test_security/test_security_validation.py::TestAuthenticationSecurity::test_unauthorized_access_protection": true, "backend/tests/test_security/test_security_validation.py::TestAuthenticationSecurity::test_jwt_token_validation": true, "backend/tests/test_security/test_security_validation.py::TestAuthenticationSecurity::test_session_security": true, "backend/tests/test_security/test_security_validation.py::TestDataValidationSecurity::test_json_payload_size_limits": true, "backend/tests/test_security/test_security_validation.py::TestDataValidationSecurity::test_nested_json_depth_limits": true, "backend/tests/test_security/test_security_validation.py::TestDataValidationSecurity::test_unicode_security": true, "backend/tests/test_security/test_security_validation.py::TestRateLimitingSecurity::test_api_rate_limiting": true, "backend/tests/test_security/test_security_validation.py::TestRateLimitingSecurity::test_concurrent_request_limits": true, "backend/tests/test_services/test_activity_log_service.py::TestActivityLogService::test_service_initialization": true, "backend/tests/test_services/test_activity_log_service.py::TestActivityLogService::test_log_event_success": true, "backend/tests/test_services/test_activity_log_service.py::TestActivityLogService::test_log_event_system_event": true, "backend/tests/test_services/test_activity_log_service.py::TestActivityLogService::test_log_event_invalid_user": true, "backend/tests/test_services/test_activity_log_service.py::TestActivityLogService::test_log_user_action_success": true, "backend/tests/test_services/test_activity_log_service.py::TestActivityLogService::test_log_user_action_invalid_action": true, "backend/tests/test_services/test_activity_log_service.py::TestActivityLogService::test_log_user_action_invalid_entity_type": true, "backend/tests/test_services/test_activity_log_service.py::TestActivityLogService::test_log_authentication_event_success": true, "backend/tests/test_services/test_activity_log_service.py::TestActivityLogService::test_log_authentication_event_failed_login": true, "backend/tests/test_services/test_activity_log_service.py::TestActivityLogService::test_log_system_event_success": true, "backend/tests/test_services/test_activity_log_service.py::TestActivityLogService::test_get_activity_log_success": true, "backend/tests/test_services/test_activity_log_service.py::TestActivityLogService::test_get_activity_log_not_found": true, "backend/tests/test_services/test_activity_log_service.py::TestActivityLogService::test_get_user_activity_logs_success": true, "backend/tests/test_services/test_activity_log_service.py::TestActivityLogService::test_get_user_activity_logs_user_not_found": true, "backend/tests/test_services/test_activity_log_service.py::TestActivityLogService::test_log_security_event_success": true, "backend/tests/test_services/test_activity_log_service.py::TestActivityLogService::test_log_security_event_invalid_severity": true, "backend/tests/test_services/test_activity_log_service.py::TestActivityLogService::test_log_security_event_invalid_threat_level": true, "backend/tests/test_services/test_activity_log_service.py::TestActivityLogService::test_update_activity_log_success": true, "backend/tests/test_services/test_activity_log_service.py::TestActivityLogService::test_update_activity_log_not_found": true, "backend/tests/test_services/test_activity_log_service.py::TestActivityLogService::test_delete_old_activity_logs_success": true, "backend/tests/test_services/test_activity_log_service.py::TestActivityLogService::test_delete_old_activity_logs_invalid_days": true, "backend/tests/test_services/test_activity_log_service.py::TestActivityLogService::test_generate_audit_report_success": true, "backend/tests/test_services/test_activity_log_service.py::TestActivityLogService::test_get_user_activity_summary_success": true, "backend/tests/test_services/test_activity_log_service.py::TestActivityLogService::test_get_user_activity_summary_invalid_date_range": true, "backend/tests/test_services/test_activity_log_service.py::TestActivityLogService::test_database_error_handling": true, "backend/tests/test_services/test_activity_log_service.py::TestActivityLogService::test_transaction_rollback_on_error": true, "backend/tests/test_standards/test_ieee_515.py::TestIEEE515::test_initialization": true, "backend/tests/test_standards/test_ieee_515.py::TestIEEE515::test_validate_design_valid_input": true, "backend/tests/test_standards/test_ieee_515.py::TestIEEE515::test_validate_design_voltage_limits": true, "backend/tests/test_standards/test_ieee_515.py::TestIEEE515::test_validate_design_power_density_limits": true, "backend/tests/test_standards/test_ieee_515.py::TestIEEE515::test_validate_design_temperature_limits": true, "backend/tests/test_standards/test_ieee_515.py::TestIEEE515::test_validate_design_hazardous_area": true, "backend/tests/test_standards/test_ieee_515.py::TestIEEE515::test_validate_design_missing_required_fields": true, "backend/tests/test_standards/test_ieee_515.py::TestIEEE515::test_get_applicable_rules_standard_application": true, "backend/tests/test_standards/test_ieee_515.py::TestIEEE515::test_get_applicable_rules_hazardous_area": true, "backend/tests/test_standards/test_ieee_515.py::TestIEEE515::test_get_applicable_rules_outdoor_installation": true, "backend/tests/test_standards/test_ieee_515.py::TestIEEE515::test_calculate_parameters_power_requirements": true, "backend/tests/test_standards/test_ieee_515.py::TestIEEE515::test_calculate_parameters_electrical_parameters": true, "backend/tests/test_standards/test_ieee_515.py::TestIEEE515::test_calculate_parameters_temperature_parameters": true, "backend/tests/test_standards/test_ieee_515.py::TestIEEE515::test_calculate_parameters_cable_selection": true, "backend/tests/test_standards/test_ieee_515.py::TestIEEE515::test_calculate_parameters_missing_input": true, "backend/tests/test_standards/test_ieee_515.py::TestIEEE515::test_voltage_validation_standard_voltages": true, "backend/tests/test_standards/test_ieee_515.py::TestIEEE515::test_voltage_validation_non_standard_voltages": true, "backend/tests/test_standards/test_ieee_515.py::TestIEEE515::test_power_density_recommendations": true, "backend/tests/test_standards/test_ieee_515.py::TestIEEE515::test_safety_requirements_validation": true, "backend/tests/test_standards/test_ieee_515.py::TestIEEE515::test_installation_requirements_validation": true, "backend/tests/test_standards/test_ieee_515.py::TestIEEE515::test_standard_info": true, "backend/tests/test_standards/test_ieee_515.py::TestIEEE515::test_get_all_rules": true, "backend/tests/test_standards/test_ieee_515.py::TestIEEE515::test_get_all_parameters": true, "backend/tests/test_standards/test_ieee_515.py::TestIEEE515::test_get_specific_rule": true, "backend/tests/test_standards/test_ieee_515.py::TestIEEE515::test_get_specific_parameter": true, "backend/tests/test_standards/test_ieee_515.py::TestIEEE515::test_logging_functionality": true, "backend/tests/test_standards/test_standards_service.py::TestStandardsService::test_initialization": true, "backend/tests/test_standards/test_standards_service.py::TestStandardsService::test_get_available_standards": true, "backend/tests/test_standards/test_standards_service.py::TestStandardsService::test_validate_design_against_single_standard": true, "backend/tests/test_standards/test_standards_service.py::TestStandardsService::test_validate_design_against_multiple_standards": true, "backend/tests/test_standards/test_standards_service.py::TestStandardsService::test_validate_design_against_all_standards": true, "backend/tests/test_standards/test_standards_service.py::TestStandardsService::test_validate_design_with_validation_options": true, "backend/tests/test_standards/test_standards_service.py::TestStandardsService::test_calculate_standards_parameters_single_standard": true, "backend/tests/test_standards/test_standards_service.py::TestStandardsService::test_calculate_standards_parameters_multiple_standards": true, "backend/tests/test_standards/test_standards_service.py::TestStandardsService::test_calculate_standards_parameters_with_options": true, "backend/tests/test_standards/test_standards_service.py::TestStandardsService::test_get_standards_recommendations_heat_tracing": true, "backend/tests/test_standards/test_standards_service.py::TestStandardsService::test_get_standards_recommendations_hazardous_area": true, "backend/tests/test_standards/test_standards_service.py::TestStandardsService::test_get_standards_recommendations_offshore": true, "backend/tests/test_standards/test_standards_service.py::TestStandardsService::test_get_compliance_summary": true, "backend/tests/test_standards/test_standards_service.py::TestStandardsService::test_get_compliance_summary_filtered": true, "backend/tests/test_standards/test_standards_service.py::TestStandardsService::test_validate_design_invalid_input": true, "backend/tests/test_standards/test_standards_service.py::TestStandardsService::test_validate_design_unknown_standard": true, "backend/tests/test_standards/test_standards_service.py::TestStandardsService::test_calculate_parameters_invalid_input": true, "backend/tests/test_standards/test_standards_service.py::TestStandardsService::test_cross_standard_conflict_detection": true, "backend/tests/test_standards/test_standards_service.py::TestStandardsService::test_consolidated_parameters": true, "backend/tests/test_standards/test_standards_service.py::TestStandardsService::test_parameter_comparisons": true, "backend/tests/test_standards/test_standards_service.py::TestStandardsService::test_design_recommendations_generation": true, "backend/tests/test_standards/test_standards_service.py::TestStandardsService::test_performance_with_multiple_standards": true, "backend/tests/test_standards/test_standards_service.py::TestStandardsService::test_logging_functionality": true, "backend/tests/test_api/test_calculations_routes.py::TestCalculationsRoutes::test_calculate_heat_tracing_valid_request": true, "backend/tests/test_api/test_calculations_routes.py::TestCalculationsRoutes::test_calculate_heat_tracing_invalid_request": true, "backend/tests/test_api/test_calculations_routes.py::TestCalculationsRoutes::test_calculate_heat_tracing_missing_auth": true, "backend/tests/test_api/test_calculations_routes.py::TestCalculationsRoutes::test_calculate_electrical_load_valid_request": true, "backend/tests/test_api/test_calculations_routes.py::TestCalculationsRoutes::test_calculate_power_requirements_valid_request": true, "backend/tests/test_api/test_calculations_routes.py::TestCalculationsRoutes::test_calculate_cable_selection_valid_request": true, "backend/tests/test_api/test_calculations_routes.py::TestCalculationsRoutes::test_get_calculation_history": true, "backend/tests/test_api/test_calculations_routes.py::TestCalculationsRoutes::test_get_calculation_history_filtered": true, "backend/tests/test_api/test_calculations_routes.py::TestCalculationsRoutes::test_validate_calculation_input": true, "backend/tests/test_api/test_calculations_routes.py::TestCalculationsRoutes::test_get_calculation_templates": true, "backend/tests/test_api/test_calculations_routes.py::TestCalculationsRoutes::test_get_calculation_template_specific": true, "backend/tests/test_api/test_calculations_routes.py::TestCalculationsRoutes::test_calculations_health_check": true, "backend/tests/test_api/test_calculations_routes.py::TestCalculationsRoutes::test_calculations_routes_without_auth": true, "backend/tests/test_api/test_calculations_routes.py::TestCalculationsRoutes::test_calculate_heat_tracing_large_dataset": true, "backend/tests/test_api/test_calculations_routes.py::TestCalculationsRoutes::test_calculate_with_performance_monitoring": true, "backend/tests/test_api/test_calculations_routes.py::TestCalculationsRoutes::test_error_handling_invalid_json": true, "backend/tests/test_api/test_calculations_routes.py::TestCalculationsRoutes::test_error_handling_missing_fields": true, "backend/tests/test_api/test_standards_routes.py::TestStandardsRoutes::test_get_available_standards": true, "backend/tests/test_api/test_standards_routes.py::TestStandardsRoutes::test_get_standards_recommendations": true, "backend/tests/test_api/test_standards_routes.py::TestStandardsRoutes::test_validate_design_against_standards": true, "backend/tests/test_api/test_standards_routes.py::TestStandardsRoutes::test_validate_against_specific_standard": true, "backend/tests/test_api/test_standards_routes.py::TestStandardsRoutes::test_calculate_standards_parameters": true, "backend/tests/test_api/test_standards_routes.py::TestStandardsRoutes::test_calculate_with_specific_standard": true, "backend/tests/test_api/test_standards_routes.py::TestStandardsRoutes::test_get_compliance_summary": true, "backend/tests/test_api/test_standards_routes.py::TestStandardsRoutes::test_get_compliance_summary_filtered": true, "backend/tests/test_api/test_standards_routes.py::TestStandardsRoutes::test_get_standard_information": true, "backend/tests/test_api/test_standards_routes.py::TestStandardsRoutes::test_get_standard_information_not_found": true, "backend/tests/test_api/test_standards_routes.py::TestStandardsRoutes::test_standards_health_check": true, "backend/tests/test_api/test_standards_routes.py::TestStandardsRoutes::test_validate_design_invalid_input": true, "backend/tests/test_api/test_standards_routes.py::TestStandardsRoutes::test_validate_design_unknown_standard": true, "backend/tests/test_api/test_standards_routes.py::TestStandardsRoutes::test_calculate_parameters_invalid_input": true, "backend/tests/test_api/test_standards_routes.py::TestStandardsRoutes::test_validate_design_multiple_standards": true, "backend/tests/test_api/test_standards_routes.py::TestStandardsRoutes::test_calculate_parameters_multiple_standards": true, "backend/tests/test_api/test_standards_routes.py::TestStandardsRoutes::test_get_recommendations_hazardous_area": true, "backend/tests/test_api/test_standards_routes.py::TestStandardsRoutes::test_get_recommendations_offshore": true, "backend/tests/test_api/test_standards_routes.py::TestStandardsRoutes::test_standards_routes_without_auth": true, "backend/tests/test_api/test_standards_routes.py::TestStandardsRoutes::test_error_handling_invalid_json": true, "backend/tests/test_api/test_standards_routes.py::TestStandardsRoutes::test_performance_multiple_standards_validation": true, "backend/tests/test_calculations/test_heat_loss_calculator.py::TestHeatLossCalculator::test_initialization": true, "backend/tests/test_calculations/test_heat_loss_calculator.py::TestHeatLossCalculator::test_calculate_thermal_resistance_valid_input": true, "backend/tests/test_calculations/test_heat_loss_calculator.py::TestHeatLossCalculator::test_calculate_thermal_resistance_invalid_diameter": true, "backend/tests/test_calculations/test_heat_loss_calculator.py::TestHeatLossCalculator::test_calculate_thermal_resistance_zero_thickness": true, "backend/tests/test_calculations/test_heat_loss_calculator.py::TestHeatLossCalculator::test_calculate_heat_loss_valid_input": true, "backend/tests/test_calculations/test_heat_loss_calculator.py::TestHeatLossCalculator::test_calculate_heat_loss_zero_temperature_difference": true, "backend/tests/test_calculations/test_heat_loss_calculator.py::TestHeatLossCalculator::test_calculate_heat_loss_missing_required_fields": true, "backend/tests/test_calculations/test_heat_loss_calculator.py::TestHeatLossCalculator::test_calculate_convection_coefficient": true, "backend/tests/test_calculations/test_heat_loss_calculator.py::TestHeatLossCalculator::test_calculate_radiation_coefficient": true, "backend/tests/test_calculations/test_heat_loss_calculator.py::TestHeatLossCalculator::test_calculate_heat_loss_with_radiation": true, "backend/tests/test_calculations/test_heat_loss_calculator.py::TestHeatLossCalculator::test_calculate_heat_loss_multiple_layers": true, "backend/tests/test_calculations/test_heat_loss_calculator.py::TestHeatLossCalculator::test_calculate_heat_loss_with_safety_factors": true, "backend/tests/test_calculations/test_heat_loss_calculator.py::TestHeatLossCalculator::test_calculate_heat_loss_performance": true, "backend/tests/test_calculations/test_heat_loss_calculator.py::TestHeatLossCalculator::test_calculate_heat_loss_edge_cases": true, "backend/tests/test_calculations/test_heat_loss_calculator.py::TestHeatLossCalculator::test_calculate_heat_loss_validation_errors": true, "backend/tests/test_calculations/test_heat_loss_calculator.py::TestHeatLossCalculator::test_logging_functionality": true, "backend/tests/test_calculations/test_power_calculator.py::TestPowerCalculator::test_initialization": true, "backend/tests/test_calculations/test_power_calculator.py::TestPowerCalculator::test_calculate_power_requirements_valid_input": true, "backend/tests/test_calculations/test_power_calculator.py::TestPowerCalculator::test_calculate_power_requirements_different_applications": true, "backend/tests/test_calculations/test_power_calculator.py::TestPowerCalculator::test_calculate_electrical_parameters": true, "backend/tests/test_calculations/test_power_calculator.py::TestPowerCalculator::test_calculate_electrical_parameters_three_phase": true, "backend/tests/test_calculations/test_power_calculator.py::TestPowerCalculator::test_select_cable_self_regulating": true, "backend/tests/test_calculations/test_power_calculator.py::TestPowerCalculator::test_select_cable_constant_wattage": true, "backend/tests/test_calculations/test_power_calculator.py::TestPowerCalculator::test_calculate_cable_derating": true, "backend/tests/test_calculations/test_power_calculator.py::TestPowerCalculator::test_calculate_voltage_drop": true, "backend/tests/test_calculations/test_power_calculator.py::TestPowerCalculator::test_calculate_circuit_protection": true, "backend/tests/test_calculations/test_power_calculator.py::TestPowerCalculator::test_calculate_power_requirements_invalid_input": true, "backend/tests/test_calculations/test_power_calculator.py::TestPowerCalculator::test_calculate_power_requirements_zero_length": true, "backend/tests/test_calculations/test_power_calculator.py::TestPowerCalculator::test_calculate_electrical_parameters_invalid_voltage": true, "backend/tests/test_calculations/test_power_calculator.py::TestPowerCalculator::test_calculate_electrical_parameters_invalid_power_factor": true, "backend/tests/test_calculations/test_power_calculator.py::TestPowerCalculator::test_select_cable_missing_data": true, "backend/tests/test_calculations/test_power_calculator.py::TestPowerCalculator::test_calculate_power_requirements_with_diversity": true, "backend/tests/test_calculations/test_power_calculator.py::TestPowerCalculator::test_calculate_startup_requirements": true, "backend/tests/test_calculations/test_power_calculator.py::TestPowerCalculator::test_calculate_energy_consumption": true, "backend/tests/test_calculations/test_power_calculator.py::TestPowerCalculator::test_logging_functionality": true, "backend/tests/test_import_export/test_csv_parser.py::TestCSVParser::test_initialization": true, "backend/tests/test_import_export/test_csv_parser.py::TestCSVParser::test_parse_valid_csv_file": true, "backend/tests/test_import_export/test_csv_parser.py::TestCSVParser::test_parse_csv_with_custom_delimiter": true, "backend/tests/test_import_export/test_csv_parser.py::TestCSVParser::test_parse_csv_with_missing_headers": true, "backend/tests/test_import_export/test_csv_parser.py::TestCSVParser::test_parse_csv_with_empty_rows": true, "backend/tests/test_import_export/test_csv_parser.py::TestCSVParser::test_parse_csv_with_data_type_conversion": true, "backend/tests/test_import_export/test_csv_parser.py::TestCSVParser::test_parse_nonexistent_file": true, "backend/tests/test_import_export/test_csv_parser.py::TestCSVParser::test_parse_invalid_csv_file": true, "backend/tests/test_import_export/test_csv_parser.py::TestCSVParser::test_parse_empty_csv_file": true, "backend/tests/test_import_export/test_csv_parser.py::TestCSVParser::test_validate_csv_structure": true, "backend/tests/test_import_export/test_csv_parser.py::TestCSVParser::test_validate_csv_structure_missing_columns": true, "backend/tests/test_import_export/test_csv_parser.py::TestCSVParser::test_parse_csv_with_encoding_issues": true, "backend/tests/test_import_export/test_csv_parser.py::TestCSVParser::test_parse_large_csv_file": true, "backend/tests/test_import_export/test_csv_parser.py::TestCSVParser::test_parse_csv_with_special_characters": true, "backend/tests/test_import_export/test_csv_parser.py::TestCSVParser::test_parse_csv_with_validation_rules": true, "backend/tests/test_import_export/test_csv_parser.py::TestCSVParser::test_get_file_metadata": true, "backend/tests/test_import_export/test_csv_parser.py::TestCSVParser::test_logging_functionality": true, "backend/tests/test_utils_and_di_integration.py::TestUtilityIntegration::test_datetime_utils_in_service_layer": true, "backend/tests/test_utils_and_di_integration.py::TestUtilityIntegration::test_string_utils_in_service_layer": true, "backend/tests/test_utils_and_di_integration.py::TestUtilityIntegration::test_pagination_utils_in_service_layer": true, "backend/tests/test_utils_and_di_integration.py::TestUtilityIntegration::test_json_validation_utils_in_service_layer": true, "backend/tests/test_utils_and_di_integration.py::TestUtilityIntegration::test_file_io_utils_in_service_layer": true, "backend/tests/test_utils_and_di_integration.py::TestDependencyInjectionIntegration::test_repository_dependency_injection": true, "backend/tests/test_utils_and_di_integration.py::TestDependencyInjectionIntegration::test_service_dependency_injection": true, "backend/tests/test_utils_and_di_integration.py::TestDependencyInjectionIntegration::test_dependency_override_manager": true, "backend/tests/test_utils_and_di_integration.py::TestDependencyInjectionIntegration::test_enhanced_test_client": true, "backend/tests/test_utils_and_di_integration.py::TestUtilityAndDIIntegration::test_service_with_utilities_and_di": true, "backend/tests/test_utils_and_di_integration.py::TestUtilityAndDIIntegration::test_api_route_with_utilities_and_di": true, "backend/tests/test_utils_and_di_integration.py::TestUtilityMockingPatterns::test_mock_all_string_utils": true, "backend/tests/test_utils_and_di_integration.py::TestUtilityMockingPatterns::test_mock_all_datetime_utils": true, "backend/tests/test_utils_and_di_integration.py::TestUtilityMockingPatterns::test_mock_all_pagination_utils": true, "backend/tests/test_utils_and_di_integration.py::TestDIMockingPatterns::test_mock_repository_dependencies": true, "backend/tests/test_utils_and_di_integration.py::TestDIMockingPatterns::test_mock_service_dependencies": true, "backend/tests/test_api/test_project_routes.py::TestProjectRoutesWithEnhancedDI::test_create_project_with_dependency_override_manager": true, "backend/tests/test_api/test_project_routes.py::TestProjectRoutesWithEnhancedDI::test_list_projects_with_pagination_utils": true, "backend/tests/test_api/test_project_routes.py::TestProjectRoutesWithEnhancedDI::test_service_dependency_injection_chain": true, "backend/tests/test_import_export/test_csv_parser.py::TestCSVParser::test_validate_data_types": true, "backend/tests/test_import_export/test_csv_parser.py::TestCSVParser::test_validate_data_types_invalid": true, "backend/tests/test_import_export/test_csv_parser.py::TestCSVParser::test_convert_data_types": true, "backend/tests/test_import_export/test_csv_parser.py::TestCSVParser::test_get_file_info": true, "backend/tests/test_services/test_project_service.py::TestProjectServiceWithUtilities::test_create_project_with_string_sanitization": true, "backend/tests/test_services/test_project_service.py::TestProjectServiceWithUtilities::test_get_projects_with_pagination_utils": true, "backend/tests/test_services/test_project_service.py::TestProjectServiceWithUtilities::test_update_project_with_datetime_utils": true, "backend/tests/test_services/test_project_service.py::TestProjectServiceWithDependencyInjection::test_service_creation_with_di": true, "backend/tests/test_services/test_project_service.py::TestProjectServiceWithDependencyInjection::test_service_method_with_repository_di": true, "backend/tests/test_services/test_project_service.py::TestProjectServiceWithDependencyInjection::test_multiple_services_with_shared_dependencies": true, "backend/tests/test_services/test_project_service.py::TestProjectServiceIntegrationPatterns::test_complete_workflow_with_utils_and_di": true, "backend/tests/test_integration/test_end_to_end_workflow.py::TestEndToEndWorkflow::test_complete_design_workflow": true, "backend/tests/test_integration/test_end_to_end_workflow.py::TestEndToEndWorkflow::test_import_calculate_export_workflow": true, "backend/tests/test_integration/test_end_to_end_workflow.py::TestEndToEndWorkflow::test_standards_validation_calculation_workflow": true, "backend/tests/test_integration/test_end_to_end_workflow.py::TestEndToEndWorkflow::test_multi_format_report_generation_workflow": true, "backend/tests/test_integration/test_end_to_end_workflow.py::TestEndToEndWorkflow::test_error_recovery_workflow": true, "backend/tests/test_integration/test_end_to_end_workflow.py::TestEndToEndWorkflow::test_performance_large_dataset_workflow": true, "backend/tests/test_integration/test_end_to_end_workflow.py::TestEndToEndWorkflow::test_caching_across_services_workflow": true, "backend/tests/test_integration/test_end_to_end_workflow.py::TestEndToEndWorkflow::test_data_consistency_workflow": true, "backend/tests/test_integration/test_end_to_end_workflow.py::TestEndToEndWorkflow::test_service_integration_error_handling": true, "backend/tests/test_import_export/test_import_service.py::TestImportService::test_initialization": true, "backend/tests/test_import_export/test_import_service.py::TestImportService::test_import_csv_file_valid": true, "backend/tests/test_import_export/test_import_service.py::TestImportService::test_import_json_file_valid": true, "backend/tests/test_import_export/test_import_service.py::TestImportService::test_import_excel_file_valid": true, "backend/tests/test_import_export/test_import_service.py::TestImportService::test_import_nonexistent_file": true, "backend/tests/test_import_export/test_import_service.py::TestImportService::test_import_unsupported_file_type": true, "backend/tests/test_import_export/test_import_service.py::TestImportService::test_import_with_validation_errors": true, "backend/tests/test_import_export/test_import_service.py::TestImportService::test_import_with_data_transformation": true, "backend/tests/test_import_export/test_import_service.py::TestImportService::test_import_with_duplicate_detection": true, "backend/tests/test_import_export/test_import_service.py::TestImportService::test_import_large_file": true, "backend/tests/test_import_export/test_import_service.py::TestImportService::test_import_with_progress_tracking": true, "backend/tests/test_import_export/test_import_service.py::TestImportService::test_validate_import_data": true, "backend/tests/test_import_export/test_import_service.py::TestImportService::test_validate_import_data_with_errors": true, "backend/tests/test_import_export/test_import_service.py::TestImportService::test_import_with_custom_parser": true, "backend/tests/test_import_export/test_import_service.py::TestImportService::test_import_with_error_recovery": true, "backend/tests/test_import_export/test_import_service.py::TestImportService::test_get_import_template": true, "backend/tests/test_import_export/test_import_service.py::TestImportService::test_get_supported_formats": true, "backend/tests/test_import_export/test_import_service.py::TestImportService::test_logging_functionality": true, "backend/tests/test_import_export/test_import_service.py::TestImportService::test_import_project_data_basic": true, "backend/tests/test_import_export/test_import_service.py::TestImportService::test_import_global_data_basic": true, "backend/tests/test_import_export/test_import_service.py::TestImportService::test_import_history_functionality": true, "backend/tests/test_import_export/test_import_service.py::TestImportService::test_generate_import_template": true, "backend/tests/test_import_export/test_import_service.py::TestImportService::test_error_handling_invalid_data_type": true, "backend/tests/test_import_export/test_import_service.py::TestImportService::test_cleanup_temp_files": true, "backend/tests/test_import_export/test_import_service.py::TestImportService::test_get_import_status": true, "backend/tests/test_import_export/test_import_service.py::TestImportService::test_service_methods_exist": true, "backend/tests/test_api/test_calculations_routes.py::TestClient": true, "backend/tests/test_api/test_calculations_routes.py::TestCalculationsRoutes": true, "backend/tests/test_api/test_project_routes.py::TestClient": true, "backend/tests/test_api/test_project_routes.py::TestProjectRoutes": true, "backend/tests/test_api/test_project_routes.py::TestProjectRoutesWithEnhancedDI": true, "backend/tests/test_api/test_standards_routes.py::TestClient": true, "backend/tests/test_api/test_standards_routes.py::TestStandardsRoutes": true, "backend/tests/test_schemas/test_project_schemas.py::TestProjectCreateSchema": true, "backend/tests/test_schemas/test_project_schemas.py::TestProjectUpdateSchema": true, "backend/tests/test_schemas/test_project_schemas.py::TestProjectReadSchema": true, "backend/tests/test_schemas/test_project_schemas.py::TestProjectSummarySchema": true, "backend/tests/test_schemas/test_project_schemas.py::TestProjectListResponseSchema": true, "backend/tests/test_services/test_project_service.py::TestProjectService": true, "backend/tests/test_services/test_project_service.py::TestProjectServiceWithUtilities": true, "backend/tests/test_services/test_project_service.py::TestProjectServiceWithDependencyInjection": true, "backend/tests/test_services/test_project_service.py::TestProjectServiceIntegrationPatterns": true}