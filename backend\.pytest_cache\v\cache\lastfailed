{"tests/test_integration/test_end_to_end_workflow.py": true, "tests/test_services/test_heat_tracing_service.py::TestHeatTracingServiceVesselOperations::test_create_vessel_invalid_dimensions": true, "tests/test_services/test_heat_tracing_service.py::TestHeatTracingServiceCalculations::test_calculate_pipe_heat_loss_success": true, "tests/test_services/test_heat_tracing_service.py::TestHeatTracingServiceCalculations::test_calculate_pipe_heat_loss_pipe_not_found": true, "tests/test_services/test_heat_tracing_service.py::TestHeatTracingServiceCalculations::test_validate_standards_compliance_success": true, "tests/test_services/test_project_service.py::TestProjectServiceWithUtilities::test_create_project_with_string_sanitization": true, "tests/test_services/test_project_service.py::TestProjectServiceWithUtilities::test_get_projects_with_pagination_utils": true, "tests/test_services/test_project_service.py::TestProjectServiceWithUtilities::test_update_project_with_datetime_utils": true, "tests/test_services/test_project_service.py::TestProjectServiceWithDependencyInjection::test_service_method_with_repository_di": true, "tests/test_services/test_project_service.py::TestProjectServiceIntegrationPatterns::test_complete_workflow_with_utils_and_di": true, "tests/test_services/test_switchboard_service.py::TestSwitchboardService::test_create_switchboard_success": true, "tests/test_services/test_switchboard_service.py::TestSwitchboardService::test_create_switchboard_invalid_voltage": true, "tests/test_services/test_switchboard_service.py::TestSwitchboardService::test_get_switchboard_success": true, "tests/test_services/test_switchboard_service.py::TestSwitchboardService::test_update_switchboard_success": true, "tests/test_services/test_switchboard_service.py::TestSwitchboardService::test_update_switchboard_invalid_voltage": true, "tests/test_services/test_switchboard_service.py::TestSwitchboardService::test_create_feeder_success": true, "tests/test_services/test_switchboard_service.py::TestSwitchboardService::test_add_switchboard_component_success": true, "tests/test_services/test_user_service.py::TestUserService::test_login_success": true, "tests/test_services/test_user_service.py::TestUserService::test_login_invalid_password": true, "tests/test_services/test_user_service.py::TestUserService::test_change_password_success": true, "tests/test_services/test_user_service.py::TestUserService::test_change_password_invalid_current": true, "tests/test_services/test_user_service.py::TestUserService::test_update_user_success": true, "tests/test_services/test_user_service.py::TestUserService::test_deactivate_user_success": true, "tests/test_services/test_user_service.py::TestUserService::test_hash_password": true, "tests/test_services/test_user_service.py::TestUserService::test_verify_password": true, "tests/test_api/test_calculations_routes.py::TestCalculationsRoutes::test_calculate_heat_tracing_valid_request": true, "tests/test_api/test_calculations_routes.py::TestCalculationsRoutes::test_calculate_heat_tracing_invalid_request": true, "tests/test_api/test_calculations_routes.py::TestCalculationsRoutes::test_calculate_heat_tracing_missing_auth": true, "tests/test_api/test_calculations_routes.py::TestCalculationsRoutes::test_calculate_electrical_load_valid_request": true, "tests/test_api/test_calculations_routes.py::TestCalculationsRoutes::test_calculate_power_requirements_valid_request": true, "tests/test_api/test_calculations_routes.py::TestCalculationsRoutes::test_calculate_cable_selection_valid_request": true, "tests/test_api/test_calculations_routes.py::TestCalculationsRoutes::test_get_calculation_history": true, "tests/test_api/test_calculations_routes.py::TestCalculationsRoutes::test_get_calculation_history_filtered": true, "tests/test_api/test_calculations_routes.py::TestCalculationsRoutes::test_validate_calculation_input": true, "tests/test_api/test_calculations_routes.py::TestCalculationsRoutes::test_get_calculation_templates": true, "tests/test_api/test_calculations_routes.py::TestCalculationsRoutes::test_get_calculation_template_specific": true, "tests/test_api/test_calculations_routes.py::TestCalculationsRoutes::test_calculations_health_check": true, "tests/test_api/test_calculations_routes.py::TestCalculationsRoutes::test_calculations_routes_without_auth": true, "tests/test_api/test_calculations_routes.py::TestCalculationsRoutes::test_calculate_heat_tracing_large_dataset": true, "tests/test_api/test_calculations_routes.py::TestCalculationsRoutes::test_calculate_with_performance_monitoring": true, "tests/test_api/test_calculations_routes.py::TestCalculationsRoutes::test_error_handling_invalid_json": true, "tests/test_api/test_calculations_routes.py::TestCalculationsRoutes::test_error_handling_missing_fields": true, "tests/test_api/test_component_routes.py::TestComponentRoutes::test_create_component_duplicate_error": true, "tests/test_api/test_component_routes.py::TestComponentRoutes::test_get_component_success": true, "tests/test_api/test_component_routes.py::TestComponentRoutes::test_update_component_success": true, "tests/test_api/test_component_routes.py::TestComponentRoutes::test_delete_component_success": true, "tests/test_api/test_component_routes.py::TestComponentRoutes::test_list_components_success": true, "tests/test_api/test_component_routes.py::TestComponentRoutes::test_list_components_with_filters": true, "tests/test_api/test_component_routes.py::TestComponentCategoryRoutes::test_create_category_success": true, "tests/test_api/test_component_routes.py::TestComponentCategoryRoutes::test_get_category_success": true, "tests/test_api/test_component_routes.py::TestComponentCategoryRoutes::test_list_categories_success": true, "tests/test_api/test_component_routes_error_handling.py::TestComponentErrorHandling::test_create_component_database_error": true, "tests/test_api/test_component_routes_error_handling.py::TestComponentErrorHandling::test_create_component_integrity_constraint_violation": true, "tests/test_api/test_component_routes_error_handling.py::TestComponentErrorHandling::test_create_component_malformed_json_data": true, "tests/test_api/test_component_routes_error_handling.py::TestComponentErrorHandling::test_create_component_extremely_large_payload": true, "tests/test_api/test_component_routes_error_handling.py::TestComponentErrorHandling::test_get_component_service_unavailable": true, "tests/test_api/test_component_routes_error_handling.py::TestComponentErrorHandling::test_update_component_concurrent_modification": true, "tests/test_api/test_component_routes_error_handling.py::TestComponentErrorHandling::test_delete_component_with_dependencies": true, "tests/test_api/test_component_routes_error_handling.py::TestComponentErrorHandling::test_list_components_database_timeout": true, "tests/test_api/test_component_routes_error_handling.py::TestComponentErrorHandling::test_search_components_with_sql_injection_attempt": true, "tests/test_api/test_component_routes_error_handling.py::TestComponentCategoryErrorHandling::test_create_category_circular_dependency": true, "tests/test_api/test_component_routes_error_handling.py::TestComponentCategoryErrorHandling::test_delete_category_with_subcategories": true, "tests/test_api/test_component_routes_error_handling.py::TestComponentCategoryErrorHandling::test_update_category_invalid_parent_hierarchy": true, "tests/test_api/test_component_routes_error_handling.py::TestComponentValidationEdgeCases::test_component_name_unicode_characters": true, "tests/test_api/test_component_routes_error_handling.py::TestComponentValidationEdgeCases::test_component_specific_data_edge_cases": true, "tests/test_api/test_document_routes.py::TestCalculationStandardRoutes::test_create_calculation_standard": true, "tests/test_api/test_document_routes.py::TestCalculationStandardRoutes::test_get_calculation_standard_by_id": true, "tests/test_api/test_document_routes.py::TestCalculationStandardRoutes::test_get_calculation_standard_by_code": true, "tests/test_api/test_document_routes.py::TestCalculationStandardRoutes::test_list_calculation_standards": true, "tests/test_api/test_document_routes.py::TestCalculationStandardRoutes::test_update_calculation_standard": true, "tests/test_api/test_document_routes.py::TestCalculationStandardRoutes::test_delete_calculation_standard": true, "tests/test_api/test_document_routes.py::TestCalculationStandardRoutes::test_create_calculation_standard_duplicate_code": true, "tests/test_api/test_document_routes.py::TestCalculationStandardRoutes::test_get_nonexistent_calculation_standard": true, "tests/test_api/test_document_routes.py::TestCalculationStandardRoutes::test_calculation_standard_validation_errors": true, "tests/test_api/test_document_routes.py::TestImportedDataRevisionRoutes::test_create_imported_data_revision": true, "tests/test_api/test_document_routes.py::TestImportedDataRevisionRoutes::test_list_imported_data_revisions_by_project": true, "tests/test_api/test_document_routes.py::TestExportedDocumentRoutes::test_create_exported_document": true, "tests/test_api/test_document_routes.py::TestExportedDocumentRoutes::test_list_exported_documents_by_project": true, "tests/test_api/test_electrical_routes.py::TestElectricalNodeRoutes::test_create_electrical_node_success": true, "tests/test_api/test_electrical_routes.py::TestElectricalNodeRoutes::test_get_electrical_node_success": true, "tests/test_api/test_electrical_routes.py::TestElectricalNodeRoutes::test_list_electrical_nodes_success": true, "tests/test_api/test_electrical_routes.py::TestElectricalNodeRoutes::test_update_electrical_node_success": true, "tests/test_api/test_electrical_routes.py::TestCableRouteRoutes::test_create_cable_route_success": true, "tests/test_api/test_electrical_routes.py::TestCableRouteRoutes::test_get_cable_route_success": true, "tests/test_api/test_electrical_routes.py::TestCableRouteRoutes::test_list_cable_routes_success": true, "tests/test_api/test_electrical_routes.py::TestLoadCalculationRoutes::test_create_load_calculation_success": true, "tests/test_api/test_electrical_routes.py::TestLoadCalculationRoutes::test_get_load_calculation_success": true, "tests/test_api/test_electrical_routes.py::TestLoadCalculationRoutes::test_list_load_calculations_success": true, "tests/test_api/test_electrical_routes.py::TestElectricalCalculationRoutes::test_cable_sizing_calculation_success": true, "tests/test_api/test_electrical_routes.py::TestElectricalCalculationRoutes::test_voltage_drop_calculation_success": true, "tests/test_api/test_electrical_routes.py::TestElectricalCalculationRoutes::test_standards_validation_success": true, "tests/test_api/test_electrical_routes.py::TestElectricalBusinessLogicRoutes::test_node_load_summary_success": true, "tests/test_api/test_electrical_routes.py::TestElectricalBusinessLogicRoutes::test_design_workflow_success": true, "tests/test_api/test_electrical_routes_edge_cases.py::TestElectricalNodeEdgeCases::test_create_electrical_node_boundary_values": true, "tests/test_api/test_electrical_routes_edge_cases.py::TestElectricalNodeEdgeCases::test_create_electrical_node_extreme_string_lengths": true, "tests/test_api/test_electrical_routes_edge_cases.py::TestCableRouteEdgeCases::test_create_cable_route_extreme_lengths": true, "tests/test_api/test_electrical_routes_edge_cases.py::TestElectricalCalculationEdgeCases::test_cable_sizing_calculation_extreme_values": true, "tests/test_api/test_heat_tracing_routes.py::TestPipeEndpoints::test_create_pipe_success": true, "tests/test_api/test_heat_tracing_routes.py::TestPipeEndpoints::test_create_pipe_validation_error": true, "tests/test_api/test_heat_tracing_routes.py::TestPipeEndpoints::test_create_pipe_duplicate_error": true, "tests/test_api/test_heat_tracing_routes.py::TestPipeEndpoints::test_get_pipe_success": true, "tests/test_api/test_heat_tracing_routes.py::TestPipeEndpoints::test_get_pipe_not_found": true, "tests/test_api/test_heat_tracing_routes.py::TestPipeEndpoints::test_update_pipe_success": true, "tests/test_api/test_heat_tracing_routes.py::TestPipeEndpoints::test_delete_pipe_success": true, "tests/test_api/test_heat_tracing_routes.py::TestPipeEndpoints::test_list_pipes_success": true, "tests/test_api/test_heat_tracing_routes.py::TestVesselEndpoints::test_get_vessel_success": true, "tests/test_api/test_heat_tracing_routes.py::TestCalculationEndpoints::test_calculate_pipe_heat_loss_success": true, "tests/test_api/test_heat_tracing_routes.py::TestCalculationEndpoints::test_validate_standards_compliance_success": true, "tests/test_api/test_heat_tracing_routes.py::TestDesignWorkflowEndpoints::test_execute_design_workflow_success": true, "tests/test_api/test_project_routes.py::TestProjectRoutes::test_list_projects_success": true, "tests/test_api/test_project_routes.py::TestProjectRoutes::test_list_projects_with_pagination": true, "tests/test_api/test_project_routes.py::TestProjectRoutes::test_list_projects_include_deleted": true, "tests/test_api/test_project_routes.py::TestProjectRoutesWithEnhancedDI::test_create_project_with_dependency_override_manager": true, "tests/test_api/test_project_routes.py::TestProjectRoutesWithEnhancedDI::test_list_projects_with_pagination_utils": true, "tests/test_api/test_standards_routes.py::TestStandardsRoutes::test_validate_against_specific_standard": true, "tests/test_api/test_standards_routes.py::TestStandardsRoutes::test_calculate_with_specific_standard": true, "tests/test_api/test_standards_routes.py::TestStandardsRoutes::test_validate_design_invalid_input": true, "tests/test_api/test_standards_routes.py::TestStandardsRoutes::test_standards_routes_without_auth": true, "tests/test_api/test_switchboard_api.py::TestSwitchboardAPI::test_list_switchboards_success": true, "tests/test_api/test_switchboard_api.py::TestSwitchboardAPI::test_business_logic_error_handling": true, "tests/test_api/test_user_api.py::TestUserManagementAPI::test_create_user_success": true, "tests/test_api/test_user_api.py::TestUserManagementAPI::test_create_user_email_exists": true, "tests/test_api/test_user_api.py::TestUserManagementAPI::test_get_user_success": true, "tests/test_api/test_user_api.py::TestUserManagementAPI::test_get_user_not_found": true, "tests/test_api/test_user_api.py::TestUserManagementAPI::test_list_users_success": true, "tests/test_api/test_user_api.py::TestUserManagementAPI::test_list_users_with_search": true, "tests/test_api/test_user_api.py::TestUserManagementAPI::test_update_user_success": true, "tests/test_api/test_user_api.py::TestUserManagementAPI::test_deactivate_user_success": true, "tests/test_api/test_user_api.py::TestUserPreferencesAPI::test_get_user_preferences_success": true, "tests/test_api/test_user_api.py::TestUserPreferencesAPI::test_get_user_preferences_not_found": true, "tests/test_api/test_user_api.py::TestUserPreferencesAPI::test_update_user_preferences_success": true, "tests/test_import_export/test_csv_parser.py::TestCSVParser::test_initialization": true, "tests/test_import_export/test_csv_parser.py::TestCSVParser::test_parse_valid_csv_file": true, "tests/test_import_export/test_csv_parser.py::TestCSVParser::test_parse_csv_with_custom_delimiter": true, "tests/test_import_export/test_csv_parser.py::TestCSVParser::test_parse_csv_with_missing_headers": true, "tests/test_import_export/test_csv_parser.py::TestCSVParser::test_parse_csv_with_empty_rows": true, "tests/test_import_export/test_csv_parser.py::TestCSVParser::test_parse_nonexistent_file": true, "tests/test_import_export/test_csv_parser.py::TestCSVParser::test_parse_invalid_csv_file": true}