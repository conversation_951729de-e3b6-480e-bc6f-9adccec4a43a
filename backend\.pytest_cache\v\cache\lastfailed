{"tests/test_services/test_heat_tracing_service.py::TestHeatTracingServiceVesselOperations::test_create_vessel_invalid_dimensions": true, "tests/test_services/test_heat_tracing_service.py::TestHeatTracingServiceCalculations::test_calculate_pipe_heat_loss_success": true, "tests/test_services/test_heat_tracing_service.py::TestHeatTracingServiceCalculations::test_calculate_pipe_heat_loss_pipe_not_found": true, "tests/test_services/test_heat_tracing_service.py::TestHeatTracingServiceCalculations::test_validate_standards_compliance_success": true, "tests/test_services/test_project_service.py::TestProjectServiceWithUtilities::test_create_project_with_string_sanitization": true, "tests/test_services/test_project_service.py::TestProjectServiceWithUtilities::test_update_project_with_datetime_utils": true, "tests/test_services/test_project_service.py::TestProjectServiceWithDependencyInjection::test_service_method_with_repository_di": true, "tests/test_services/test_project_service.py::TestProjectServiceIntegrationPatterns::test_complete_workflow_with_utils_and_di": true, "tests/test_services/test_switchboard_service.py::TestSwitchboardService::test_create_switchboard_success": true, "tests/test_services/test_switchboard_service.py::TestSwitchboardService::test_create_switchboard_invalid_voltage": true, "tests/test_services/test_switchboard_service.py::TestSwitchboardService::test_get_switchboard_success": true, "tests/test_services/test_switchboard_service.py::TestSwitchboardService::test_update_switchboard_success": true, "tests/test_services/test_switchboard_service.py::TestSwitchboardService::test_update_switchboard_invalid_voltage": true, "tests/test_services/test_switchboard_service.py::TestSwitchboardService::test_create_feeder_success": true, "tests/test_services/test_switchboard_service.py::TestSwitchboardService::test_add_switchboard_component_success": true, "tests/test_services/test_user_service.py::TestUserService::test_login_success": true, "tests/test_services/test_user_service.py::TestUserService::test_login_invalid_password": true, "tests/test_services/test_user_service.py::TestUserService::test_change_password_success": true, "tests/test_services/test_user_service.py::TestUserService::test_change_password_invalid_current": true, "tests/test_services/test_user_service.py::TestUserService::test_update_user_success": true, "tests/test_api/test_calculations_routes.py::TestCalculationsRoutes::test_calculate_heat_tracing_valid_request": true, "tests/test_api/test_calculations_routes.py::TestCalculationsRoutes::test_calculate_heat_tracing_invalid_request": true, "tests/test_api/test_calculations_routes.py::TestCalculationsRoutes::test_calculate_heat_tracing_missing_auth": true, "tests/test_api/test_calculations_routes.py::TestCalculationsRoutes::test_calculate_electrical_load_valid_request": true, "tests/test_api/test_calculations_routes.py::TestCalculationsRoutes::test_calculate_power_requirements_valid_request": true, "tests/test_api/test_calculations_routes.py::TestCalculationsRoutes::test_calculate_cable_selection_valid_request": true, "tests/test_api/test_calculations_routes.py::TestCalculationsRoutes::test_get_calculation_history": true, "tests/test_api/test_calculations_routes.py::TestCalculationsRoutes::test_get_calculation_history_filtered": true, "tests/test_api/test_calculations_routes.py::TestCalculationsRoutes::test_validate_calculation_input": true, "tests/test_api/test_calculations_routes.py::TestCalculationsRoutes::test_get_calculation_templates": true, "tests/test_api/test_calculations_routes.py::TestCalculationsRoutes::test_get_calculation_template_specific": true, "tests/test_api/test_calculations_routes.py::TestCalculationsRoutes::test_calculations_health_check": true, "tests/test_api/test_calculations_routes.py::TestCalculationsRoutes::test_calculations_routes_without_auth": true, "tests/test_api/test_calculations_routes.py::TestCalculationsRoutes::test_calculate_heat_tracing_large_dataset": true, "tests/test_api/test_calculations_routes.py::TestCalculationsRoutes::test_calculate_with_performance_monitoring": true, "tests/test_api/test_calculations_routes.py::TestCalculationsRoutes::test_error_handling_invalid_json": true, "tests/test_api/test_calculations_routes.py::TestCalculationsRoutes::test_error_handling_missing_fields": true, "tests/test_api/test_component_routes.py::TestComponentRoutes::test_create_component_duplicate_error": true, "tests/test_api/test_component_routes.py::TestComponentRoutes::test_get_component_success": true, "tests/test_api/test_component_routes.py::TestComponentRoutes::test_update_component_success": true, "tests/test_api/test_component_routes.py::TestComponentRoutes::test_delete_component_success": true, "tests/test_api/test_component_routes.py::TestComponentRoutes::test_list_components_success": true, "tests/test_api/test_component_routes.py::TestComponentRoutes::test_list_components_with_filters": true, "tests/test_api/test_component_routes.py::TestComponentCategoryRoutes::test_create_category_success": true, "tests/test_api/test_component_routes.py::TestComponentCategoryRoutes::test_get_category_success": true, "tests/test_api/test_component_routes.py::TestComponentCategoryRoutes::test_list_categories_success": true, "tests/test_api/test_component_routes_error_handling.py::TestComponentErrorHandling::test_create_component_database_error": true, "tests/test_api/test_component_routes_error_handling.py::TestComponentErrorHandling::test_create_component_integrity_constraint_violation": true, "tests/test_api/test_component_routes_error_handling.py::TestComponentErrorHandling::test_get_component_service_unavailable": true, "tests/test_api/test_component_routes_error_handling.py::TestComponentErrorHandling::test_update_component_concurrent_modification": true, "tests/test_api/test_component_routes_error_handling.py::TestComponentErrorHandling::test_delete_component_with_dependencies": true, "tests/test_api/test_component_routes_error_handling.py::TestComponentErrorHandling::test_list_components_database_timeout": true, "tests/test_api/test_component_routes_error_handling.py::TestComponentCategoryErrorHandling::test_create_category_circular_dependency": true, "tests/test_api/test_component_routes_error_handling.py::TestComponentCategoryErrorHandling::test_delete_category_with_subcategories": true, "tests/test_api/test_component_routes_error_handling.py::TestComponentCategoryErrorHandling::test_update_category_invalid_parent_hierarchy": true, "tests/test_api/test_component_routes_error_handling.py::TestComponentValidationEdgeCases::test_component_name_unicode_characters": true, "tests/test_api/test_component_routes_error_handling.py::TestComponentValidationEdgeCases::test_component_specific_data_edge_cases": true, "tests/test_api/test_document_routes.py::TestImportedDataRevisionRoutes::test_create_imported_data_revision": true, "tests/test_api/test_document_routes.py::TestImportedDataRevisionRoutes::test_list_imported_data_revisions_by_project": true, "tests/test_api/test_document_routes.py::TestExportedDocumentRoutes::test_create_exported_document": true, "tests/test_api/test_document_routes.py::TestExportedDocumentRoutes::test_list_exported_documents_by_project": true, "tests/test_api/test_electrical_routes.py::TestElectricalNodeRoutes::test_create_electrical_node_success": true, "tests/test_api/test_electrical_routes.py::TestElectricalNodeRoutes::test_get_electrical_node_success": true, "tests/test_api/test_electrical_routes.py::TestElectricalNodeRoutes::test_list_electrical_nodes_success": true, "tests/test_api/test_electrical_routes.py::TestElectricalNodeRoutes::test_update_electrical_node_success": true, "tests/test_api/test_electrical_routes.py::TestCableRouteRoutes::test_create_cable_route_success": true, "tests/test_api/test_electrical_routes.py::TestCableRouteRoutes::test_get_cable_route_success": true, "tests/test_api/test_electrical_routes.py::TestCableRouteRoutes::test_list_cable_routes_success": true, "tests/test_api/test_electrical_routes.py::TestLoadCalculationRoutes::test_create_load_calculation_success": true, "tests/test_api/test_electrical_routes.py::TestLoadCalculationRoutes::test_get_load_calculation_success": true, "tests/test_api/test_electrical_routes.py::TestLoadCalculationRoutes::test_list_load_calculations_success": true, "tests/test_api/test_electrical_routes.py::TestElectricalCalculationRoutes::test_cable_sizing_calculation_success": true, "tests/test_api/test_electrical_routes.py::TestElectricalCalculationRoutes::test_voltage_drop_calculation_success": true, "tests/test_api/test_electrical_routes.py::TestElectricalCalculationRoutes::test_standards_validation_success": true, "tests/test_api/test_electrical_routes.py::TestElectricalBusinessLogicRoutes::test_node_load_summary_success": true, "tests/test_api/test_electrical_routes.py::TestElectricalBusinessLogicRoutes::test_design_workflow_success": true, "tests/test_api/test_electrical_routes_edge_cases.py::TestElectricalNodeEdgeCases::test_create_electrical_node_boundary_values": true, "tests/test_api/test_electrical_routes_edge_cases.py::TestElectricalNodeEdgeCases::test_create_electrical_node_extreme_string_lengths": true, "tests/test_api/test_electrical_routes_edge_cases.py::TestCableRouteEdgeCases::test_create_cable_route_extreme_lengths": true, "tests/test_api/test_electrical_routes_edge_cases.py::TestElectricalCalculationEdgeCases::test_cable_sizing_calculation_extreme_values": true, "tests/test_api/test_heat_tracing_routes.py::TestPipeEndpoints::test_create_pipe_success": true, "tests/test_api/test_heat_tracing_routes.py::TestPipeEndpoints::test_create_pipe_validation_error": true, "tests/test_api/test_heat_tracing_routes.py::TestPipeEndpoints::test_create_pipe_duplicate_error": true, "tests/test_api/test_heat_tracing_routes.py::TestPipeEndpoints::test_get_pipe_success": true, "tests/test_api/test_heat_tracing_routes.py::TestPipeEndpoints::test_get_pipe_not_found": true, "tests/test_api/test_heat_tracing_routes.py::TestPipeEndpoints::test_update_pipe_success": true, "tests/test_api/test_heat_tracing_routes.py::TestPipeEndpoints::test_delete_pipe_success": true, "tests/test_api/test_heat_tracing_routes.py::TestPipeEndpoints::test_list_pipes_success": true, "tests/test_api/test_heat_tracing_routes.py::TestVesselEndpoints::test_get_vessel_success": true, "tests/test_api/test_heat_tracing_routes.py::TestCalculationEndpoints::test_calculate_pipe_heat_loss_success": true, "tests/test_api/test_heat_tracing_routes.py::TestCalculationEndpoints::test_validate_standards_compliance_success": true, "tests/test_api/test_heat_tracing_routes.py::TestDesignWorkflowEndpoints::test_execute_design_workflow_success": true, "tests/test_api/test_project_routes.py::TestProjectRoutes::test_list_projects_success": true, "tests/test_api/test_project_routes.py::TestProjectRoutes::test_list_projects_with_pagination": true, "tests/test_api/test_project_routes.py::TestProjectRoutes::test_list_projects_include_deleted": true, "tests/test_api/test_project_routes.py::TestProjectRoutesWithEnhancedDI::test_create_project_with_dependency_override_manager": true, "tests/test_api/test_project_routes.py::TestProjectRoutesWithEnhancedDI::test_list_projects_with_pagination_utils": true, "tests/test_api/test_standards_routes.py::TestStandardsRoutes::test_validate_against_specific_standard": true, "tests/test_api/test_standards_routes.py::TestStandardsRoutes::test_calculate_with_specific_standard": true, "tests/test_api/test_standards_routes.py::TestStandardsRoutes::test_validate_design_invalid_input": true, "tests/test_api/test_standards_routes.py::TestStandardsRoutes::test_standards_routes_without_auth": true, "tests/test_api/test_switchboard_api.py::TestSwitchboardAPI::test_list_switchboards_success": true, "tests/test_api/test_switchboard_api.py::TestSwitchboardAPI::test_business_logic_error_handling": true, "tests/test_api/test_user_api.py::TestUserManagementAPI::test_create_user_success": true, "tests/test_api/test_user_api.py::TestUserManagementAPI::test_create_user_email_exists": true, "tests/test_api/test_user_api.py::TestUserManagementAPI::test_get_user_success": true, "tests/test_api/test_user_api.py::TestUserManagementAPI::test_get_user_not_found": true, "tests/test_api/test_user_api.py::TestUserManagementAPI::test_list_users_success": true, "tests/test_api/test_user_api.py::TestUserManagementAPI::test_list_users_with_search": true, "tests/test_api/test_user_api.py::TestUserManagementAPI::test_update_user_success": true, "tests/test_api/test_user_api.py::TestUserManagementAPI::test_deactivate_user_success": true, "tests/test_api/test_user_api.py::TestUserPreferencesAPI::test_get_user_preferences_success": true, "tests/test_api/test_user_api.py::TestUserPreferencesAPI::test_get_user_preferences_not_found": true, "tests/test_api/test_user_api.py::TestUserPreferencesAPI::test_update_user_preferences_success": true, "tests/test_import_export/test_csv_parser.py::TestCSVParser::test_initialization": true, "tests/test_import_export/test_csv_parser.py::TestCSVParser::test_parse_valid_csv_file": true, "tests/test_import_export/test_csv_parser.py::TestCSVParser::test_parse_csv_with_custom_delimiter": true, "tests/test_import_export/test_csv_parser.py::TestCSVParser::test_parse_csv_with_missing_headers": true, "tests/test_import_export/test_csv_parser.py::TestCSVParser::test_parse_csv_with_empty_rows": true, "tests/test_import_export/test_csv_parser.py::TestCSVParser::test_parse_nonexistent_file": true, "tests/test_import_export/test_csv_parser.py::TestCSVParser::test_parse_invalid_csv_file": true, "tests/test_schemas/test_project_schemas.py::TestProjectReadSchema::test_from_orm_conversion": true, "tests/test_schemas/test_project_schemas.py::TestProjectReadSchema::test_read_schema_includes_all_fields": true, "tests/test_schemas/test_project_schemas.py::TestProjectSummarySchema::test_summary_includes_essential_fields": true, "tests/test_schemas/test_project_schemas.py::TestProjectSummarySchema::test_summary_excludes_detailed_fields": true, "tests/test_repositories/test_document_repository.py::TestImportedDataRevisionRepository::test_create_imported_data_revision": true, "tests/test_integration/test_end_to_end_workflow.py::TestEndToEndWorkflow::test_complete_design_workflow": true, "tests/test_integration/test_end_to_end_workflow.py::TestEndToEndWorkflow::test_import_calculate_export_workflow": true, "tests/test_integration/test_end_to_end_workflow.py::TestEndToEndWorkflow::test_multi_format_report_generation_workflow": true, "tests/test_integration/test_end_to_end_workflow.py::TestEndToEndWorkflow::test_error_recovery_workflow": true, "tests/test_integration/test_end_to_end_workflow.py::TestEndToEndWorkflow::test_performance_large_dataset_workflow": true, "tests/test_integration/test_end_to_end_workflow.py::TestEndToEndWorkflow::test_caching_across_services_workflow": true, "tests/test_integration/test_end_to_end_workflow.py::TestEndToEndWorkflow::test_data_consistency_workflow": true, "tests/test_integration/test_end_to_end_workflow.py::TestEndToEndWorkflow::test_service_integration_error_handling": true, "tests/test_api/test_electrical_routes_edge_cases.py::TestElectricalNodeEdgeCases::test_create_electrical_node_invalid_enum_values": true, "tests/test_api/test_electrical_routes_edge_cases.py::TestElectricalNodeEdgeCases::test_list_electrical_nodes_pagination_edge_cases": true, "tests/test_api/test_electrical_routes_edge_cases.py::TestCableRouteEdgeCases::test_create_cable_route_same_nodes": true, "tests/test_calculations/test_calculation_service.py::TestCalculationService::test_initialization": true, "tests/test_calculations/test_calculation_service.py::TestCalculationService::test_calculate_heat_tracing_system_valid_input": true, "tests/test_calculations/test_calculation_service.py::TestCalculationService::test_calculate_heat_tracing_system_single_circuit": true, "tests/test_calculations/test_calculation_service.py::TestCalculationService::test_calculate_electrical_system_valid_input": true, "tests/test_calculations/test_calculation_service.py::TestCalculationService::test_calculate_heat_tracing_system_invalid_input": true, "tests/test_calculations/test_calculation_service.py::TestCalculationService::test_calculate_heat_tracing_system_empty_circuits": true, "tests/test_calculations/test_calculation_service.py::TestCalculationService::test_calculate_system_summary": true, "tests/test_calculations/test_calculation_service.py::TestCalculationService::test_calculate_with_different_safety_factors": true, "tests/test_calculations/test_calculation_service.py::TestCalculationService::test_calculate_with_optimization_options": true, "tests/test_calculations/test_calculation_service.py::TestCalculationService::test_calculate_parallel_processing": true, "tests/test_calculations/test_calculation_service.py::TestCalculationService::test_calculate_with_caching": true, "tests/test_calculations/test_calculation_service.py::TestCalculationService::test_calculate_with_validation_errors": true, "tests/test_calculations/test_calculation_service.py::TestCalculationService::test_calculate_load_analysis": true, "tests/test_calculations/test_calculation_service.py::TestCalculationService::test_calculate_cost_estimation": true, "tests/test_calculations/test_calculation_service.py::TestCalculationService::test_calculate_energy_analysis": true, "tests/test_calculations/test_calculation_service.py::TestCalculationService::test_error_handling_and_recovery": true, "tests/test_calculations/test_calculation_service.py::TestCalculationService::test_logging_functionality": true, "tests/test_calculations/test_calculation_service.py::TestCalculationService::test_performance_monitoring": true, "tests/test_calculations/test_heat_loss_calculator.py::TestHeatLossCalculator::test_initialization": true, "tests/test_calculations/test_heat_loss_calculator.py::TestHeatLossCalculator::test_calculate_thermal_resistance_valid_input": true, "tests/test_calculations/test_heat_loss_calculator.py::TestHeatLossCalculator::test_calculate_thermal_resistance_invalid_diameter": true, "tests/test_calculations/test_heat_loss_calculator.py::TestHeatLossCalculator::test_calculate_thermal_resistance_zero_thickness": true, "tests/test_calculations/test_heat_loss_calculator.py::TestHeatLossCalculator::test_calculate_heat_loss_valid_input": true, "tests/test_calculations/test_heat_loss_calculator.py::TestHeatLossCalculator::test_calculate_heat_loss_zero_temperature_difference": true, "tests/test_calculations/test_heat_loss_calculator.py::TestHeatLossCalculator::test_calculate_heat_loss_missing_required_fields": true, "tests/test_calculations/test_heat_loss_calculator.py::TestHeatLossCalculator::test_calculate_convection_coefficient": true, "tests/test_calculations/test_heat_loss_calculator.py::TestHeatLossCalculator::test_calculate_radiation_coefficient": true, "tests/test_calculations/test_heat_loss_calculator.py::TestHeatLossCalculator::test_calculate_heat_loss_with_radiation": true, "tests/test_calculations/test_heat_loss_calculator.py::TestHeatLossCalculator::test_calculate_heat_loss_with_safety_factors": true, "tests/test_calculations/test_heat_loss_calculator.py::TestHeatLossCalculator::test_calculate_heat_loss_validation_errors": true, "tests/test_calculations/test_heat_loss_calculator.py::TestHeatLossCalculator::test_logging_functionality": true, "tests/test_calculations/test_power_calculator.py::TestPowerCalculator::test_initialization": true, "tests/test_calculations/test_power_calculator.py::TestPowerCalculator::test_calculate_power_requirements_valid_input": true, "tests/test_calculations/test_power_calculator.py::TestPowerCalculator::test_calculate_power_requirements_different_applications": true, "tests/test_calculations/test_power_calculator.py::TestPowerCalculator::test_calculate_electrical_parameters": true, "tests/test_calculations/test_power_calculator.py::TestPowerCalculator::test_calculate_electrical_parameters_three_phase": true, "tests/test_calculations/test_power_calculator.py::TestPowerCalculator::test_select_cable_self_regulating": true, "tests/test_calculations/test_power_calculator.py::TestPowerCalculator::test_select_cable_constant_wattage": true, "tests/test_calculations/test_power_calculator.py::TestPowerCalculator::test_calculate_cable_derating": true, "tests/test_calculations/test_power_calculator.py::TestPowerCalculator::test_calculate_voltage_drop": true, "tests/test_calculations/test_power_calculator.py::TestPowerCalculator::test_calculate_circuit_protection": true, "tests/test_calculations/test_power_calculator.py::TestPowerCalculator::test_calculate_power_requirements_invalid_input": true, "tests/test_calculations/test_power_calculator.py::TestPowerCalculator::test_calculate_power_requirements_zero_length": true, "tests/test_calculations/test_power_calculator.py::TestPowerCalculator::test_calculate_electrical_parameters_invalid_voltage": true, "tests/test_calculations/test_power_calculator.py::TestPowerCalculator::test_calculate_electrical_parameters_invalid_power_factor": true, "tests/test_calculations/test_power_calculator.py::TestPowerCalculator::test_select_cable_missing_data": true, "tests/test_calculations/test_power_calculator.py::TestPowerCalculator::test_calculate_power_requirements_with_diversity": true, "tests/test_calculations/test_power_calculator.py::TestPowerCalculator::test_calculate_startup_requirements": true, "tests/test_calculations/test_power_calculator.py::TestPowerCalculator::test_calculate_energy_consumption": true, "tests/test_calculations/test_power_calculator.py::TestPowerCalculator::test_logging_functionality": true, "tests/test_integration/test_electrical_integration_enhanced.py::TestElectricalSystemIntegration::test_create_complete_electrical_system": true, "tests/test_integration/test_electrical_integration_enhanced.py::TestElectricalSystemIntegration::test_electrical_calculations_integration": true, "tests/test_integration/test_electrical_integration_enhanced.py::TestElectricalSystemIntegration::test_electrical_system_validation_workflow": true, "tests/test_integration/test_electrical_integration_enhanced.py::TestElectricalSystemIntegration::test_electrical_load_analysis_integration": true, "tests/test_integration/test_electrical_integration_enhanced.py::TestCrossModuleIntegration::test_electrical_component_integration": true, "tests/test_integration/test_electrical_integration_enhanced.py::TestCrossModuleIntegration::test_project_electrical_integration": true, "tests/test_integration/test_simple_document_integration.py::TestDocumentIntegration::test_calculation_standard_service_operations": true, "tests/test_integration/test_simple_document_integration.py::TestDocumentIntegration::test_document_models_relationships": true, "tests/test_performance/test_api_performance.py::TestAPIPerformance::test_electrical_node_creation_performance": true, "tests/test_performance/test_api_performance.py::TestAPIPerformance::test_electrical_node_list_performance": true, "tests/test_performance/test_api_performance.py::TestAPIPerformance::test_component_search_performance": true, "tests/test_performance/test_api_performance.py::TestAPIPerformance::test_cable_sizing_calculation_performance": true, "tests/test_performance/test_api_performance.py::TestAPIPerformance::test_concurrent_request_performance[5]": true, "tests/test_performance/test_api_performance.py::TestAPIPerformance::test_concurrent_request_performance[10]": true, "tests/test_performance/test_api_performance.py::TestAPIPerformance::test_concurrent_request_performance[20]": true, "tests/test_performance/test_api_performance.py::TestAPIPerformance::test_memory_usage_during_large_operations": true, "tests/test_performance/test_api_performance.py::TestDatabasePerformance::test_bulk_insert_performance": true, "tests/test_performance/test_api_performance.py::TestDatabasePerformance::test_complex_query_performance": true, "tests/test_performance/test_load_testing.py::TestAdvancedLoadTesting::test_electrical_nodes_concurrent_creation": true, "tests/test_performance/test_load_testing.py::TestAdvancedLoadTesting::test_component_search_load": true, "tests/test_performance/test_load_testing.py::TestAdvancedLoadTesting::test_calculation_engine_stress": true, "tests/test_reports/test_document_generator.py::TestDocumentGenerator::test_generate_pdf_report_valid_input": true, "tests/test_reports/test_document_generator.py::TestDocumentGenerator::test_generate_html_report_valid_input": true, "tests/test_reports/test_document_generator.py::TestDocumentGenerator::test_generate_report_with_custom_template": true, "tests/test_reports/test_document_generator.py::TestDocumentGenerator::test_generate_report_invalid_output_format": true, "tests/test_reports/test_document_generator.py::TestDocumentGenerator::test_generate_report_missing_required_data": true, "tests/test_reports/test_document_generator.py::TestDocumentGenerator::test_validate_report_data_valid": true, "tests/test_reports/test_document_generator.py::TestDocumentGenerator::test_generate_report_with_charts": true, "tests/test_reports/test_document_generator.py::TestDocumentGenerator::test_generate_report_with_images": true, "tests/test_reports/test_document_generator.py::TestDocumentGenerator::test_get_available_templates": true, "tests/test_reports/test_document_generator.py::TestDocumentGenerator::test_generate_report_with_watermark": true, "tests/test_reports/test_document_generator.py::TestDocumentGenerator::test_logging_functionality": true, "tests/test_reports/test_report_service.py::TestReportService::test_generate_calculation_report_valid_input": true, "tests/test_reports/test_report_service.py::TestReportService::test_generate_calculation_report_html_format": true, "tests/test_reports/test_report_service.py::TestReportService::test_generate_calculation_report_excel_format": true, "tests/test_reports/test_report_service.py::TestReportService::test_generate_calculation_report_with_caching": true, "tests/test_reports/test_report_service.py::TestReportService::test_get_generation_history": true, "tests/test_reports/test_report_service.py::TestReportService::test_get_generation_history_filtered": true, "tests/test_reports/test_report_service.py::TestReportService::test_clear_cache_all": true, "tests/test_reports/test_report_service.py::TestReportService::test_clear_cache_specific_project": true, "tests/test_reports/test_report_service.py::TestReportService::test_get_available_templates": true, "tests/test_reports/test_report_service.py::TestReportService::test_generate_report_performance": true, "tests/test_reports/test_report_service.py::TestReportService::test_logging_functionality": true, "tests/test_repositories/test_document_repository.py::TestImportedDataRevisionRepository::test_get_by_project_id": true, "tests/test_repositories/test_document_repository.py::TestImportedDataRevisionRepository::test_get_active_revision_by_filename": true, "tests/test_repositories/test_document_repository.py::TestImportedDataRevisionRepository::test_deactivate_other_revisions": true, "tests/test_repositories/test_document_repository.py::TestExportedDocumentRepository::test_create_exported_document": true, "tests/test_repositories/test_document_repository.py::TestExportedDocumentRepository::test_get_latest_by_document_type": true, "tests/test_repositories/test_document_repository.py::TestExportedDocumentRepository::test_mark_others_as_not_latest": true, "tests/test_repositories/test_project_repository.py::TestProjectRepository::test_create_project": true, "tests/test_repositories/test_project_repository.py::TestProjectRepository::test_get_by_id_existing": true, "tests/test_repositories/test_project_repository.py::TestProjectRepository::test_get_by_code_existing": true, "tests/test_repositories/test_project_repository.py::TestProjectRepository::test_get_by_name_existing": true, "tests/test_repositories/test_project_repository.py::TestProjectRepository::test_update_project": true, "tests/test_repositories/test_project_repository.py::TestProjectRepository::test_soft_delete_project": true, "tests/test_repositories/test_project_repository.py::TestProjectRepository::test_soft_delete_already_deleted_project": true, "tests/test_repositories/test_project_repository.py::TestProjectRepository::test_get_project_with_related_data": true, "tests/test_repositories/test_project_repository.py::TestProjectRepository::test_unique_constraint_name": true, "tests/test_repositories/test_project_repository.py::TestProjectRepository::test_unique_constraint_project_number": true, "tests/test_security/test_security_validation.py::TestInputValidationSecurity::test_sql_injection_protection": true, "tests/test_security/test_security_validation.py::TestInputValidationSecurity::test_xss_protection": true, "tests/test_security/test_security_validation.py::TestInputValidationSecurity::test_command_injection_protection": true, "tests/test_security/test_security_validation.py::TestAuthenticationSecurity::test_unauthorized_access_protection": true, "tests/test_security/test_security_validation.py::TestDataValidationSecurity::test_json_payload_size_limits": true, "tests/test_security/test_security_validation.py::TestDataValidationSecurity::test_unicode_security": true, "tests/test_security/test_security_validation.py::TestRateLimitingSecurity::test_api_rate_limiting": true, "tests/test_standards/test_ieee_515.py::TestIEEE515::test_validate_design_hazardous_area": true, "tests/test_standards/test_ieee_515.py::TestIEEE515::test_validate_design_missing_required_fields": true, "tests/test_standards/test_standards_service.py::TestStandardsService::test_validate_design_invalid_input": true, "tests/test_standards/test_standards_service.py::TestStandardsService::test_validate_design_unknown_standard": true, "tests/test_standards/test_standards_service.py::TestStandardsService::test_logging_functionality": true, "tests/test_utils.py::TestFileIOUtils::test_temporary_file": true}