["examples/phase2_integration_test.py::test_enhanced_service_methods", "examples/phase2_integration_test.py::test_json_validation", "examples/phase2_integration_test.py::test_pagination_utilities", "examples/phase2_integration_test.py::test_string_utilities_integration", "examples/phase2_integration_test.py::test_unit_conversion_integration", "tests/test_api/test_activity_log_routes.py::TestActivityLogRoutes::test_create_activity_log_success", "tests/test_api/test_activity_log_routes.py::TestActivityLogRoutes::test_database_error_handling", "tests/test_api/test_activity_log_routes.py::TestActivityLogRoutes::test_delete_old_activity_logs_success", "tests/test_api/test_activity_log_routes.py::TestActivityLogRoutes::test_get_activity_log_not_found", "tests/test_api/test_activity_log_routes.py::TestActivityLogRoutes::test_get_activity_log_success", "tests/test_api/test_activity_log_routes.py::TestActivityLogRoutes::test_get_entity_activity_logs_success", "tests/test_api/test_activity_log_routes.py::TestActivityLogRoutes::test_get_recent_activity_success", "tests/test_api/test_activity_log_routes.py::TestActivityLogRoutes::test_get_security_events_success", "tests/test_api/test_activity_log_routes.py::TestActivityLogRoutes::test_get_user_activity_logs_success", "tests/test_api/test_activity_log_routes.py::TestActivityLogRoutes::test_get_user_activity_summary_success", "tests/test_api/test_activity_log_routes.py::TestActivityLogRoutes::test_invalid_input_error_handling", "tests/test_api/test_activity_log_routes.py::TestActivityLogRoutes::test_list_activity_logs_success", "tests/test_api/test_activity_log_routes.py::TestActivityLogRoutes::test_list_activity_logs_with_filters", "tests/test_api/test_activity_log_routes.py::TestActivityLogRoutes::test_log_security_event_success", "tests/test_api/test_activity_log_routes.py::TestActivityLogRoutes::test_search_activity_logs_success", "tests/test_api/test_activity_log_routes.py::TestActivityLogRoutes::test_update_activity_log_success", "tests/test_api/test_activity_log_routes.py::TestActivityLogRoutes::test_validation_error_handling", "tests/test_api/test_calculations_routes.py::TestCalculationsRoutes::test_calculate_cable_selection_valid_request", "tests/test_api/test_calculations_routes.py::TestCalculationsRoutes::test_calculate_electrical_load_valid_request", "tests/test_api/test_calculations_routes.py::TestCalculationsRoutes::test_calculate_heat_tracing_invalid_request", "tests/test_api/test_calculations_routes.py::TestCalculationsRoutes::test_calculate_heat_tracing_large_dataset", "tests/test_api/test_calculations_routes.py::TestCalculationsRoutes::test_calculate_heat_tracing_missing_auth", "tests/test_api/test_calculations_routes.py::TestCalculationsRoutes::test_calculate_heat_tracing_valid_request", "tests/test_api/test_calculations_routes.py::TestCalculationsRoutes::test_calculate_power_requirements_valid_request", "tests/test_api/test_calculations_routes.py::TestCalculationsRoutes::test_calculate_with_performance_monitoring", "tests/test_api/test_calculations_routes.py::TestCalculationsRoutes::test_calculations_health_check", "tests/test_api/test_calculations_routes.py::TestCalculationsRoutes::test_calculations_routes_without_auth", "tests/test_api/test_calculations_routes.py::TestCalculationsRoutes::test_error_handling_invalid_json", "tests/test_api/test_calculations_routes.py::TestCalculationsRoutes::test_error_handling_missing_fields", "tests/test_api/test_calculations_routes.py::TestCalculationsRoutes::test_get_calculation_history", "tests/test_api/test_calculations_routes.py::TestCalculationsRoutes::test_get_calculation_history_filtered", "tests/test_api/test_calculations_routes.py::TestCalculationsRoutes::test_get_calculation_template_specific", "tests/test_api/test_calculations_routes.py::TestCalculationsRoutes::test_get_calculation_templates", "tests/test_api/test_calculations_routes.py::TestCalculationsRoutes::test_validate_calculation_input", "tests/test_api/test_component_routes.py::TestComponentCategoryRoutes::test_create_category_success", "tests/test_api/test_component_routes.py::TestComponentCategoryRoutes::test_get_category_success", "tests/test_api/test_component_routes.py::TestComponentCategoryRoutes::test_list_categories_success", "tests/test_api/test_component_routes.py::TestComponentRoutes::test_create_component_duplicate_error", "tests/test_api/test_component_routes.py::TestComponentRoutes::test_create_component_success", "tests/test_api/test_component_routes.py::TestComponentRoutes::test_create_component_validation_error", "tests/test_api/test_component_routes.py::TestComponentRoutes::test_delete_component_success", "tests/test_api/test_component_routes.py::TestComponentRoutes::test_get_component_not_found", "tests/test_api/test_component_routes.py::TestComponentRoutes::test_get_component_success", "tests/test_api/test_component_routes.py::TestComponentRoutes::test_list_components_success", "tests/test_api/test_component_routes.py::TestComponentRoutes::test_list_components_with_filters", "tests/test_api/test_component_routes.py::TestComponentRoutes::test_update_component_success", "tests/test_api/test_component_routes_error_handling.py::TestComponentCategoryErrorHandling::test_create_category_circular_dependency", "tests/test_api/test_component_routes_error_handling.py::TestComponentCategoryErrorHandling::test_delete_category_with_subcategories", "tests/test_api/test_component_routes_error_handling.py::TestComponentCategoryErrorHandling::test_update_category_invalid_parent_hierarchy", "tests/test_api/test_component_routes_error_handling.py::TestComponentErrorHandling::test_create_component_database_error", "tests/test_api/test_component_routes_error_handling.py::TestComponentErrorHandling::test_create_component_extremely_large_payload", "tests/test_api/test_component_routes_error_handling.py::TestComponentErrorHandling::test_create_component_integrity_constraint_violation", "tests/test_api/test_component_routes_error_handling.py::TestComponentErrorHandling::test_create_component_malformed_json_data", "tests/test_api/test_component_routes_error_handling.py::TestComponentErrorHandling::test_delete_component_with_dependencies", "tests/test_api/test_component_routes_error_handling.py::TestComponentErrorHandling::test_get_component_service_unavailable", "tests/test_api/test_component_routes_error_handling.py::TestComponentErrorHandling::test_list_components_database_timeout", "tests/test_api/test_component_routes_error_handling.py::TestComponentErrorHandling::test_search_components_with_sql_injection_attempt", "tests/test_api/test_component_routes_error_handling.py::TestComponentErrorHandling::test_update_component_concurrent_modification", "tests/test_api/test_component_routes_error_handling.py::TestComponentValidationEdgeCases::test_component_name_unicode_characters", "tests/test_api/test_component_routes_error_handling.py::TestComponentValidationEdgeCases::test_component_specific_data_edge_cases", "tests/test_api/test_document_routes.py::TestCalculationStandardRoutes::test_calculation_standard_validation_errors", "tests/test_api/test_document_routes.py::TestCalculationStandardRoutes::test_create_calculation_standard", "tests/test_api/test_document_routes.py::TestCalculationStandardRoutes::test_create_calculation_standard_duplicate_code", "tests/test_api/test_document_routes.py::TestCalculationStandardRoutes::test_delete_calculation_standard", "tests/test_api/test_document_routes.py::TestCalculationStandardRoutes::test_get_calculation_standard_by_code", "tests/test_api/test_document_routes.py::TestCalculationStandardRoutes::test_get_calculation_standard_by_id", "tests/test_api/test_document_routes.py::TestCalculationStandardRoutes::test_get_nonexistent_calculation_standard", "tests/test_api/test_document_routes.py::TestCalculationStandardRoutes::test_list_calculation_standards", "tests/test_api/test_document_routes.py::TestCalculationStandardRoutes::test_update_calculation_standard", "tests/test_api/test_document_routes.py::TestExportedDocumentRoutes::test_create_exported_document", "tests/test_api/test_document_routes.py::TestExportedDocumentRoutes::test_list_exported_documents_by_project", "tests/test_api/test_document_routes.py::TestImportedDataRevisionRoutes::test_create_imported_data_revision", "tests/test_api/test_document_routes.py::TestImportedDataRevisionRoutes::test_list_imported_data_revisions_by_project", "tests/test_api/test_electrical_routes.py::TestCableRouteRoutes::test_create_cable_route_success", "tests/test_api/test_electrical_routes.py::TestCableRouteRoutes::test_get_cable_route_success", "tests/test_api/test_electrical_routes.py::TestCableRouteRoutes::test_list_cable_routes_success", "tests/test_api/test_electrical_routes.py::TestElectricalBusinessLogicRoutes::test_design_workflow_success", "tests/test_api/test_electrical_routes.py::TestElectricalBusinessLogicRoutes::test_node_load_summary_success", "tests/test_api/test_electrical_routes.py::TestElectricalBusinessLogicRoutes::test_optimize_cable_route_success", "tests/test_api/test_electrical_routes.py::TestElectricalCalculationRoutes::test_cable_sizing_calculation_success", "tests/test_api/test_electrical_routes.py::TestElectricalCalculationRoutes::test_standards_validation_success", "tests/test_api/test_electrical_routes.py::TestElectricalCalculationRoutes::test_voltage_drop_calculation_success", "tests/test_api/test_electrical_routes.py::TestElectricalNodeRoutes::test_create_electrical_node_success", "tests/test_api/test_electrical_routes.py::TestElectricalNodeRoutes::test_create_electrical_node_validation_error", "tests/test_api/test_electrical_routes.py::TestElectricalNodeRoutes::test_delete_electrical_node_success", "tests/test_api/test_electrical_routes.py::TestElectricalNodeRoutes::test_get_electrical_node_not_found", "tests/test_api/test_electrical_routes.py::TestElectricalNodeRoutes::test_get_electrical_node_success", "tests/test_api/test_electrical_routes.py::TestElectricalNodeRoutes::test_list_electrical_nodes_success", "tests/test_api/test_electrical_routes.py::TestElectricalNodeRoutes::test_update_electrical_node_success", "tests/test_api/test_electrical_routes.py::TestLoadCalculationRoutes::test_create_load_calculation_success", "tests/test_api/test_electrical_routes.py::TestLoadCalculationRoutes::test_get_load_calculation_success", "tests/test_api/test_electrical_routes.py::TestLoadCalculationRoutes::test_list_load_calculations_success", "tests/test_api/test_electrical_routes_edge_cases.py::TestCableRouteEdgeCases::test_create_cable_route_extreme_lengths", "tests/test_api/test_electrical_routes_edge_cases.py::TestCableRouteEdgeCases::test_create_cable_route_same_nodes", "tests/test_api/test_electrical_routes_edge_cases.py::TestElectricalCalculationEdgeCases::test_cable_sizing_calculation_extreme_values", "tests/test_api/test_electrical_routes_edge_cases.py::TestElectricalNodeEdgeCases::test_create_electrical_node_boundary_values", "tests/test_api/test_electrical_routes_edge_cases.py::TestElectricalNodeEdgeCases::test_create_electrical_node_extreme_string_lengths", "tests/test_api/test_electrical_routes_edge_cases.py::TestElectricalNodeEdgeCases::test_create_electrical_node_invalid_enum_values", "tests/test_api/test_electrical_routes_edge_cases.py::TestElectricalNodeEdgeCases::test_get_electrical_node_invalid_ids", "tests/test_api/test_electrical_routes_edge_cases.py::TestElectricalNodeEdgeCases::test_list_electrical_nodes_pagination_edge_cases", "tests/test_api/test_heat_tracing_routes.py::TestCalculationEndpoints::test_calculate_pipe_heat_loss_success", "tests/test_api/test_heat_tracing_routes.py::TestCalculationEndpoints::test_validate_standards_compliance_success", "tests/test_api/test_heat_tracing_routes.py::TestDesignWorkflowEndpoints::test_execute_design_workflow_success", "tests/test_api/test_heat_tracing_routes.py::TestPipeEndpoints::test_create_pipe_duplicate_error", "tests/test_api/test_heat_tracing_routes.py::TestPipeEndpoints::test_create_pipe_success", "tests/test_api/test_heat_tracing_routes.py::TestPipeEndpoints::test_create_pipe_validation_error", "tests/test_api/test_heat_tracing_routes.py::TestPipeEndpoints::test_delete_pipe_success", "tests/test_api/test_heat_tracing_routes.py::TestPipeEndpoints::test_get_pipe_not_found", "tests/test_api/test_heat_tracing_routes.py::TestPipeEndpoints::test_get_pipe_success", "tests/test_api/test_heat_tracing_routes.py::TestPipeEndpoints::test_list_pipes_success", "tests/test_api/test_heat_tracing_routes.py::TestPipeEndpoints::test_update_pipe_success", "tests/test_api/test_heat_tracing_routes.py::TestVesselEndpoints::test_create_vessel_success", "tests/test_api/test_heat_tracing_routes.py::TestVesselEndpoints::test_get_vessel_success", "tests/test_api/test_project_routes.py::TestProjectRoutes::test_create_project_duplicate_error", "tests/test_api/test_project_routes.py::TestProjectRoutes::test_create_project_success", "tests/test_api/test_project_routes.py::TestProjectRoutes::test_create_project_validation_error", "tests/test_api/test_project_routes.py::TestProjectRoutes::test_delete_project_not_found", "tests/test_api/test_project_routes.py::TestProjectRoutes::test_delete_project_success", "tests/test_api/test_project_routes.py::TestProjectRoutes::test_get_project_by_code_success", "tests/test_api/test_project_routes.py::TestProjectRoutes::test_get_project_by_id_success", "tests/test_api/test_project_routes.py::TestProjectRoutes::test_get_project_not_found", "tests/test_api/test_project_routes.py::TestProjectRoutes::test_invalid_pagination_parameters", "tests/test_api/test_project_routes.py::TestProjectRoutes::test_list_projects_include_deleted", "tests/test_api/test_project_routes.py::TestProjectRoutes::test_list_projects_success", "tests/test_api/test_project_routes.py::TestProjectRoutes::test_list_projects_with_pagination", "tests/test_api/test_project_routes.py::TestProjectRoutes::test_update_project_not_found", "tests/test_api/test_project_routes.py::TestProjectRoutes::test_update_project_success", "tests/test_api/test_project_routes.py::TestProjectRoutesWithEnhancedDI::test_create_project_with_dependency_override_manager", "tests/test_api/test_project_routes.py::TestProjectRoutesWithEnhancedDI::test_list_projects_with_pagination_utils", "tests/test_api/test_project_routes.py::TestProjectRoutesWithEnhancedDI::test_service_dependency_injection_chain", "tests/test_api/test_standards_routes.py::TestStandardsRoutes::test_calculate_parameters_invalid_input", "tests/test_api/test_standards_routes.py::TestStandardsRoutes::test_calculate_parameters_multiple_standards", "tests/test_api/test_standards_routes.py::TestStandardsRoutes::test_calculate_standards_parameters", "tests/test_api/test_standards_routes.py::TestStandardsRoutes::test_calculate_with_specific_standard", "tests/test_api/test_standards_routes.py::TestStandardsRoutes::test_error_handling_invalid_json", "tests/test_api/test_standards_routes.py::TestStandardsRoutes::test_get_available_standards", "tests/test_api/test_standards_routes.py::TestStandardsRoutes::test_get_compliance_summary", "tests/test_api/test_standards_routes.py::TestStandardsRoutes::test_get_compliance_summary_filtered", "tests/test_api/test_standards_routes.py::TestStandardsRoutes::test_get_recommendations_hazardous_area", "tests/test_api/test_standards_routes.py::TestStandardsRoutes::test_get_recommendations_offshore", "tests/test_api/test_standards_routes.py::TestStandardsRoutes::test_get_standard_information", "tests/test_api/test_standards_routes.py::TestStandardsRoutes::test_get_standard_information_not_found", "tests/test_api/test_standards_routes.py::TestStandardsRoutes::test_get_standards_recommendations", "tests/test_api/test_standards_routes.py::TestStandardsRoutes::test_performance_multiple_standards_validation", "tests/test_api/test_standards_routes.py::TestStandardsRoutes::test_standards_health_check", "tests/test_api/test_standards_routes.py::TestStandardsRoutes::test_standards_routes_without_auth", "tests/test_api/test_standards_routes.py::TestStandardsRoutes::test_validate_against_specific_standard", "tests/test_api/test_standards_routes.py::TestStandardsRoutes::test_validate_design_against_standards", "tests/test_api/test_standards_routes.py::TestStandardsRoutes::test_validate_design_invalid_input", "tests/test_api/test_standards_routes.py::TestStandardsRoutes::test_validate_design_multiple_standards", "tests/test_api/test_standards_routes.py::TestStandardsRoutes::test_validate_design_unknown_standard", "tests/test_api/test_switchboard_api.py::TestSwitchboardAPI::test_add_switchboard_component_success", "tests/test_api/test_switchboard_api.py::TestSwitchboardAPI::test_business_logic_error_handling", "tests/test_api/test_switchboard_api.py::TestSwitchboardAPI::test_create_feeder_success", "tests/test_api/test_switchboard_api.py::TestSwitchboardAPI::test_create_switchboard_invalid_data", "tests/test_api/test_switchboard_api.py::TestSwitchboardAPI::test_create_switchboard_success", "tests/test_api/test_switchboard_api.py::TestSwitchboardAPI::test_delete_switchboard_success", "tests/test_api/test_switchboard_api.py::TestSwitchboardAPI::test_get_capacity_analysis_success", "tests/test_api/test_switchboard_api.py::TestSwitchboardAPI::test_get_feeder_success", "tests/test_api/test_switchboard_api.py::TestSwitchboardAPI::test_get_load_summary_success", "tests/test_api/test_switchboard_api.py::TestSwitchboardAPI::test_get_switchboard_not_found", "tests/test_api/test_switchboard_api.py::TestSwitchboardAPI::test_get_switchboard_success", "tests/test_api/test_switchboard_api.py::TestSwitchboardAPI::test_list_switchboards_success", "tests/test_api/test_switchboard_api.py::TestSwitchboardAPI::test_update_switchboard_success", "tests/test_api/test_user_api.py::TestUserAuthenticationAPI::test_change_password_invalid_current", "tests/test_api/test_user_api.py::TestUserAuthenticationAPI::test_change_password_success", "tests/test_api/test_user_api.py::TestUserAuthenticationAPI::test_login_invalid_credentials", "tests/test_api/test_user_api.py::TestUserAuthenticationAPI::test_login_success", "tests/test_api/test_user_api.py::TestUserAuthenticationAPI::test_logout_success", "tests/test_api/test_user_api.py::TestUserManagementAPI::test_create_user_email_exists", "tests/test_api/test_user_api.py::TestUserManagementAPI::test_create_user_success", "tests/test_api/test_user_api.py::TestUserManagementAPI::test_deactivate_user_success", "tests/test_api/test_user_api.py::TestUserManagementAPI::test_get_user_not_found", "tests/test_api/test_user_api.py::TestUserManagementAPI::test_get_user_success", "tests/test_api/test_user_api.py::TestUserManagementAPI::test_list_users_success", "tests/test_api/test_user_api.py::TestUserManagementAPI::test_list_users_with_search", "tests/test_api/test_user_api.py::TestUserManagementAPI::test_update_user_success", "tests/test_api/test_user_api.py::TestUserPreferencesAPI::test_delete_user_preferences_success", "tests/test_api/test_user_api.py::TestUserPreferencesAPI::test_get_user_preferences_not_found", "tests/test_api/test_user_api.py::TestUserPreferencesAPI::test_get_user_preferences_success", "tests/test_api/test_user_api.py::TestUserPreferencesAPI::test_update_user_preferences_success", "tests/test_calculations/test_calculation_service.py::TestCalculationService::test_calculate_cost_estimation", "tests/test_calculations/test_calculation_service.py::TestCalculationService::test_calculate_electrical_system_valid_input", "tests/test_calculations/test_calculation_service.py::TestCalculationService::test_calculate_energy_analysis", "tests/test_calculations/test_calculation_service.py::TestCalculationService::test_calculate_heat_tracing_system_empty_circuits", "tests/test_calculations/test_calculation_service.py::TestCalculationService::test_calculate_heat_tracing_system_invalid_input", "tests/test_calculations/test_calculation_service.py::TestCalculationService::test_calculate_heat_tracing_system_single_circuit", "tests/test_calculations/test_calculation_service.py::TestCalculationService::test_calculate_heat_tracing_system_valid_input", "tests/test_calculations/test_calculation_service.py::TestCalculationService::test_calculate_load_analysis", "tests/test_calculations/test_calculation_service.py::TestCalculationService::test_calculate_parallel_processing", "tests/test_calculations/test_calculation_service.py::TestCalculationService::test_calculate_system_summary", "tests/test_calculations/test_calculation_service.py::TestCalculationService::test_calculate_with_caching", "tests/test_calculations/test_calculation_service.py::TestCalculationService::test_calculate_with_different_safety_factors", "tests/test_calculations/test_calculation_service.py::TestCalculationService::test_calculate_with_optimization_options", "tests/test_calculations/test_calculation_service.py::TestCalculationService::test_calculate_with_validation_errors", "tests/test_calculations/test_calculation_service.py::TestCalculationService::test_error_handling_and_recovery", "tests/test_calculations/test_calculation_service.py::TestCalculationService::test_initialization", "tests/test_calculations/test_calculation_service.py::TestCalculationService::test_logging_functionality", "tests/test_calculations/test_calculation_service.py::TestCalculationService::test_performance_monitoring", "tests/test_calculations/test_heat_loss_calculator.py::TestHeatLossCalculator::test_calculate_convection_coefficient", "tests/test_calculations/test_heat_loss_calculator.py::TestHeatLossCalculator::test_calculate_heat_loss_edge_cases", "tests/test_calculations/test_heat_loss_calculator.py::TestHeatLossCalculator::test_calculate_heat_loss_missing_required_fields", "tests/test_calculations/test_heat_loss_calculator.py::TestHeatLossCalculator::test_calculate_heat_loss_multiple_layers", "tests/test_calculations/test_heat_loss_calculator.py::TestHeatLossCalculator::test_calculate_heat_loss_performance", "tests/test_calculations/test_heat_loss_calculator.py::TestHeatLossCalculator::test_calculate_heat_loss_valid_input", "tests/test_calculations/test_heat_loss_calculator.py::TestHeatLossCalculator::test_calculate_heat_loss_validation_errors", "tests/test_calculations/test_heat_loss_calculator.py::TestHeatLossCalculator::test_calculate_heat_loss_with_radiation", "tests/test_calculations/test_heat_loss_calculator.py::TestHeatLossCalculator::test_calculate_heat_loss_with_safety_factors", "tests/test_calculations/test_heat_loss_calculator.py::TestHeatLossCalculator::test_calculate_heat_loss_zero_temperature_difference", "tests/test_calculations/test_heat_loss_calculator.py::TestHeatLossCalculator::test_calculate_radiation_coefficient", "tests/test_calculations/test_heat_loss_calculator.py::TestHeatLossCalculator::test_calculate_thermal_resistance_invalid_diameter", "tests/test_calculations/test_heat_loss_calculator.py::TestHeatLossCalculator::test_calculate_thermal_resistance_valid_input", "tests/test_calculations/test_heat_loss_calculator.py::TestHeatLossCalculator::test_calculate_thermal_resistance_zero_thickness", "tests/test_calculations/test_heat_loss_calculator.py::TestHeatLossCalculator::test_initialization", "tests/test_calculations/test_heat_loss_calculator.py::TestHeatLossCalculator::test_logging_functionality", "tests/test_calculations/test_power_calculator.py::TestPowerCalculator::test_calculate_cable_derating", "tests/test_calculations/test_power_calculator.py::TestPowerCalculator::test_calculate_circuit_protection", "tests/test_calculations/test_power_calculator.py::TestPowerCalculator::test_calculate_electrical_parameters", "tests/test_calculations/test_power_calculator.py::TestPowerCalculator::test_calculate_electrical_parameters_invalid_power_factor", "tests/test_calculations/test_power_calculator.py::TestPowerCalculator::test_calculate_electrical_parameters_invalid_voltage", "tests/test_calculations/test_power_calculator.py::TestPowerCalculator::test_calculate_electrical_parameters_three_phase", "tests/test_calculations/test_power_calculator.py::TestPowerCalculator::test_calculate_energy_consumption", "tests/test_calculations/test_power_calculator.py::TestPowerCalculator::test_calculate_power_requirements_different_applications", "tests/test_calculations/test_power_calculator.py::TestPowerCalculator::test_calculate_power_requirements_invalid_input", "tests/test_calculations/test_power_calculator.py::TestPowerCalculator::test_calculate_power_requirements_valid_input", "tests/test_calculations/test_power_calculator.py::TestPowerCalculator::test_calculate_power_requirements_with_diversity", "tests/test_calculations/test_power_calculator.py::TestPowerCalculator::test_calculate_power_requirements_zero_length", "tests/test_calculations/test_power_calculator.py::TestPowerCalculator::test_calculate_startup_requirements", "tests/test_calculations/test_power_calculator.py::TestPowerCalculator::test_calculate_voltage_drop", "tests/test_calculations/test_power_calculator.py::TestPowerCalculator::test_initialization", "tests/test_calculations/test_power_calculator.py::TestPowerCalculator::test_logging_functionality", "tests/test_calculations/test_power_calculator.py::TestPowerCalculator::test_select_cable_constant_wattage", "tests/test_calculations/test_power_calculator.py::TestPowerCalculator::test_select_cable_missing_data", "tests/test_calculations/test_power_calculator.py::TestPowerCalculator::test_select_cable_self_regulating", "tests/test_import_export/test_csv_parser.py::TestCSVParser::test_convert_data_types", "tests/test_import_export/test_csv_parser.py::TestCSVParser::test_get_file_info", "tests/test_import_export/test_csv_parser.py::TestCSVParser::test_initialization", "tests/test_import_export/test_csv_parser.py::TestCSVParser::test_logging_functionality", "tests/test_import_export/test_csv_parser.py::TestCSVParser::test_parse_csv_with_custom_delimiter", "tests/test_import_export/test_csv_parser.py::TestCSVParser::test_parse_csv_with_data_type_conversion", "tests/test_import_export/test_csv_parser.py::TestCSVParser::test_parse_csv_with_empty_rows", "tests/test_import_export/test_csv_parser.py::TestCSVParser::test_parse_csv_with_encoding_issues", "tests/test_import_export/test_csv_parser.py::TestCSVParser::test_parse_csv_with_missing_headers", "tests/test_import_export/test_csv_parser.py::TestCSVParser::test_parse_csv_with_special_characters", "tests/test_import_export/test_csv_parser.py::TestCSVParser::test_parse_empty_csv_file", "tests/test_import_export/test_csv_parser.py::TestCSVParser::test_parse_invalid_csv_file", "tests/test_import_export/test_csv_parser.py::TestCSVParser::test_parse_large_csv_file", "tests/test_import_export/test_csv_parser.py::TestCSVParser::test_parse_nonexistent_file", "tests/test_import_export/test_csv_parser.py::TestCSVParser::test_parse_valid_csv_file", "tests/test_import_export/test_csv_parser.py::TestCSVParser::test_validate_data_types", "tests/test_import_export/test_csv_parser.py::TestCSVParser::test_validate_data_types_invalid", "tests/test_import_export/test_import_service.py::TestImportService::test_cleanup_temp_files", "tests/test_import_export/test_import_service.py::TestImportService::test_error_handling_invalid_data_type", "tests/test_import_export/test_import_service.py::TestImportService::test_generate_import_template", "tests/test_import_export/test_import_service.py::TestImportService::test_get_import_status", "tests/test_import_export/test_import_service.py::TestImportService::test_import_global_data_basic", "tests/test_import_export/test_import_service.py::TestImportService::test_import_history_functionality", "tests/test_import_export/test_import_service.py::TestImportService::test_import_project_data_basic", "tests/test_import_export/test_import_service.py::TestImportService::test_initialization", "tests/test_import_export/test_import_service.py::TestImportService::test_service_methods_exist", "tests/test_integration/test_electrical_integration_enhanced.py::TestCrossModuleIntegration::test_electrical_component_integration", "tests/test_integration/test_electrical_integration_enhanced.py::TestCrossModuleIntegration::test_project_electrical_integration", "tests/test_integration/test_electrical_integration_enhanced.py::TestElectricalSystemIntegration::test_create_complete_electrical_system", "tests/test_integration/test_electrical_integration_enhanced.py::TestElectricalSystemIntegration::test_electrical_calculations_integration", "tests/test_integration/test_electrical_integration_enhanced.py::TestElectricalSystemIntegration::test_electrical_load_analysis_integration", "tests/test_integration/test_electrical_integration_enhanced.py::TestElectricalSystemIntegration::test_electrical_system_validation_workflow", "tests/test_integration/test_minimal_integration.py::TestMinimalIntegration::test_default_values", "tests/test_integration/test_minimal_integration.py::TestMinimalIntegration::test_enum_values", "tests/test_integration/test_minimal_integration.py::TestMinimalIntegration::test_optional_fields", "tests/test_integration/test_minimal_integration.py::TestMinimalIntegration::test_schema_serialization", "tests/test_integration/test_minimal_integration.py::TestMinimalIntegration::test_schema_validation_errors", "tests/test_integration/test_minimal_integration.py::TestMinimalIntegration::test_switchboard_schema_with_enum", "tests/test_integration/test_minimal_integration.py::TestMinimalIntegration::test_user_schema_validation", "tests/test_integration/test_simple_document_integration.py::TestDocumentIntegration::test_calculation_standard_repository_crud", "tests/test_integration/test_simple_document_integration.py::TestDocumentIntegration::test_calculation_standard_schema_validation", "tests/test_integration/test_simple_document_integration.py::TestDocumentIntegration::test_calculation_standard_service_operations", "tests/test_integration/test_simple_document_integration.py::TestDocumentIntegration::test_database_schema_creation", "tests/test_integration/test_simple_document_integration.py::TestDocumentIntegration::test_document_models_relationships", "tests/test_performance/test_api_performance.py::TestAPIPerformance::test_cable_sizing_calculation_performance", "tests/test_performance/test_api_performance.py::TestAPIPerformance::test_component_search_performance", "tests/test_performance/test_api_performance.py::TestAPIPerformance::test_concurrent_request_performance[10]", "tests/test_performance/test_api_performance.py::TestAPIPerformance::test_concurrent_request_performance[20]", "tests/test_performance/test_api_performance.py::TestAPIPerformance::test_concurrent_request_performance[5]", "tests/test_performance/test_api_performance.py::TestAPIPerformance::test_electrical_node_creation_performance", "tests/test_performance/test_api_performance.py::TestAPIPerformance::test_electrical_node_list_performance", "tests/test_performance/test_api_performance.py::TestAPIPerformance::test_memory_usage_during_large_operations", "tests/test_performance/test_api_performance.py::TestDatabasePerformance::test_bulk_insert_performance", "tests/test_performance/test_api_performance.py::TestDatabasePerformance::test_complex_query_performance", "tests/test_performance/test_load_testing.py::TestAdvancedLoadTesting::test_calculation_engine_stress", "tests/test_performance/test_load_testing.py::TestAdvancedLoadTesting::test_component_search_load", "tests/test_performance/test_load_testing.py::TestAdvancedLoadTesting::test_electrical_nodes_concurrent_creation", "tests/test_reports/test_document_generator.py::TestDocumentGenerator::test_generate_cable_schedule_report", "tests/test_reports/test_document_generator.py::TestDocumentGenerator::test_generate_dashboard_report", "tests/test_reports/test_document_generator.py::TestDocumentGenerator::test_generate_excel_report_valid_input", "tests/test_reports/test_document_generator.py::TestDocumentGenerator::test_generate_html_report_valid_input", "tests/test_reports/test_document_generator.py::TestDocumentGenerator::test_generate_multi_format_report_package", "tests/test_reports/test_document_generator.py::TestDocumentGenerator::test_generate_pdf_report_valid_input", "tests/test_reports/test_document_generator.py::TestDocumentGenerator::test_generate_report_invalid_output_format", "tests/test_reports/test_document_generator.py::TestDocumentGenerator::test_generate_report_missing_required_data", "tests/test_reports/test_document_generator.py::TestDocumentGenerator::test_generate_report_performance", "tests/test_reports/test_document_generator.py::TestDocumentGenerator::test_generate_report_with_charts", "tests/test_reports/test_document_generator.py::TestDocumentGenerator::test_generate_report_with_custom_template", "tests/test_reports/test_document_generator.py::TestDocumentGenerator::test_generate_report_with_images", "tests/test_reports/test_document_generator.py::TestDocumentGenerator::test_generate_report_with_watermark", "tests/test_reports/test_document_generator.py::TestDocumentGenerator::test_get_available_templates", "tests/test_reports/test_document_generator.py::TestDocumentGenerator::test_initialization", "tests/test_reports/test_document_generator.py::TestDocumentGenerator::test_logging_functionality", "tests/test_reports/test_document_generator.py::TestDocumentGenerator::test_validate_report_data_invalid", "tests/test_reports/test_document_generator.py::TestDocumentGenerator::test_validate_report_data_valid", "tests/test_reports/test_report_service.py::TestReportService::test_clear_cache_all", "tests/test_reports/test_report_service.py::TestReportService::test_clear_cache_specific_project", "tests/test_reports/test_report_service.py::TestReportService::test_export_data_csv_format", "tests/test_reports/test_report_service.py::TestReportService::test_export_data_excel_format", "tests/test_reports/test_report_service.py::TestReportService::test_export_data_invalid_format", "tests/test_reports/test_report_service.py::TestReportService::test_export_data_json_format", "tests/test_reports/test_report_service.py::TestReportService::test_export_data_with_filters", "tests/test_reports/test_report_service.py::TestReportService::test_generate_cable_schedule_valid_input", "tests/test_reports/test_report_service.py::TestReportService::test_generate_calculation_report_excel_format", "tests/test_reports/test_report_service.py::TestReportService::test_generate_calculation_report_html_format", "tests/test_reports/test_report_service.py::TestReportService::test_generate_calculation_report_invalid_format", "tests/test_reports/test_report_service.py::TestReportService::test_generate_calculation_report_missing_data", "tests/test_reports/test_report_service.py::TestReportService::test_generate_calculation_report_valid_input", "tests/test_reports/test_report_service.py::TestReportService::test_generate_calculation_report_with_caching", "tests/test_reports/test_report_service.py::TestReportService::test_generate_dashboard_valid_input", "tests/test_reports/test_report_service.py::TestReportService::test_generate_report_package_valid_input", "tests/test_reports/test_report_service.py::TestReportService::test_generate_report_performance", "tests/test_reports/test_report_service.py::TestReportService::test_get_available_templates", "tests/test_reports/test_report_service.py::TestReportService::test_get_generation_history", "tests/test_reports/test_report_service.py::TestReportService::test_get_generation_history_filtered", "tests/test_reports/test_report_service.py::TestReportService::test_initialization", "tests/test_reports/test_report_service.py::TestReportService::test_logging_functionality", "tests/test_repositories/test_activity_log_repository.py::TestActivityLogRepository::test_count_filtered_activity_logs", "tests/test_repositories/test_activity_log_repository.py::TestActivityLogRepository::test_database_error_handling", "tests/test_repositories/test_activity_log_repository.py::TestActivityLogRepository::test_delete_old_logs", "tests/test_repositories/test_activity_log_repository.py::TestActivityLogRepository::test_empty_results", "tests/test_repositories/test_activity_log_repository.py::TestActivityLogRepository::test_filter_activity_logs", "tests/test_repositories/test_activity_log_repository.py::TestActivityLogRepository::test_get_by_date_range", "tests/test_repositories/test_activity_log_repository.py::TestActivityLogRepository::test_get_by_entity", "tests/test_repositories/test_activity_log_repository.py::TestActivityLogRepository::test_get_by_event_type", "tests/test_repositories/test_activity_log_repository.py::TestActivityLogRepository::test_get_by_user_id", "tests/test_repositories/test_activity_log_repository.py::TestActivityLogRepository::test_get_daily_activity_counts", "tests/test_repositories/test_activity_log_repository.py::TestActivityLogRepository::test_get_recent_activity", "tests/test_repositories/test_activity_log_repository.py::TestActivityLogRepository::test_get_security_events", "tests/test_repositories/test_activity_log_repository.py::TestActivityLogRepository::test_get_unique_entities_count", "tests/test_repositories/test_activity_log_repository.py::TestActivityLogRepository::test_get_unique_users_count", "tests/test_repositories/test_activity_log_repository.py::TestActivityLogRepository::test_get_user_activity_summary", "tests/test_repositories/test_activity_log_repository.py::TestActivityLogRepository::test_repository_initialization", "tests/test_repositories/test_activity_log_repository.py::TestActivityLogRepository::test_search_by_details", "tests/test_repositories/test_document_repository.py::TestCalculationStandardRepository::test_create_calculation_standard", "tests/test_repositories/test_document_repository.py::TestCalculationStandardRepository::test_get_by_name", "tests/test_repositories/test_document_repository.py::TestCalculationStandardRepository::test_get_by_standard_code", "tests/test_repositories/test_document_repository.py::TestCalculationStandardRepository::test_search_by_name_or_code", "tests/test_repositories/test_document_repository.py::TestExportedDocumentRepository::test_create_exported_document", "tests/test_repositories/test_document_repository.py::TestExportedDocumentRepository::test_get_latest_by_document_type", "tests/test_repositories/test_document_repository.py::TestExportedDocumentRepository::test_mark_others_as_not_latest", "tests/test_repositories/test_document_repository.py::TestImportedDataRevisionRepository::test_create_imported_data_revision", "tests/test_repositories/test_document_repository.py::TestImportedDataRevisionRepository::test_deactivate_other_revisions", "tests/test_repositories/test_document_repository.py::TestImportedDataRevisionRepository::test_get_active_revision_by_filename", "tests/test_repositories/test_document_repository.py::TestImportedDataRevisionRepository::test_get_by_project_id", "tests/test_repositories/test_electrical_repository.py::TestCableRouteRepository::test_get_routes_by_installation_method_success", "tests/test_repositories/test_electrical_repository.py::TestCableRouteRepository::test_get_routes_by_node_both_directions", "tests/test_repositories/test_electrical_repository.py::TestCableRouteRepository::test_get_routes_by_node_invalid_direction", "tests/test_repositories/test_electrical_repository.py::TestCableRouteRepository::test_get_routes_with_high_voltage_drop_success", "tests/test_repositories/test_electrical_repository.py::TestElectricalNodeRepository::test_count_by_project_none_result", "tests/test_repositories/test_electrical_repository.py::TestElectricalNodeRepository::test_count_by_project_success", "tests/test_repositories/test_electrical_repository.py::TestElectricalNodeRepository::test_get_by_node_type_success", "tests/test_repositories/test_electrical_repository.py::TestElectricalNodeRepository::test_get_by_project_id_database_error", "tests/test_repositories/test_electrical_repository.py::TestElectricalNodeRepository::test_get_by_project_id_success", "tests/test_repositories/test_electrical_repository.py::TestElectricalNodeRepository::test_get_nodes_with_capacity_success", "tests/test_repositories/test_electrical_repository.py::TestLoadCalculationRepository::test_get_by_electrical_node_id_success", "tests/test_repositories/test_electrical_repository.py::TestLoadCalculationRepository::test_get_by_load_type_success", "tests/test_repositories/test_electrical_repository.py::TestLoadCalculationRepository::test_get_total_power_by_node_none_result", "tests/test_repositories/test_electrical_repository.py::TestLoadCalculationRepository::test_get_total_power_by_node_success", "tests/test_repositories/test_electrical_repository.py::TestVoltageDropCalculationRepository::test_count_by_compliance_status_success", "tests/test_repositories/test_electrical_repository.py::TestVoltageDropCalculationRepository::test_get_average_voltage_drop_by_project_success", "tests/test_repositories/test_electrical_repository.py::TestVoltageDropCalculationRepository::test_get_by_cable_route_id_success", "tests/test_repositories/test_electrical_repository.py::TestVoltageDropCalculationRepository::test_get_non_compliant_calculations_success", "tests/test_repositories/test_heat_tracing_repository.py::TestControlCircuitRepository::test_create_control_circuit_success", "tests/test_repositories/test_heat_tracing_repository.py::TestControlCircuitRepository::test_get_by_switchboard_id", "tests/test_repositories/test_heat_tracing_repository.py::TestControlCircuitRepository::test_get_with_limiting_function", "tests/test_repositories/test_heat_tracing_repository.py::TestHTCircuitRepository::test_create_htcircuit_success", "tests/test_repositories/test_heat_tracing_repository.py::TestHTCircuitRepository::test_get_by_feeder_id", "tests/test_repositories/test_heat_tracing_repository.py::TestHTCircuitRepository::test_get_by_pipe_id", "tests/test_repositories/test_heat_tracing_repository.py::TestHTCircuitRepository::test_get_total_feeder_load", "tests/test_repositories/test_heat_tracing_repository.py::TestHTCircuitRepository::test_update_load_calculation", "tests/test_repositories/test_heat_tracing_repository.py::TestHeatTracingRepository::test_get_design_readiness_empty", "tests/test_repositories/test_heat_tracing_repository.py::TestHeatTracingRepository::test_get_project_summary_empty", "tests/test_repositories/test_heat_tracing_repository.py::TestHeatTracingRepository::test_initialization", "tests/test_repositories/test_heat_tracing_repository.py::TestPipeRepository::test_count_by_project", "tests/test_repositories/test_heat_tracing_repository.py::TestPipeRepository::test_create_pipe_success", "tests/test_repositories/test_heat_tracing_repository.py::TestPipeRepository::test_get_pipe_by_id_not_found", "tests/test_repositories/test_heat_tracing_repository.py::TestPipeRepository::test_get_pipe_by_id_success", "tests/test_repositories/test_heat_tracing_repository.py::TestPipeRepository::test_get_pipe_by_line_tag", "tests/test_repositories/test_heat_tracing_repository.py::TestPipeRepository::test_get_pipe_by_line_tag_not_found", "tests/test_repositories/test_heat_tracing_repository.py::TestPipeRepository::test_get_pipes_by_project_id", "tests/test_repositories/test_heat_tracing_repository.py::TestPipeRepository::test_get_pipes_without_circuits", "tests/test_repositories/test_heat_tracing_repository.py::TestPipeRepository::test_get_with_heat_loss_calculations", "tests/test_repositories/test_heat_tracing_repository.py::TestPipeRepository::test_update_heat_loss_calculation", "tests/test_repositories/test_heat_tracing_repository.py::TestPipeRepository::test_update_heat_loss_calculation_not_found", "tests/test_repositories/test_heat_tracing_repository.py::TestVesselRepository::test_create_vessel_success", "tests/test_repositories/test_heat_tracing_repository.py::TestVesselRepository::test_get_vessel_by_equipment_tag", "tests/test_repositories/test_heat_tracing_repository.py::TestVesselRepository::test_get_vessels_without_circuits", "tests/test_repositories/test_heat_tracing_repository.py::TestVesselRepository::test_update_heat_loss_calculation", "tests/test_repositories/test_project_repository.py::TestProjectRepository::test_count_active_projects", "tests/test_repositories/test_project_repository.py::TestProjectRepository::test_count_active_projects_empty_db", "tests/test_repositories/test_project_repository.py::TestProjectRepository::test_create_project", "tests/test_repositories/test_project_repository.py::TestProjectRepository::test_get_active_projects", "tests/test_repositories/test_project_repository.py::TestProjectRepository::test_get_active_projects_with_pagination", "tests/test_repositories/test_project_repository.py::TestProjectRepository::test_get_all_projects", "tests/test_repositories/test_project_repository.py::TestProjectRepository::test_get_all_with_pagination", "tests/test_repositories/test_project_repository.py::TestProjectRepository::test_get_by_code_existing", "tests/test_repositories/test_project_repository.py::TestProjectRepository::test_get_by_code_nonexistent", "tests/test_repositories/test_project_repository.py::TestProjectRepository::test_get_by_id_existing", "tests/test_repositories/test_project_repository.py::TestProjectRepository::test_get_by_id_nonexistent", "tests/test_repositories/test_project_repository.py::TestProjectRepository::test_get_by_name_existing", "tests/test_repositories/test_project_repository.py::TestProjectRepository::test_get_by_name_nonexistent", "tests/test_repositories/test_project_repository.py::TestProjectRepository::test_get_project_with_related_data", "tests/test_repositories/test_project_repository.py::TestProjectRepository::test_get_project_with_related_data_nonexistent", "tests/test_repositories/test_project_repository.py::TestProjectRepository::test_search_projects_by_description", "tests/test_repositories/test_project_repository.py::TestProjectRepository::test_search_projects_by_name", "tests/test_repositories/test_project_repository.py::TestProjectRepository::test_search_projects_by_project_number", "tests/test_repositories/test_project_repository.py::TestProjectRepository::test_search_projects_case_insensitive", "tests/test_repositories/test_project_repository.py::TestProjectRepository::test_search_projects_excludes_deleted", "tests/test_repositories/test_project_repository.py::TestProjectRepository::test_search_projects_with_pagination", "tests/test_repositories/test_project_repository.py::TestProjectRepository::test_soft_delete_already_deleted_project", "tests/test_repositories/test_project_repository.py::TestProjectRepository::test_soft_delete_nonexistent_project", "tests/test_repositories/test_project_repository.py::TestProjectRepository::test_soft_delete_project", "tests/test_repositories/test_project_repository.py::TestProjectRepository::test_unique_constraint_name", "tests/test_repositories/test_project_repository.py::TestProjectRepository::test_unique_constraint_project_number", "tests/test_repositories/test_project_repository.py::TestProjectRepository::test_update_nonexistent_project", "tests/test_repositories/test_project_repository.py::TestProjectRepository::test_update_project", "tests/test_schemas/test_activity_log_schemas.py::TestActivityLogBaseSchema::test_entity_consistency_validation", "tests/test_schemas/test_activity_log_schemas.py::TestActivityLogBaseSchema::test_invalid_json_details_validation", "tests/test_schemas/test_activity_log_schemas.py::TestActivityLogBaseSchema::test_json_details_validation", "tests/test_schemas/test_activity_log_schemas.py::TestActivityLogBaseSchema::test_minimal_activity_log_base", "tests/test_schemas/test_activity_log_schemas.py::TestActivityLogBaseSchema::test_valid_activity_log_base", "tests/test_schemas/test_activity_log_schemas.py::TestActivityLogCreateSchema::test_system_event_without_user", "tests/test_schemas/test_activity_log_schemas.py::TestActivityLogCreateSchema::test_valid_activity_log_create", "tests/test_schemas/test_activity_log_schemas.py::TestActivityLogFilterSchema::test_date_range_validation", "tests/test_schemas/test_activity_log_schemas.py::TestActivityLogFilterSchema::test_minimal_filter_schema", "tests/test_schemas/test_activity_log_schemas.py::TestActivityLogFilterSchema::test_valid_filter_schema", "tests/test_schemas/test_activity_log_schemas.py::TestAuditReportRequestSchema::test_date_range_too_large", "tests/test_schemas/test_activity_log_schemas.py::TestAuditReportRequestSchema::test_invalid_report_type", "tests/test_schemas/test_activity_log_schemas.py::TestAuditReportRequestSchema::test_valid_audit_report_request", "tests/test_schemas/test_activity_log_schemas.py::TestEventCategoryMappingSchema::test_event_category_mapping", "tests/test_schemas/test_activity_log_schemas.py::TestEventEnums::test_entity_type_enum_values", "tests/test_schemas/test_activity_log_schemas.py::TestEventEnums::test_event_category_enum_values", "tests/test_schemas/test_activity_log_schemas.py::TestEventEnums::test_event_type_enum_values", "tests/test_schemas/test_activity_log_schemas.py::TestSecurityEventSchema::test_invalid_severity", "tests/test_schemas/test_activity_log_schemas.py::TestSecurityEventSchema::test_invalid_threat_level", "tests/test_schemas/test_activity_log_schemas.py::TestSecurityEventSchema::test_valid_security_event", "tests/test_schemas/test_component_schemas.py::TestComponentCategoryCreateSchema::test_category_name_normalization", "tests/test_schemas/test_component_schemas.py::TestComponentCategoryCreateSchema::test_category_name_validation_empty", "tests/test_schemas/test_component_schemas.py::TestComponentCategoryCreateSchema::test_category_name_validation_whitespace", "tests/test_schemas/test_component_schemas.py::TestComponentCategoryCreateSchema::test_category_with_parent", "tests/test_schemas/test_component_schemas.py::TestComponentCategoryCreateSchema::test_minimal_category_creation", "tests/test_schemas/test_component_schemas.py::TestComponentCategoryCreateSchema::test_valid_category_creation", "tests/test_schemas/test_component_schemas.py::TestComponentCategoryUpdateSchema::test_category_update_validation_same_as_create", "tests/test_schemas/test_component_schemas.py::TestComponentCategoryUpdateSchema::test_empty_category_update", "tests/test_schemas/test_component_schemas.py::TestComponentCategoryUpdateSchema::test_valid_partial_category_update", "tests/test_schemas/test_component_schemas.py::TestComponentCreateSchema::test_category_id_validation", "tests/test_schemas/test_component_schemas.py::TestComponentCreateSchema::test_minimal_component_creation", "tests/test_schemas/test_component_schemas.py::TestComponentCreateSchema::test_name_normalization", "tests/test_schemas/test_component_schemas.py::TestComponentCreateSchema::test_name_validation_empty", "tests/test_schemas/test_component_schemas.py::TestComponentCreateSchema::test_name_validation_whitespace", "tests/test_schemas/test_component_schemas.py::TestComponentCreateSchema::test_specific_data_empty_string", "tests/test_schemas/test_component_schemas.py::TestComponentCreateSchema::test_specific_data_json_validation_invalid", "tests/test_schemas/test_component_schemas.py::TestComponentCreateSchema::test_specific_data_json_validation_valid", "tests/test_schemas/test_component_schemas.py::TestComponentCreateSchema::test_specific_data_whitespace_only", "tests/test_schemas/test_component_schemas.py::TestComponentCreateSchema::test_valid_component_creation", "tests/test_schemas/test_component_schemas.py::TestComponentListResponseSchema::test_empty_list_response", "tests/test_schemas/test_component_schemas.py::TestComponentListResponseSchema::test_list_response_structure", "tests/test_schemas/test_component_schemas.py::TestComponentReadSchema::test_read_schema_includes_all_fields", "tests/test_schemas/test_component_schemas.py::TestComponentUpdateSchema::test_empty_update", "tests/test_schemas/test_component_schemas.py::TestComponentUpdateSchema::test_update_validation_same_as_create", "tests/test_schemas/test_component_schemas.py::TestComponentUpdateSchema::test_valid_partial_update", "tests/test_schemas/test_document_schemas.py::TestCalculationStandardSchemas::test_parameters_json_validation_invalid_json", "tests/test_schemas/test_document_schemas.py::TestCalculationStandardSchemas::test_parameters_json_validation_not_object", "tests/test_schemas/test_document_schemas.py::TestCalculationStandardSchemas::test_parameters_json_validation_valid_object", "tests/test_schemas/test_document_schemas.py::TestCalculationStandardSchemas::test_standard_code_normalization", "tests/test_schemas/test_document_schemas.py::TestCalculationStandardSchemas::test_standard_code_validation_empty", "tests/test_schemas/test_document_schemas.py::TestCalculationStandardSchemas::test_standard_code_validation_invalid_chars", "tests/test_schemas/test_document_schemas.py::TestCalculationStandardSchemas::test_standard_name_validation_empty", "tests/test_schemas/test_document_schemas.py::TestCalculationStandardSchemas::test_valid_calculation_standard_creation", "tests/test_schemas/test_document_schemas.py::TestEnums::test_document_type_enum_values", "tests/test_schemas/test_document_schemas.py::TestEnums::test_file_format_enum_values", "tests/test_schemas/test_document_schemas.py::TestEnums::test_import_type_enum_values", "tests/test_schemas/test_document_schemas.py::TestExportedDocumentSchemas::test_document_filename_validation_empty", "tests/test_schemas/test_document_schemas.py::TestExportedDocumentSchemas::test_document_filename_validation_invalid_extension", "tests/test_schemas/test_document_schemas.py::TestExportedDocumentSchemas::test_document_filename_validation_valid_extensions", "tests/test_schemas/test_document_schemas.py::TestExportedDocumentSchemas::test_valid_exported_document_creation", "tests/test_schemas/test_document_schemas.py::TestImportedDataRevisionSchemas::test_filename_validation_empty", "tests/test_schemas/test_document_schemas.py::TestImportedDataRevisionSchemas::test_filename_validation_invalid_extension", "tests/test_schemas/test_document_schemas.py::TestImportedDataRevisionSchemas::test_filename_validation_valid_extensions", "tests/test_schemas/test_document_schemas.py::TestImportedDataRevisionSchemas::test_revision_identifier_validation_invalid_chars", "tests/test_schemas/test_document_schemas.py::TestImportedDataRevisionSchemas::test_revision_identifier_validation_valid_chars", "tests/test_schemas/test_document_schemas.py::TestImportedDataRevisionSchemas::test_update_schema_partial_fields", "tests/test_schemas/test_document_schemas.py::TestImportedDataRevisionSchemas::test_valid_imported_data_revision_creation", "tests/test_schemas/test_electrical_schemas.py::TestCableRouteSchemas::test_cable_route_create_schema_same_nodes_validation", "tests/test_schemas/test_electrical_schemas.py::TestCableRouteSchemas::test_cable_route_create_schema_valid", "tests/test_schemas/test_electrical_schemas.py::TestCableRouteSchemas::test_cable_route_temperature_range_validation", "tests/test_schemas/test_electrical_schemas.py::TestCalculationIntegrationSchemas::test_cable_sizing_calculation_input_invalid_power", "tests/test_schemas/test_electrical_schemas.py::TestCalculationIntegrationSchemas::test_cable_sizing_calculation_input_valid", "tests/test_schemas/test_electrical_schemas.py::TestCalculationIntegrationSchemas::test_cable_sizing_calculation_result_valid", "tests/test_schemas/test_electrical_schemas.py::TestCalculationIntegrationSchemas::test_voltage_drop_calculation_input_valid", "tests/test_schemas/test_electrical_schemas.py::TestDesignWorkflowSchemas::test_electrical_design_input_schema_node_or_route_validation", "tests/test_schemas/test_electrical_schemas.py::TestDesignWorkflowSchemas::test_electrical_design_input_schema_valid", "tests/test_schemas/test_electrical_schemas.py::TestElectricalNodeSchemas::test_electrical_node_create_schema_name_validation", "tests/test_schemas/test_electrical_schemas.py::TestElectricalNodeSchemas::test_electrical_node_create_schema_valid", "tests/test_schemas/test_electrical_schemas.py::TestElectricalNodeSchemas::test_electrical_node_read_schema_from_dict", "tests/test_schemas/test_electrical_schemas.py::TestElectricalNodeSchemas::test_electrical_node_update_schema_partial", "tests/test_schemas/test_electrical_schemas.py::TestLoadCalculationSchemas::test_load_calculation_create_schema_valid", "tests/test_schemas/test_electrical_schemas.py::TestLoadCalculationSchemas::test_load_calculation_electrical_parameters_validation", "tests/test_schemas/test_heat_tracing_schemas.py::TestControlCircuitSchemas::test_control_circuit_create_schema_valid", "tests/test_schemas/test_heat_tracing_schemas.py::TestControlCircuitSchemas::test_control_circuit_limiting_setpoint_validation", "tests/test_schemas/test_heat_tracing_schemas.py::TestDesignWorkflowSchemas::test_design_input_schema_pipe_or_vessel_validation", "tests/test_schemas/test_heat_tracing_schemas.py::TestDesignWorkflowSchemas::test_design_input_schema_valid", "tests/test_schemas/test_heat_tracing_schemas.py::TestHTCircuitSchemas::test_htcircuit_create_schema_both_pipe_and_vessel_validation", "tests/test_schemas/test_heat_tracing_schemas.py::TestHTCircuitSchemas::test_htcircuit_create_schema_pipe_or_vessel_validation", "tests/test_schemas/test_heat_tracing_schemas.py::TestHTCircuitSchemas::test_htcircuit_create_schema_valid_pipe", "tests/test_schemas/test_heat_tracing_schemas.py::TestHTCircuitSchemas::test_htcircuit_create_schema_valid_vessel", "tests/test_schemas/test_heat_tracing_schemas.py::TestHeatLossCalculationSchemas::test_heat_loss_calculation_input_fluid_temp_validation", "tests/test_schemas/test_heat_tracing_schemas.py::TestHeatLossCalculationSchemas::test_heat_loss_calculation_input_valid", "tests/test_schemas/test_heat_tracing_schemas.py::TestHeatLossCalculationSchemas::test_heat_loss_calculation_result_valid", "tests/test_schemas/test_heat_tracing_schemas.py::TestPipeSchemas::test_pipe_create_schema_diameter_validation", "tests/test_schemas/test_heat_tracing_schemas.py::TestPipeSchemas::test_pipe_create_schema_name_validation", "tests/test_schemas/test_heat_tracing_schemas.py::TestPipeSchemas::test_pipe_create_schema_valid", "tests/test_schemas/test_heat_tracing_schemas.py::TestPipeSchemas::test_pipe_read_schema_from_dict", "tests/test_schemas/test_heat_tracing_schemas.py::TestPipeSchemas::test_pipe_update_schema_partial", "tests/test_schemas/test_heat_tracing_schemas.py::TestVesselSchemas::test_vessel_create_schema_dimensions_type_validation", "tests/test_schemas/test_heat_tracing_schemas.py::TestVesselSchemas::test_vessel_create_schema_dimensions_validation", "tests/test_schemas/test_heat_tracing_schemas.py::TestVesselSchemas::test_vessel_create_schema_valid", "tests/test_schemas/test_project_schemas.py::TestProjectCreateSchema::test_name_validation_empty", "tests/test_schemas/test_project_schemas.py::TestProjectCreateSchema::test_name_validation_whitespace", "tests/test_schemas/test_project_schemas.py::TestProjectCreateSchema::test_project_number_normalization", "tests/test_schemas/test_project_schemas.py::TestProjectCreateSchema::test_project_number_validation_empty", "tests/test_schemas/test_project_schemas.py::TestProjectCreateSchema::test_project_number_validation_invalid_chars", "tests/test_schemas/test_project_schemas.py::TestProjectCreateSchema::test_temperature_bounds_validation", "tests/test_schemas/test_project_schemas.py::TestProjectCreateSchema::test_temperature_range_validation", "tests/test_schemas/test_project_schemas.py::TestProjectCreateSchema::test_valid_project_creation", "tests/test_schemas/test_project_schemas.py::TestProjectCreateSchema::test_voltages_json_validation_invalid_json", "tests/test_schemas/test_project_schemas.py::TestProjectCreateSchema::test_voltages_json_validation_negative_voltage", "tests/test_schemas/test_project_schemas.py::TestProjectCreateSchema::test_voltages_json_validation_not_array", "tests/test_schemas/test_project_schemas.py::TestProjectCreateSchema::test_voltages_json_validation_valid", "tests/test_schemas/test_project_schemas.py::TestProjectCreateSchema::test_wind_speed_validation", "tests/test_schemas/test_project_schemas.py::TestProjectListResponseSchema::test_empty_list_response", "tests/test_schemas/test_project_schemas.py::TestProjectListResponseSchema::test_list_response_structure", "tests/test_schemas/test_project_schemas.py::TestProjectReadSchema::test_from_orm_conversion", "tests/test_schemas/test_project_schemas.py::TestProjectReadSchema::test_read_schema_includes_all_fields", "tests/test_schemas/test_project_schemas.py::TestProjectSummarySchema::test_summary_excludes_detailed_fields", "tests/test_schemas/test_project_schemas.py::TestProjectSummarySchema::test_summary_includes_essential_fields", "tests/test_schemas/test_project_schemas.py::TestProjectUpdateSchema::test_empty_update", "tests/test_schemas/test_project_schemas.py::TestProjectUpdateSchema::test_update_validation_same_as_create", "tests/test_schemas/test_project_schemas.py::TestProjectUpdateSchema::test_valid_partial_update", "tests/test_schemas/test_switchboard_schemas.py::TestSwitchboardSchemaEdgeCases::test_component_maximum_quantity", "tests/test_schemas/test_switchboard_schemas.py::TestSwitchboardSchemaEdgeCases::test_optional_fields_none", "tests/test_schemas/test_switchboard_schemas.py::TestSwitchboardSchemaEdgeCases::test_position_whitespace_handling", "tests/test_schemas/test_switchboard_schemas.py::TestSwitchboardSchemaEdgeCases::test_switchboard_maximum_voltage", "tests/test_schemas/test_switchboard_schemas.py::TestSwitchboardSchemaEdgeCases::test_switchboard_minimum_voltage", "tests/test_schemas/test_switchboard_schemas.py::TestSwitchboardSchemas::test_feeder_component_create_schema_valid", "tests/test_schemas/test_switchboard_schemas.py::TestSwitchboardSchemas::test_feeder_create_schema_empty_name", "tests/test_schemas/test_switchboard_schemas.py::TestSwitchboardSchemas::test_feeder_create_schema_valid", "tests/test_schemas/test_switchboard_schemas.py::TestSwitchboardSchemas::test_switchboard_capacity_analysis_schema_overloaded", "tests/test_schemas/test_switchboard_schemas.py::TestSwitchboardSchemas::test_switchboard_capacity_analysis_schema_valid", "tests/test_schemas/test_switchboard_schemas.py::TestSwitchboardSchemas::test_switchboard_component_create_schema_invalid_quantity", "tests/test_schemas/test_switchboard_schemas.py::TestSwitchboardSchemas::test_switchboard_component_create_schema_valid", "tests/test_schemas/test_switchboard_schemas.py::TestSwitchboardSchemas::test_switchboard_create_schema_empty_name", "tests/test_schemas/test_switchboard_schemas.py::TestSwitchboardSchemas::test_switchboard_create_schema_invalid_phases", "tests/test_schemas/test_switchboard_schemas.py::TestSwitchboardSchemas::test_switchboard_create_schema_invalid_voltage", "tests/test_schemas/test_switchboard_schemas.py::TestSwitchboardSchemas::test_switchboard_create_schema_valid", "tests/test_schemas/test_switchboard_schemas.py::TestSwitchboardSchemas::test_switchboard_load_summary_schema_valid", "tests/test_schemas/test_switchboard_schemas.py::TestSwitchboardSchemas::test_switchboard_update_schema_partial", "tests/test_schemas/test_user_schemas.py::TestUserPreferenceSchemas::test_user_preference_create_schema_invalid_safety_margin", "tests/test_schemas/test_user_schemas.py::TestUserPreferenceSchemas::test_user_preference_create_schema_invalid_temperature_range", "tests/test_schemas/test_user_schemas.py::TestUserPreferenceSchemas::test_user_preference_create_schema_invalid_theme", "tests/test_schemas/test_user_schemas.py::TestUserPreferenceSchemas::test_user_preference_create_schema_temperature_boundaries", "tests/test_schemas/test_user_schemas.py::TestUserPreferenceSchemas::test_user_preference_create_schema_valid", "tests/test_schemas/test_user_schemas.py::TestUserPreferenceSchemas::test_user_preference_update_schema_partial", "tests/test_schemas/test_user_schemas.py::TestUserSchemaEdgeCases::test_password_minimum_length", "tests/test_schemas/test_user_schemas.py::TestUserSchemaEdgeCases::test_user_name_whitespace_handling", "tests/test_schemas/test_user_schemas.py::TestUserSchemaEdgeCases::test_user_optional_email", "tests/test_schemas/test_user_schemas.py::TestUserSchemaEdgeCases::test_user_preference_equal_temperatures", "tests/test_schemas/test_user_schemas.py::TestUserSchemaEdgeCases::test_user_preference_theme_case_sensitivity", "tests/test_schemas/test_user_schemas.py::TestUserSchemas::test_login_request_schema_invalid_email", "tests/test_schemas/test_user_schemas.py::TestUserSchemas::test_login_request_schema_valid", "tests/test_schemas/test_user_schemas.py::TestUserSchemas::test_password_change_request_schema_valid", "tests/test_schemas/test_user_schemas.py::TestUserSchemas::test_password_change_request_schema_weak_new_password", "tests/test_schemas/test_user_schemas.py::TestUserSchemas::test_password_reset_confirm_schema_valid", "tests/test_schemas/test_user_schemas.py::TestUserSchemas::test_user_create_schema_empty_name", "tests/test_schemas/test_user_schemas.py::TestUserSchemas::test_user_create_schema_invalid_email", "tests/test_schemas/test_user_schemas.py::TestUserSchemas::test_user_create_schema_password_no_digit", "tests/test_schemas/test_user_schemas.py::TestUserSchemas::test_user_create_schema_password_no_uppercase", "tests/test_schemas/test_user_schemas.py::TestUserSchemas::test_user_create_schema_password_validation", "tests/test_schemas/test_user_schemas.py::TestUserSchemas::test_user_create_schema_valid", "tests/test_schemas/test_user_schemas.py::TestUserSchemas::test_user_update_schema_partial", "tests/test_security/test_security_validation.py::TestAuthenticationSecurity::test_jwt_token_validation", "tests/test_security/test_security_validation.py::TestAuthenticationSecurity::test_session_security", "tests/test_security/test_security_validation.py::TestAuthenticationSecurity::test_unauthorized_access_protection", "tests/test_security/test_security_validation.py::TestDataValidationSecurity::test_json_payload_size_limits", "tests/test_security/test_security_validation.py::TestDataValidationSecurity::test_nested_json_depth_limits", "tests/test_security/test_security_validation.py::TestDataValidationSecurity::test_unicode_security", "tests/test_security/test_security_validation.py::TestInputValidationSecurity::test_command_injection_protection", "tests/test_security/test_security_validation.py::TestInputValidationSecurity::test_path_traversal_protection", "tests/test_security/test_security_validation.py::TestInputValidationSecurity::test_sql_injection_protection", "tests/test_security/test_security_validation.py::TestInputValidationSecurity::test_xss_protection", "tests/test_security/test_security_validation.py::TestRateLimitingSecurity::test_api_rate_limiting", "tests/test_security/test_security_validation.py::TestRateLimitingSecurity::test_concurrent_request_limits", "tests/test_services/test_activity_log_service.py::TestActivityLogService::test_database_error_handling", "tests/test_services/test_activity_log_service.py::TestActivityLogService::test_delete_old_activity_logs_invalid_days", "tests/test_services/test_activity_log_service.py::TestActivityLogService::test_delete_old_activity_logs_success", "tests/test_services/test_activity_log_service.py::TestActivityLogService::test_generate_audit_report_success", "tests/test_services/test_activity_log_service.py::TestActivityLogService::test_get_activity_log_not_found", "tests/test_services/test_activity_log_service.py::TestActivityLogService::test_get_activity_log_success", "tests/test_services/test_activity_log_service.py::TestActivityLogService::test_get_user_activity_logs_success", "tests/test_services/test_activity_log_service.py::TestActivityLogService::test_get_user_activity_logs_user_not_found", "tests/test_services/test_activity_log_service.py::TestActivityLogService::test_get_user_activity_summary_invalid_date_range", "tests/test_services/test_activity_log_service.py::TestActivityLogService::test_get_user_activity_summary_success", "tests/test_services/test_activity_log_service.py::TestActivityLogService::test_log_authentication_event_failed_login", "tests/test_services/test_activity_log_service.py::TestActivityLogService::test_log_authentication_event_success", "tests/test_services/test_activity_log_service.py::TestActivityLogService::test_log_event_invalid_user", "tests/test_services/test_activity_log_service.py::TestActivityLogService::test_log_event_success", "tests/test_services/test_activity_log_service.py::TestActivityLogService::test_log_event_system_event", "tests/test_services/test_activity_log_service.py::TestActivityLogService::test_log_security_event_invalid_severity", "tests/test_services/test_activity_log_service.py::TestActivityLogService::test_log_security_event_invalid_threat_level", "tests/test_services/test_activity_log_service.py::TestActivityLogService::test_log_security_event_success", "tests/test_services/test_activity_log_service.py::TestActivityLogService::test_log_system_event_success", "tests/test_services/test_activity_log_service.py::TestActivityLogService::test_log_user_action_invalid_action", "tests/test_services/test_activity_log_service.py::TestActivityLogService::test_log_user_action_invalid_entity_type", "tests/test_services/test_activity_log_service.py::TestActivityLogService::test_log_user_action_success", "tests/test_services/test_activity_log_service.py::TestActivityLogService::test_service_initialization", "tests/test_services/test_activity_log_service.py::TestActivityLogService::test_transaction_rollback_on_error", "tests/test_services/test_activity_log_service.py::TestActivityLogService::test_update_activity_log_not_found", "tests/test_services/test_activity_log_service.py::TestActivityLogService::test_update_activity_log_success", "tests/test_services/test_component_service.py::TestComponentService::test_create_component_category_not_found", "tests/test_services/test_component_service.py::TestComponentService::test_create_component_duplicate_name", "tests/test_services/test_component_service.py::TestComponentService::test_create_component_integrity_error", "tests/test_services/test_component_service.py::TestComponentService::test_create_component_success", "tests/test_services/test_component_service.py::TestComponentService::test_delete_component_not_found", "tests/test_services/test_component_service.py::TestComponentService::test_delete_component_success", "tests/test_services/test_component_service.py::TestComponentService::test_get_component_details_deleted", "tests/test_services/test_component_service.py::TestComponentService::test_get_component_details_not_found", "tests/test_services/test_component_service.py::TestComponentService::test_get_component_details_success", "tests/test_services/test_component_service.py::TestComponentService::test_get_components_list_success", "tests/test_services/test_component_service.py::TestComponentService::test_get_components_list_with_category_filter", "tests/test_services/test_component_service.py::TestComponentService::test_get_components_list_with_search", "tests/test_services/test_component_service.py::TestComponentService::test_update_component_not_found", "tests/test_services/test_component_service.py::TestComponentService::test_update_component_success", "tests/test_services/test_electrical_service.py::TestElectricalService::test_cable_sizing_calculation_error_handling", "tests/test_services/test_electrical_service.py::TestElectricalService::test_cable_sizing_calculation_success", "tests/test_services/test_electrical_service.py::TestElectricalService::test_calculate_load_for_electrical_node_success", "tests/test_services/test_electrical_service.py::TestElectricalService::test_calculate_load_for_nonexistent_node", "tests/test_services/test_electrical_service.py::TestElectricalService::test_electrical_standards_validation_success", "tests/test_services/test_electrical_service.py::TestElectricalService::test_generate_recommendations", "tests/test_services/test_electrical_service.py::TestElectricalService::test_voltage_drop_calculation_error_handling", "tests/test_services/test_electrical_service.py::TestElectricalService::test_voltage_drop_calculation_success", "tests/test_services/test_heat_tracing_service.py::TestHeatTracingServiceCalculations::test_calculate_pipe_heat_loss_pipe_not_found", "tests/test_services/test_heat_tracing_service.py::TestHeatTracingServiceCalculations::test_calculate_pipe_heat_loss_success", "tests/test_services/test_heat_tracing_service.py::TestHeatTracingServiceCalculations::test_validate_standards_compliance_success", "tests/test_services/test_heat_tracing_service.py::TestHeatTracingServiceDesignWorkflow::test_execute_design_workflow_success", "tests/test_services/test_heat_tracing_service.py::TestHeatTracingServicePipeOperations::test_create_pipe_duplicate_error", "tests/test_services/test_heat_tracing_service.py::TestHeatTracingServicePipeOperations::test_create_pipe_success", "tests/test_services/test_heat_tracing_service.py::TestHeatTracingServicePipeOperations::test_create_pipe_validation_error", "tests/test_services/test_heat_tracing_service.py::TestHeatTracingServicePipeOperations::test_delete_pipe_success", "tests/test_services/test_heat_tracing_service.py::TestHeatTracingServicePipeOperations::test_get_pipe_details_deleted", "tests/test_services/test_heat_tracing_service.py::TestHeatTracingServicePipeOperations::test_get_pipe_details_not_found", "tests/test_services/test_heat_tracing_service.py::TestHeatTracingServicePipeOperations::test_get_pipe_details_success", "tests/test_services/test_heat_tracing_service.py::TestHeatTracingServicePipeOperations::test_get_pipes_list_success", "tests/test_services/test_heat_tracing_service.py::TestHeatTracingServicePipeOperations::test_update_pipe_not_found", "tests/test_services/test_heat_tracing_service.py::TestHeatTracingServicePipeOperations::test_update_pipe_success", "tests/test_services/test_heat_tracing_service.py::TestHeatTracingServiceVesselOperations::test_create_vessel_invalid_dimensions", "tests/test_services/test_heat_tracing_service.py::TestHeatTracingServiceVesselOperations::test_create_vessel_success", "tests/test_services/test_heat_tracing_service.py::TestHeatTracingServiceVesselOperations::test_get_vessel_details_success", "tests/test_services/test_project_service.py::TestProjectService::test_create_project_duplicate_name_error", "tests/test_services/test_project_service.py::TestProjectService::test_create_project_success", "tests/test_services/test_project_service.py::TestProjectService::test_create_project_validation_error", "tests/test_services/test_project_service.py::TestProjectService::test_delete_project_success", "tests/test_services/test_project_service.py::TestProjectService::test_get_project_details_by_code", "tests/test_services/test_project_service.py::TestProjectService::test_get_project_details_by_id", "tests/test_services/test_project_service.py::TestProjectService::test_get_project_details_not_found", "tests/test_services/test_project_service.py::TestProjectService::test_get_projects_list_success", "tests/test_services/test_project_service.py::TestProjectService::test_update_project_success", "tests/test_services/test_project_service.py::TestProjectServiceIntegrationPatterns::test_complete_workflow_with_utils_and_di", "tests/test_services/test_project_service.py::TestProjectServiceWithDependencyInjection::test_multiple_services_with_shared_dependencies", "tests/test_services/test_project_service.py::TestProjectServiceWithDependencyInjection::test_service_creation_with_di", "tests/test_services/test_project_service.py::TestProjectServiceWithDependencyInjection::test_service_method_with_repository_di", "tests/test_services/test_project_service.py::TestProjectServiceWithUtilities::test_create_project_with_string_sanitization", "tests/test_services/test_project_service.py::TestProjectServiceWithUtilities::test_get_projects_with_pagination_utils", "tests/test_services/test_project_service.py::TestProjectServiceWithUtilities::test_update_project_with_datetime_utils", "tests/test_services/test_switchboard_service.py::TestSwitchboardService::test_add_switchboard_component_component_not_found", "tests/test_services/test_switchboard_service.py::TestSwitchboardService::test_add_switchboard_component_success", "tests/test_services/test_switchboard_service.py::TestSwitchboardService::test_add_switchboard_component_switchboard_not_found", "tests/test_services/test_switchboard_service.py::TestSwitchboardService::test_create_feeder_success", "tests/test_services/test_switchboard_service.py::TestSwitchboardService::test_create_feeder_switchboard_not_found", "tests/test_services/test_switchboard_service.py::TestSwitchboardService::test_create_switchboard_invalid_voltage", "tests/test_services/test_switchboard_service.py::TestSwitchboardService::test_create_switchboard_success", "tests/test_services/test_switchboard_service.py::TestSwitchboardService::test_delete_switchboard_not_found", "tests/test_services/test_switchboard_service.py::TestSwitchboardService::test_delete_switchboard_success", "tests/test_services/test_switchboard_service.py::TestSwitchboardService::test_get_switchboard_capacity_analysis_success", "tests/test_services/test_switchboard_service.py::TestSwitchboardService::test_get_switchboard_load_summary_success", "tests/test_services/test_switchboard_service.py::TestSwitchboardService::test_get_switchboard_not_found", "tests/test_services/test_switchboard_service.py::TestSwitchboardService::test_get_switchboard_success", "tests/test_services/test_switchboard_service.py::TestSwitchboardService::test_update_switchboard_invalid_voltage", "tests/test_services/test_switchboard_service.py::TestSwitchboardService::test_update_switchboard_success", "tests/test_services/test_user_service.py::TestUserService::test_change_password_invalid_current", "tests/test_services/test_user_service.py::TestUserService::test_change_password_success", "tests/test_services/test_user_service.py::TestUserService::test_count_active_users_success", "tests/test_services/test_user_service.py::TestUserService::test_create_or_update_user_preferences_success", "tests/test_services/test_user_service.py::TestUserService::test_create_user_email_already_exists", "tests/test_services/test_user_service.py::TestUserService::test_create_user_success", "tests/test_services/test_user_service.py::TestUserService::test_deactivate_user_success", "tests/test_services/test_user_service.py::TestUserService::test_get_user_inactive", "tests/test_services/test_user_service.py::TestUserService::test_get_user_not_found", "tests/test_services/test_user_service.py::TestUserService::test_get_user_preferences_not_found", "tests/test_services/test_user_service.py::TestUserService::test_get_user_preferences_success", "tests/test_services/test_user_service.py::TestUserService::test_get_user_success", "tests/test_services/test_user_service.py::TestUserService::test_hash_password", "tests/test_services/test_user_service.py::TestUserService::test_login_inactive_user", "tests/test_services/test_user_service.py::TestUserService::test_login_invalid_email", "tests/test_services/test_user_service.py::TestUserService::test_login_invalid_password", "tests/test_services/test_user_service.py::TestUserService::test_login_success", "tests/test_services/test_user_service.py::TestUserService::test_search_users_success", "tests/test_services/test_user_service.py::TestUserService::test_update_user_success", "tests/test_services/test_user_service.py::TestUserService::test_verify_password", "tests/test_standards/test_ieee_515.py::TestIEEE515::test_calculate_parameters_cable_selection", "tests/test_standards/test_ieee_515.py::TestIEEE515::test_calculate_parameters_electrical_parameters", "tests/test_standards/test_ieee_515.py::TestIEEE515::test_calculate_parameters_missing_input", "tests/test_standards/test_ieee_515.py::TestIEEE515::test_calculate_parameters_power_requirements", "tests/test_standards/test_ieee_515.py::TestIEEE515::test_calculate_parameters_temperature_parameters", "tests/test_standards/test_ieee_515.py::TestIEEE515::test_get_all_parameters", "tests/test_standards/test_ieee_515.py::TestIEEE515::test_get_all_rules", "tests/test_standards/test_ieee_515.py::TestIEEE515::test_get_applicable_rules_hazardous_area", "tests/test_standards/test_ieee_515.py::TestIEEE515::test_get_applicable_rules_outdoor_installation", "tests/test_standards/test_ieee_515.py::TestIEEE515::test_get_applicable_rules_standard_application", "tests/test_standards/test_ieee_515.py::TestIEEE515::test_get_specific_parameter", "tests/test_standards/test_ieee_515.py::TestIEEE515::test_get_specific_rule", "tests/test_standards/test_ieee_515.py::TestIEEE515::test_initialization", "tests/test_standards/test_ieee_515.py::TestIEEE515::test_installation_requirements_validation", "tests/test_standards/test_ieee_515.py::TestIEEE515::test_logging_functionality", "tests/test_standards/test_ieee_515.py::TestIEEE515::test_power_density_recommendations", "tests/test_standards/test_ieee_515.py::TestIEEE515::test_safety_requirements_validation", "tests/test_standards/test_ieee_515.py::TestIEEE515::test_standard_info", "tests/test_standards/test_ieee_515.py::TestIEEE515::test_validate_design_hazardous_area", "tests/test_standards/test_ieee_515.py::TestIEEE515::test_validate_design_missing_required_fields", "tests/test_standards/test_ieee_515.py::TestIEEE515::test_validate_design_power_density_limits", "tests/test_standards/test_ieee_515.py::TestIEEE515::test_validate_design_temperature_limits", "tests/test_standards/test_ieee_515.py::TestIEEE515::test_validate_design_valid_input", "tests/test_standards/test_ieee_515.py::TestIEEE515::test_validate_design_voltage_limits", "tests/test_standards/test_ieee_515.py::TestIEEE515::test_voltage_validation_non_standard_voltages", "tests/test_standards/test_ieee_515.py::TestIEEE515::test_voltage_validation_standard_voltages", "tests/test_standards/test_standards_service.py::TestStandardsService::test_calculate_parameters_invalid_input", "tests/test_standards/test_standards_service.py::TestStandardsService::test_calculate_standards_parameters_multiple_standards", "tests/test_standards/test_standards_service.py::TestStandardsService::test_calculate_standards_parameters_single_standard", "tests/test_standards/test_standards_service.py::TestStandardsService::test_calculate_standards_parameters_with_options", "tests/test_standards/test_standards_service.py::TestStandardsService::test_consolidated_parameters", "tests/test_standards/test_standards_service.py::TestStandardsService::test_cross_standard_conflict_detection", "tests/test_standards/test_standards_service.py::TestStandardsService::test_design_recommendations_generation", "tests/test_standards/test_standards_service.py::TestStandardsService::test_get_available_standards", "tests/test_standards/test_standards_service.py::TestStandardsService::test_get_compliance_summary", "tests/test_standards/test_standards_service.py::TestStandardsService::test_get_compliance_summary_filtered", "tests/test_standards/test_standards_service.py::TestStandardsService::test_get_standards_recommendations_hazardous_area", "tests/test_standards/test_standards_service.py::TestStandardsService::test_get_standards_recommendations_heat_tracing", "tests/test_standards/test_standards_service.py::TestStandardsService::test_get_standards_recommendations_offshore", "tests/test_standards/test_standards_service.py::TestStandardsService::test_initialization", "tests/test_standards/test_standards_service.py::TestStandardsService::test_logging_functionality", "tests/test_standards/test_standards_service.py::TestStandardsService::test_parameter_comparisons", "tests/test_standards/test_standards_service.py::TestStandardsService::test_performance_with_multiple_standards", "tests/test_standards/test_standards_service.py::TestStandardsService::test_validate_design_against_all_standards", "tests/test_standards/test_standards_service.py::TestStandardsService::test_validate_design_against_multiple_standards", "tests/test_standards/test_standards_service.py::TestStandardsService::test_validate_design_against_single_standard", "tests/test_standards/test_standards_service.py::TestStandardsService::test_validate_design_invalid_input", "tests/test_standards/test_standards_service.py::TestStandardsService::test_validate_design_unknown_standard", "tests/test_standards/test_standards_service.py::TestStandardsService::test_validate_design_with_validation_options", "tests/test_utils.py::TestDateTimeUtils::test_calculate_time_difference", "tests/test_utils.py::TestDateTimeUtils::test_format_datetime", "tests/test_utils.py::TestDateTimeUtils::test_parse_datetime", "tests/test_utils.py::TestDateTimeUtils::test_utcnow_aware", "tests/test_utils.py::TestFileIOUtils::test_file_io_error_handling", "tests/test_utils.py::TestFileIOUtils::test_json_file_operations", "tests/test_utils.py::TestFileIOUtils::test_temporary_directory", "tests/test_utils.py::TestFileIOUtils::test_temporary_file", "tests/test_utils.py::TestIntegration::test_file_json_validation_combination", "tests/test_utils.py::TestIntegration::test_uuid_datetime_combination", "tests/test_utils.py::TestJSONValidation::test_validate_json_data_failure", "tests/test_utils.py::TestJSONValidation::test_validate_json_data_success", "tests/test_utils.py::TestPaginationUtils::test_pagination_params_validation", "tests/test_utils.py::TestPaginationUtils::test_parse_pagination_params", "tests/test_utils.py::TestStringUtils::test_hash_string", "tests/test_utils.py::TestStringUtils::test_pad_string", "tests/test_utils.py::TestStringUtils::test_sanitize_text", "tests/test_utils.py::TestStringUtils::test_slugify", "tests/test_utils.py::TestStringUtils::test_slugify_with_options", "tests/test_utils.py::TestStringUtils::test_truncate_string", "tests/test_utils.py::TestUUIDUtils::test_generate_uuid7", "tests/test_utils.py::TestUUIDUtils::test_generate_uuid7_str", "tests/test_utils.py::TestUUIDUtils::test_invalid_uuid_conversion", "tests/test_utils.py::TestUUIDUtils::test_is_valid_uuid", "tests/test_utils.py::TestUUIDUtils::test_uuid_conversion", "tests/test_utils_and_di_integration.py::TestDIMockingPatterns::test_mock_repository_dependencies", "tests/test_utils_and_di_integration.py::TestDIMockingPatterns::test_mock_service_dependencies", "tests/test_utils_and_di_integration.py::TestDependencyInjectionIntegration::test_dependency_override_manager", "tests/test_utils_and_di_integration.py::TestDependencyInjectionIntegration::test_enhanced_test_client", "tests/test_utils_and_di_integration.py::TestDependencyInjectionIntegration::test_repository_dependency_injection", "tests/test_utils_and_di_integration.py::TestDependencyInjectionIntegration::test_service_dependency_injection", "tests/test_utils_and_di_integration.py::TestUtilityAndDIIntegration::test_api_route_with_utilities_and_di", "tests/test_utils_and_di_integration.py::TestUtilityAndDIIntegration::test_service_with_utilities_and_di", "tests/test_utils_and_di_integration.py::TestUtilityIntegration::test_datetime_utils_in_service_layer", "tests/test_utils_and_di_integration.py::TestUtilityIntegration::test_file_io_utils_in_service_layer", "tests/test_utils_and_di_integration.py::TestUtilityIntegration::test_json_validation_utils_in_service_layer", "tests/test_utils_and_di_integration.py::TestUtilityIntegration::test_pagination_utils_in_service_layer", "tests/test_utils_and_di_integration.py::TestUtilityIntegration::test_string_utils_in_service_layer", "tests/test_utils_and_di_integration.py::TestUtilityMockingPatterns::test_mock_all_datetime_utils", "tests/test_utils_and_di_integration.py::TestUtilityMockingPatterns::test_mock_all_pagination_utils", "tests/test_utils_and_di_integration.py::TestUtilityMockingPatterns::test_mock_all_string_utils"]