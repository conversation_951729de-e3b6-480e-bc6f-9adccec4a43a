["tests/test_api/test_activity_log_routes.py::TestActivityLogRoutes::test_create_activity_log_success", "tests/test_api/test_activity_log_routes.py::TestActivityLogRoutes::test_database_error_handling", "tests/test_api/test_activity_log_routes.py::TestActivityLogRoutes::test_delete_old_activity_logs_success", "tests/test_api/test_activity_log_routes.py::TestActivityLogRoutes::test_get_activity_log_not_found", "tests/test_api/test_activity_log_routes.py::TestActivityLogRoutes::test_get_activity_log_success", "tests/test_api/test_activity_log_routes.py::TestActivityLogRoutes::test_get_entity_activity_logs_success", "tests/test_api/test_activity_log_routes.py::TestActivityLogRoutes::test_get_recent_activity_success", "tests/test_api/test_activity_log_routes.py::TestActivityLogRoutes::test_get_security_events_success", "tests/test_api/test_activity_log_routes.py::TestActivityLogRoutes::test_get_user_activity_logs_success", "tests/test_api/test_activity_log_routes.py::TestActivityLogRoutes::test_get_user_activity_summary_success", "tests/test_api/test_activity_log_routes.py::TestActivityLogRoutes::test_invalid_input_error_handling", "tests/test_api/test_activity_log_routes.py::TestActivityLogRoutes::test_list_activity_logs_success", "tests/test_api/test_activity_log_routes.py::TestActivityLogRoutes::test_list_activity_logs_with_filters", "tests/test_api/test_activity_log_routes.py::TestActivityLogRoutes::test_log_security_event_success", "tests/test_api/test_activity_log_routes.py::TestActivityLogRoutes::test_search_activity_logs_success", "tests/test_api/test_activity_log_routes.py::TestActivityLogRoutes::test_update_activity_log_success", "tests/test_api/test_activity_log_routes.py::TestActivityLogRoutes::test_validation_error_handling", "tests/test_api/test_calculations_routes.py::TestCalculationsRoutes::test_calculate_cable_selection_valid_request", "tests/test_api/test_calculations_routes.py::TestCalculationsRoutes::test_calculate_electrical_load_valid_request", "tests/test_api/test_calculations_routes.py::TestCalculationsRoutes::test_calculate_heat_tracing_invalid_request", "tests/test_api/test_calculations_routes.py::TestCalculationsRoutes::test_calculate_heat_tracing_large_dataset", "tests/test_api/test_calculations_routes.py::TestCalculationsRoutes::test_calculate_heat_tracing_missing_auth", "tests/test_api/test_calculations_routes.py::TestCalculationsRoutes::test_calculate_heat_tracing_valid_request", "tests/test_api/test_calculations_routes.py::TestCalculationsRoutes::test_calculate_power_requirements_valid_request", "tests/test_api/test_calculations_routes.py::TestCalculationsRoutes::test_calculate_with_performance_monitoring", "tests/test_api/test_calculations_routes.py::TestCalculationsRoutes::test_calculations_health_check", "tests/test_api/test_calculations_routes.py::TestCalculationsRoutes::test_calculations_routes_without_auth", "tests/test_api/test_calculations_routes.py::TestCalculationsRoutes::test_error_handling_invalid_json", "tests/test_api/test_calculations_routes.py::TestCalculationsRoutes::test_error_handling_missing_fields", "tests/test_api/test_calculations_routes.py::TestCalculationsRoutes::test_get_calculation_history", "tests/test_api/test_calculations_routes.py::TestCalculationsRoutes::test_get_calculation_history_filtered", "tests/test_api/test_calculations_routes.py::TestCalculationsRoutes::test_get_calculation_template_specific", "tests/test_api/test_calculations_routes.py::TestCalculationsRoutes::test_get_calculation_templates", "tests/test_api/test_calculations_routes.py::TestCalculationsRoutes::test_validate_calculation_input", "tests/test_api/test_component_routes.py::TestComponentCategoryRoutes::test_create_category_success", "tests/test_api/test_component_routes.py::TestComponentCategoryRoutes::test_get_category_success", "tests/test_api/test_component_routes.py::TestComponentCategoryRoutes::test_list_categories_success", "tests/test_api/test_component_routes.py::TestComponentRoutes::test_create_component_duplicate_error", "tests/test_api/test_component_routes.py::TestComponentRoutes::test_create_component_success", "tests/test_api/test_component_routes.py::TestComponentRoutes::test_create_component_validation_error", "tests/test_api/test_component_routes.py::TestComponentRoutes::test_delete_component_success", "tests/test_api/test_component_routes.py::TestComponentRoutes::test_get_component_not_found", "tests/test_api/test_component_routes.py::TestComponentRoutes::test_get_component_success", "tests/test_api/test_component_routes.py::TestComponentRoutes::test_list_components_success", "tests/test_api/test_component_routes.py::TestComponentRoutes::test_list_components_with_filters", "tests/test_api/test_component_routes.py::TestComponentRoutes::test_update_component_success", "tests/test_api/test_component_routes_error_handling.py::TestComponentCategoryErrorHandling::test_create_category_circular_dependency", "tests/test_api/test_component_routes_error_handling.py::TestComponentCategoryErrorHandling::test_delete_category_with_subcategories", "tests/test_api/test_component_routes_error_handling.py::TestComponentCategoryErrorHandling::test_update_category_invalid_parent_hierarchy", "tests/test_api/test_component_routes_error_handling.py::TestComponentErrorHandling::test_create_component_database_error", "tests/test_api/test_component_routes_error_handling.py::TestComponentErrorHandling::test_create_component_extremely_large_payload", "tests/test_api/test_component_routes_error_handling.py::TestComponentErrorHandling::test_create_component_integrity_constraint_violation", "tests/test_api/test_component_routes_error_handling.py::TestComponentErrorHandling::test_create_component_malformed_json_data", "tests/test_api/test_component_routes_error_handling.py::TestComponentErrorHandling::test_delete_component_with_dependencies", "tests/test_api/test_component_routes_error_handling.py::TestComponentErrorHandling::test_get_component_service_unavailable", "tests/test_api/test_component_routes_error_handling.py::TestComponentErrorHandling::test_list_components_database_timeout", "tests/test_api/test_component_routes_error_handling.py::TestComponentErrorHandling::test_search_components_with_sql_injection_attempt", "tests/test_api/test_component_routes_error_handling.py::TestComponentErrorHandling::test_update_component_concurrent_modification", "tests/test_api/test_component_routes_error_handling.py::TestComponentValidationEdgeCases::test_component_name_unicode_characters", "tests/test_api/test_component_routes_error_handling.py::TestComponentValidationEdgeCases::test_component_specific_data_edge_cases", "tests/test_api/test_document_routes.py::TestCalculationStandardRoutes::test_calculation_standard_validation_errors", "tests/test_api/test_document_routes.py::TestCalculationStandardRoutes::test_create_calculation_standard", "tests/test_api/test_document_routes.py::TestCalculationStandardRoutes::test_create_calculation_standard_duplicate_code", "tests/test_api/test_document_routes.py::TestCalculationStandardRoutes::test_delete_calculation_standard", "tests/test_api/test_document_routes.py::TestCalculationStandardRoutes::test_get_calculation_standard_by_code", "tests/test_api/test_document_routes.py::TestCalculationStandardRoutes::test_get_calculation_standard_by_id", "tests/test_api/test_document_routes.py::TestCalculationStandardRoutes::test_get_nonexistent_calculation_standard", "tests/test_api/test_document_routes.py::TestCalculationStandardRoutes::test_list_calculation_standards", "tests/test_api/test_document_routes.py::TestCalculationStandardRoutes::test_update_calculation_standard", "tests/test_api/test_document_routes.py::TestExportedDocumentRoutes::test_create_exported_document", "tests/test_api/test_document_routes.py::TestExportedDocumentRoutes::test_list_exported_documents_by_project", "tests/test_api/test_document_routes.py::TestImportedDataRevisionRoutes::test_create_imported_data_revision", "tests/test_api/test_document_routes.py::TestImportedDataRevisionRoutes::test_list_imported_data_revisions_by_project", "tests/test_api/test_electrical_routes.py::TestCableRouteRoutes::test_create_cable_route_success", "tests/test_api/test_electrical_routes.py::TestCableRouteRoutes::test_get_cable_route_success", "tests/test_api/test_electrical_routes.py::TestCableRouteRoutes::test_list_cable_routes_success", "tests/test_api/test_electrical_routes.py::TestElectricalBusinessLogicRoutes::test_design_workflow_success", "tests/test_api/test_electrical_routes.py::TestElectricalBusinessLogicRoutes::test_node_load_summary_success", "tests/test_api/test_electrical_routes.py::TestElectricalBusinessLogicRoutes::test_optimize_cable_route_success", "tests/test_api/test_electrical_routes.py::TestElectricalCalculationRoutes::test_cable_sizing_calculation_success", "tests/test_api/test_electrical_routes.py::TestElectricalCalculationRoutes::test_standards_validation_success", "tests/test_api/test_electrical_routes.py::TestElectricalCalculationRoutes::test_voltage_drop_calculation_success", "tests/test_api/test_electrical_routes.py::TestElectricalNodeRoutes::test_create_electrical_node_success", "tests/test_api/test_electrical_routes.py::TestElectricalNodeRoutes::test_create_electrical_node_validation_error", "tests/test_api/test_electrical_routes.py::TestElectricalNodeRoutes::test_delete_electrical_node_success", "tests/test_api/test_electrical_routes.py::TestElectricalNodeRoutes::test_get_electrical_node_not_found", "tests/test_api/test_electrical_routes.py::TestElectricalNodeRoutes::test_get_electrical_node_success", "tests/test_api/test_electrical_routes.py::TestElectricalNodeRoutes::test_list_electrical_nodes_success", "tests/test_api/test_electrical_routes.py::TestElectricalNodeRoutes::test_update_electrical_node_success", "tests/test_api/test_electrical_routes.py::TestLoadCalculationRoutes::test_create_load_calculation_success", "tests/test_api/test_electrical_routes.py::TestLoadCalculationRoutes::test_get_load_calculation_success", "tests/test_api/test_electrical_routes.py::TestLoadCalculationRoutes::test_list_load_calculations_success", "tests/test_api/test_electrical_routes_edge_cases.py::TestCableRouteEdgeCases::test_create_cable_route_extreme_lengths", "tests/test_api/test_electrical_routes_edge_cases.py::TestCableRouteEdgeCases::test_create_cable_route_same_nodes", "tests/test_api/test_electrical_routes_edge_cases.py::TestElectricalCalculationEdgeCases::test_cable_sizing_calculation_extreme_values", "tests/test_api/test_electrical_routes_edge_cases.py::TestElectricalNodeEdgeCases::test_create_electrical_node_boundary_values", "tests/test_api/test_electrical_routes_edge_cases.py::TestElectricalNodeEdgeCases::test_create_electrical_node_extreme_string_lengths", "tests/test_api/test_electrical_routes_edge_cases.py::TestElectricalNodeEdgeCases::test_create_electrical_node_invalid_enum_values", "tests/test_api/test_electrical_routes_edge_cases.py::TestElectricalNodeEdgeCases::test_get_electrical_node_invalid_ids", "tests/test_api/test_electrical_routes_edge_cases.py::TestElectricalNodeEdgeCases::test_list_electrical_nodes_pagination_edge_cases", "tests/test_api/test_heat_tracing_routes.py::TestCalculationEndpoints::test_calculate_pipe_heat_loss_success", "tests/test_api/test_heat_tracing_routes.py::TestCalculationEndpoints::test_validate_standards_compliance_success", "tests/test_api/test_heat_tracing_routes.py::TestDesignWorkflowEndpoints::test_execute_design_workflow_success", "tests/test_api/test_heat_tracing_routes.py::TestPipeEndpoints::test_create_pipe_duplicate_error", "tests/test_api/test_heat_tracing_routes.py::TestPipeEndpoints::test_create_pipe_success", "tests/test_api/test_heat_tracing_routes.py::TestPipeEndpoints::test_create_pipe_validation_error", "tests/test_api/test_heat_tracing_routes.py::TestPipeEndpoints::test_delete_pipe_success", "tests/test_api/test_heat_tracing_routes.py::TestPipeEndpoints::test_get_pipe_not_found", "tests/test_api/test_heat_tracing_routes.py::TestPipeEndpoints::test_get_pipe_success", "tests/test_api/test_heat_tracing_routes.py::TestPipeEndpoints::test_list_pipes_success", "tests/test_api/test_heat_tracing_routes.py::TestPipeEndpoints::test_update_pipe_success", "tests/test_api/test_heat_tracing_routes.py::TestVesselEndpoints::test_create_vessel_success", "tests/test_api/test_heat_tracing_routes.py::TestVesselEndpoints::test_get_vessel_success", "tests/test_api/test_project_routes.py::TestProjectRoutes::test_create_project_duplicate_error", "tests/test_api/test_project_routes.py::TestProjectRoutes::test_create_project_success", "tests/test_api/test_project_routes.py::TestProjectRoutes::test_create_project_validation_error", "tests/test_api/test_project_routes.py::TestProjectRoutes::test_delete_project_not_found", "tests/test_api/test_project_routes.py::TestProjectRoutes::test_delete_project_success", "tests/test_api/test_project_routes.py::TestProjectRoutes::test_get_project_by_code_success", "tests/test_api/test_project_routes.py::TestProjectRoutes::test_get_project_by_id_success", "tests/test_api/test_project_routes.py::TestProjectRoutes::test_get_project_not_found", "tests/test_api/test_project_routes.py::TestProjectRoutes::test_invalid_pagination_parameters", "tests/test_api/test_project_routes.py::TestProjectRoutes::test_list_projects_include_deleted", "tests/test_api/test_project_routes.py::TestProjectRoutes::test_list_projects_success", "tests/test_api/test_project_routes.py::TestProjectRoutes::test_list_projects_with_pagination", "tests/test_api/test_project_routes.py::TestProjectRoutes::test_update_project_not_found", "tests/test_api/test_project_routes.py::TestProjectRoutes::test_update_project_success", "tests/test_api/test_project_routes.py::TestProjectRoutesWithEnhancedDI::test_create_project_with_dependency_override_manager", "tests/test_api/test_project_routes.py::TestProjectRoutesWithEnhancedDI::test_list_projects_with_pagination_utils", "tests/test_api/test_project_routes.py::TestProjectRoutesWithEnhancedDI::test_service_dependency_injection_chain", "tests/test_api/test_standards_routes.py::TestStandardsRoutes::test_calculate_parameters_invalid_input", "tests/test_api/test_standards_routes.py::TestStandardsRoutes::test_calculate_parameters_multiple_standards", "tests/test_api/test_standards_routes.py::TestStandardsRoutes::test_calculate_standards_parameters", "tests/test_api/test_standards_routes.py::TestStandardsRoutes::test_calculate_with_specific_standard", "tests/test_api/test_standards_routes.py::TestStandardsRoutes::test_error_handling_invalid_json", "tests/test_api/test_standards_routes.py::TestStandardsRoutes::test_get_available_standards", "tests/test_api/test_standards_routes.py::TestStandardsRoutes::test_get_compliance_summary", "tests/test_api/test_standards_routes.py::TestStandardsRoutes::test_get_compliance_summary_filtered", "tests/test_api/test_standards_routes.py::TestStandardsRoutes::test_get_recommendations_hazardous_area", "tests/test_api/test_standards_routes.py::TestStandardsRoutes::test_get_recommendations_offshore", "tests/test_api/test_standards_routes.py::TestStandardsRoutes::test_get_standard_information", "tests/test_api/test_standards_routes.py::TestStandardsRoutes::test_get_standard_information_not_found", "tests/test_api/test_standards_routes.py::TestStandardsRoutes::test_get_standards_recommendations", "tests/test_api/test_standards_routes.py::TestStandardsRoutes::test_performance_multiple_standards_validation", "tests/test_api/test_standards_routes.py::TestStandardsRoutes::test_standards_health_check", "tests/test_api/test_standards_routes.py::TestStandardsRoutes::test_standards_routes_without_auth", "tests/test_api/test_standards_routes.py::TestStandardsRoutes::test_validate_against_specific_standard", "tests/test_api/test_standards_routes.py::TestStandardsRoutes::test_validate_design_against_standards", "tests/test_api/test_standards_routes.py::TestStandardsRoutes::test_validate_design_invalid_input", "tests/test_api/test_standards_routes.py::TestStandardsRoutes::test_validate_design_multiple_standards", "tests/test_api/test_standards_routes.py::TestStandardsRoutes::test_validate_design_unknown_standard", "tests/test_api/test_switchboard_api.py::TestSwitchboardAPI::test_add_switchboard_component_success", "tests/test_api/test_switchboard_api.py::TestSwitchboardAPI::test_business_logic_error_handling", "tests/test_api/test_switchboard_api.py::TestSwitchboardAPI::test_create_feeder_success", "tests/test_api/test_switchboard_api.py::TestSwitchboardAPI::test_create_switchboard_invalid_data", "tests/test_api/test_switchboard_api.py::TestSwitchboardAPI::test_create_switchboard_success", "tests/test_api/test_switchboard_api.py::TestSwitchboardAPI::test_delete_switchboard_success", "tests/test_api/test_switchboard_api.py::TestSwitchboardAPI::test_get_capacity_analysis_success", "tests/test_api/test_switchboard_api.py::TestSwitchboardAPI::test_get_feeder_success", "tests/test_api/test_switchboard_api.py::TestSwitchboardAPI::test_get_load_summary_success", "tests/test_api/test_switchboard_api.py::TestSwitchboardAPI::test_get_switchboard_not_found", "tests/test_api/test_switchboard_api.py::TestSwitchboardAPI::test_get_switchboard_success", "tests/test_api/test_switchboard_api.py::TestSwitchboardAPI::test_list_switchboards_success", "tests/test_api/test_switchboard_api.py::TestSwitchboardAPI::test_update_switchboard_success", "tests/test_api/test_user_api.py::TestUserAuthenticationAPI::test_change_password_invalid_current", "tests/test_api/test_user_api.py::TestUserAuthenticationAPI::test_change_password_success", "tests/test_api/test_user_api.py::TestUserAuthenticationAPI::test_login_invalid_credentials", "tests/test_api/test_user_api.py::TestUserAuthenticationAPI::test_login_success", "tests/test_api/test_user_api.py::TestUserAuthenticationAPI::test_logout_success", "tests/test_api/test_user_api.py::TestUserManagementAPI::test_create_user_email_exists", "tests/test_api/test_user_api.py::TestUserManagementAPI::test_create_user_success", "tests/test_api/test_user_api.py::TestUserManagementAPI::test_deactivate_user_success", "tests/test_api/test_user_api.py::TestUserManagementAPI::test_get_user_not_found", "tests/test_api/test_user_api.py::TestUserManagementAPI::test_get_user_success", "tests/test_api/test_user_api.py::TestUserManagementAPI::test_list_users_success", "tests/test_api/test_user_api.py::TestUserManagementAPI::test_list_users_with_search", "tests/test_api/test_user_api.py::TestUserManagementAPI::test_update_user_success", "tests/test_api/test_user_api.py::TestUserPreferencesAPI::test_delete_user_preferences_success", "tests/test_api/test_user_api.py::TestUserPreferencesAPI::test_get_user_preferences_not_found", "tests/test_api/test_user_api.py::TestUserPreferencesAPI::test_get_user_preferences_success", "tests/test_api/test_user_api.py::TestUserPreferencesAPI::test_update_user_preferences_success", "tests/test_import_export/test_csv_parser.py::TestCSVParser::test_parse_csv_with_special_characters", "tests/test_repositories/test_activity_log_repository.py::TestActivityLogRepository::test_count_filtered_activity_logs", "tests/test_repositories/test_activity_log_repository.py::TestActivityLogRepository::test_database_error_handling", "tests/test_repositories/test_activity_log_repository.py::TestActivityLogRepository::test_delete_old_logs", "tests/test_repositories/test_activity_log_repository.py::TestActivityLogRepository::test_empty_results", "tests/test_repositories/test_activity_log_repository.py::TestActivityLogRepository::test_filter_activity_logs", "tests/test_repositories/test_activity_log_repository.py::TestActivityLogRepository::test_get_by_date_range", "tests/test_repositories/test_activity_log_repository.py::TestActivityLogRepository::test_get_by_entity", "tests/test_repositories/test_activity_log_repository.py::TestActivityLogRepository::test_get_by_event_type", "tests/test_repositories/test_activity_log_repository.py::TestActivityLogRepository::test_get_by_user_id", "tests/test_repositories/test_activity_log_repository.py::TestActivityLogRepository::test_get_daily_activity_counts", "tests/test_repositories/test_activity_log_repository.py::TestActivityLogRepository::test_get_recent_activity", "tests/test_repositories/test_activity_log_repository.py::TestActivityLogRepository::test_get_security_events", "tests/test_repositories/test_activity_log_repository.py::TestActivityLogRepository::test_get_unique_entities_count", "tests/test_repositories/test_activity_log_repository.py::TestActivityLogRepository::test_get_unique_users_count", "tests/test_repositories/test_activity_log_repository.py::TestActivityLogRepository::test_get_user_activity_summary", "tests/test_repositories/test_activity_log_repository.py::TestActivityLogRepository::test_repository_initialization", "tests/test_repositories/test_activity_log_repository.py::TestActivityLogRepository::test_search_by_details", "tests/test_repositories/test_document_repository.py::TestCalculationStandardRepository::test_create_calculation_standard", "tests/test_repositories/test_document_repository.py::TestCalculationStandardRepository::test_get_by_name", "tests/test_repositories/test_document_repository.py::TestCalculationStandardRepository::test_get_by_standard_code", "tests/test_repositories/test_document_repository.py::TestCalculationStandardRepository::test_search_by_name_or_code", "tests/test_repositories/test_document_repository.py::TestExportedDocumentRepository::test_create_exported_document", "tests/test_repositories/test_document_repository.py::TestExportedDocumentRepository::test_get_latest_by_document_type", "tests/test_repositories/test_document_repository.py::TestExportedDocumentRepository::test_mark_others_as_not_latest", "tests/test_repositories/test_document_repository.py::TestImportedDataRevisionRepository::test_create_imported_data_revision", "tests/test_repositories/test_document_repository.py::TestImportedDataRevisionRepository::test_deactivate_other_revisions", "tests/test_repositories/test_document_repository.py::TestImportedDataRevisionRepository::test_get_active_revision_by_filename", "tests/test_repositories/test_document_repository.py::TestImportedDataRevisionRepository::test_get_by_project_id", "tests/test_repositories/test_electrical_repository.py::TestCableRouteRepository::test_get_routes_by_installation_method_success", "tests/test_repositories/test_electrical_repository.py::TestCableRouteRepository::test_get_routes_by_node_both_directions", "tests/test_repositories/test_electrical_repository.py::TestCableRouteRepository::test_get_routes_by_node_invalid_direction", "tests/test_repositories/test_electrical_repository.py::TestCableRouteRepository::test_get_routes_with_high_voltage_drop_success", "tests/test_repositories/test_electrical_repository.py::TestElectricalNodeRepository::test_count_by_project_none_result", "tests/test_repositories/test_electrical_repository.py::TestElectricalNodeRepository::test_count_by_project_success", "tests/test_repositories/test_electrical_repository.py::TestElectricalNodeRepository::test_get_by_node_type_success", "tests/test_repositories/test_electrical_repository.py::TestElectricalNodeRepository::test_get_by_project_id_database_error", "tests/test_repositories/test_electrical_repository.py::TestElectricalNodeRepository::test_get_by_project_id_success", "tests/test_repositories/test_electrical_repository.py::TestElectricalNodeRepository::test_get_nodes_with_capacity_success", "tests/test_repositories/test_electrical_repository.py::TestLoadCalculationRepository::test_get_by_electrical_node_id_success", "tests/test_repositories/test_electrical_repository.py::TestLoadCalculationRepository::test_get_by_load_type_success", "tests/test_repositories/test_electrical_repository.py::TestLoadCalculationRepository::test_get_total_power_by_node_none_result", "tests/test_repositories/test_electrical_repository.py::TestLoadCalculationRepository::test_get_total_power_by_node_success", "tests/test_repositories/test_electrical_repository.py::TestVoltageDropCalculationRepository::test_count_by_compliance_status_success", "tests/test_repositories/test_electrical_repository.py::TestVoltageDropCalculationRepository::test_get_average_voltage_drop_by_project_success", "tests/test_repositories/test_electrical_repository.py::TestVoltageDropCalculationRepository::test_get_by_cable_route_id_success", "tests/test_repositories/test_electrical_repository.py::TestVoltageDropCalculationRepository::test_get_non_compliant_calculations_success", "tests/test_repositories/test_heat_tracing_repository.py::TestControlCircuitRepository::test_create_control_circuit_success", "tests/test_repositories/test_heat_tracing_repository.py::TestControlCircuitRepository::test_get_by_switchboard_id", "tests/test_repositories/test_heat_tracing_repository.py::TestControlCircuitRepository::test_get_with_limiting_function", "tests/test_repositories/test_heat_tracing_repository.py::TestHTCircuitRepository::test_create_htcircuit_success", "tests/test_repositories/test_heat_tracing_repository.py::TestHTCircuitRepository::test_get_by_feeder_id", "tests/test_repositories/test_heat_tracing_repository.py::TestHTCircuitRepository::test_get_by_pipe_id", "tests/test_repositories/test_heat_tracing_repository.py::TestHTCircuitRepository::test_get_total_feeder_load", "tests/test_repositories/test_heat_tracing_repository.py::TestHTCircuitRepository::test_update_load_calculation", "tests/test_repositories/test_heat_tracing_repository.py::TestHeatTracingRepository::test_get_design_readiness_empty", "tests/test_repositories/test_heat_tracing_repository.py::TestHeatTracingRepository::test_get_project_summary_empty", "tests/test_repositories/test_heat_tracing_repository.py::TestHeatTracingRepository::test_initialization", "tests/test_repositories/test_heat_tracing_repository.py::TestPipeRepository::test_count_by_project", "tests/test_repositories/test_heat_tracing_repository.py::TestPipeRepository::test_create_pipe_success", "tests/test_repositories/test_heat_tracing_repository.py::TestPipeRepository::test_get_pipe_by_id_not_found", "tests/test_repositories/test_heat_tracing_repository.py::TestPipeRepository::test_get_pipe_by_id_success", "tests/test_repositories/test_heat_tracing_repository.py::TestPipeRepository::test_get_pipe_by_line_tag", "tests/test_repositories/test_heat_tracing_repository.py::TestPipeRepository::test_get_pipe_by_line_tag_not_found", "tests/test_repositories/test_heat_tracing_repository.py::TestPipeRepository::test_get_pipes_by_project_id", "tests/test_repositories/test_heat_tracing_repository.py::TestPipeRepository::test_get_pipes_without_circuits", "tests/test_repositories/test_heat_tracing_repository.py::TestPipeRepository::test_get_with_heat_loss_calculations", "tests/test_repositories/test_heat_tracing_repository.py::TestPipeRepository::test_update_heat_loss_calculation", "tests/test_repositories/test_heat_tracing_repository.py::TestPipeRepository::test_update_heat_loss_calculation_not_found", "tests/test_repositories/test_heat_tracing_repository.py::TestVesselRepository::test_create_vessel_success", "tests/test_repositories/test_heat_tracing_repository.py::TestVesselRepository::test_get_vessel_by_equipment_tag", "tests/test_repositories/test_heat_tracing_repository.py::TestVesselRepository::test_get_vessels_without_circuits", "tests/test_repositories/test_heat_tracing_repository.py::TestVesselRepository::test_update_heat_loss_calculation", "tests/test_repositories/test_project_repository.py::TestProjectRepository::test_count_active_projects", "tests/test_repositories/test_project_repository.py::TestProjectRepository::test_count_active_projects_empty_db", "tests/test_repositories/test_project_repository.py::TestProjectRepository::test_create_project", "tests/test_repositories/test_project_repository.py::TestProjectRepository::test_get_active_projects", "tests/test_repositories/test_project_repository.py::TestProjectRepository::test_get_active_projects_with_pagination", "tests/test_repositories/test_project_repository.py::TestProjectRepository::test_get_all_projects", "tests/test_repositories/test_project_repository.py::TestProjectRepository::test_get_all_with_pagination", "tests/test_repositories/test_project_repository.py::TestProjectRepository::test_get_by_code_existing", "tests/test_repositories/test_project_repository.py::TestProjectRepository::test_get_by_code_nonexistent", "tests/test_repositories/test_project_repository.py::TestProjectRepository::test_get_by_id_existing", "tests/test_repositories/test_project_repository.py::TestProjectRepository::test_get_by_id_nonexistent", "tests/test_repositories/test_project_repository.py::TestProjectRepository::test_get_by_name_existing", "tests/test_repositories/test_project_repository.py::TestProjectRepository::test_get_by_name_nonexistent", "tests/test_repositories/test_project_repository.py::TestProjectRepository::test_get_project_with_related_data", "tests/test_repositories/test_project_repository.py::TestProjectRepository::test_get_project_with_related_data_nonexistent", "tests/test_repositories/test_project_repository.py::TestProjectRepository::test_search_projects_by_description", "tests/test_repositories/test_project_repository.py::TestProjectRepository::test_search_projects_by_name", "tests/test_repositories/test_project_repository.py::TestProjectRepository::test_search_projects_by_project_number", "tests/test_repositories/test_project_repository.py::TestProjectRepository::test_search_projects_case_insensitive", "tests/test_repositories/test_project_repository.py::TestProjectRepository::test_search_projects_excludes_deleted", "tests/test_repositories/test_project_repository.py::TestProjectRepository::test_search_projects_with_pagination", "tests/test_repositories/test_project_repository.py::TestProjectRepository::test_soft_delete_already_deleted_project", "tests/test_repositories/test_project_repository.py::TestProjectRepository::test_soft_delete_nonexistent_project", "tests/test_repositories/test_project_repository.py::TestProjectRepository::test_soft_delete_project", "tests/test_repositories/test_project_repository.py::TestProjectRepository::test_unique_constraint_name", "tests/test_repositories/test_project_repository.py::TestProjectRepository::test_unique_constraint_project_number", "tests/test_repositories/test_project_repository.py::TestProjectRepository::test_update_nonexistent_project", "tests/test_repositories/test_project_repository.py::TestProjectRepository::test_update_project", "tests/test_schemas/test_document_schemas.py::TestExportedDocumentSchemas::test_document_filename_validation_empty", "tests/test_schemas/test_project_schemas.py::TestProjectCreateSchema::test_voltages_json_validation_negative_voltage", "tests/test_services/test_activity_log_service.py::TestActivityLogService::test_database_error_handling", "tests/test_services/test_activity_log_service.py::TestActivityLogService::test_delete_old_activity_logs_invalid_days", "tests/test_services/test_activity_log_service.py::TestActivityLogService::test_delete_old_activity_logs_success", "tests/test_services/test_activity_log_service.py::TestActivityLogService::test_generate_audit_report_success", "tests/test_services/test_activity_log_service.py::TestActivityLogService::test_get_activity_log_not_found", "tests/test_services/test_activity_log_service.py::TestActivityLogService::test_get_activity_log_success", "tests/test_services/test_activity_log_service.py::TestActivityLogService::test_get_user_activity_logs_success", "tests/test_services/test_activity_log_service.py::TestActivityLogService::test_get_user_activity_logs_user_not_found", "tests/test_services/test_activity_log_service.py::TestActivityLogService::test_get_user_activity_summary_invalid_date_range", "tests/test_services/test_activity_log_service.py::TestActivityLogService::test_get_user_activity_summary_success", "tests/test_services/test_activity_log_service.py::TestActivityLogService::test_log_authentication_event_failed_login", "tests/test_services/test_activity_log_service.py::TestActivityLogService::test_log_authentication_event_success", "tests/test_services/test_activity_log_service.py::TestActivityLogService::test_log_event_invalid_user", "tests/test_services/test_activity_log_service.py::TestActivityLogService::test_log_event_success", "tests/test_services/test_activity_log_service.py::TestActivityLogService::test_log_event_system_event", "tests/test_services/test_activity_log_service.py::TestActivityLogService::test_log_security_event_invalid_severity", "tests/test_services/test_activity_log_service.py::TestActivityLogService::test_log_security_event_invalid_threat_level", "tests/test_services/test_activity_log_service.py::TestActivityLogService::test_log_security_event_success", "tests/test_services/test_activity_log_service.py::TestActivityLogService::test_log_system_event_success", "tests/test_services/test_activity_log_service.py::TestActivityLogService::test_log_user_action_invalid_action", "tests/test_services/test_activity_log_service.py::TestActivityLogService::test_log_user_action_invalid_entity_type", "tests/test_services/test_activity_log_service.py::TestActivityLogService::test_log_user_action_success", "tests/test_services/test_activity_log_service.py::TestActivityLogService::test_service_initialization", "tests/test_services/test_activity_log_service.py::TestActivityLogService::test_transaction_rollback_on_error", "tests/test_services/test_activity_log_service.py::TestActivityLogService::test_update_activity_log_not_found", "tests/test_services/test_activity_log_service.py::TestActivityLogService::test_update_activity_log_success", "tests/test_services/test_component_service.py::TestComponentService::test_create_component_category_not_found", "tests/test_services/test_component_service.py::TestComponentService::test_create_component_duplicate_name", "tests/test_services/test_component_service.py::TestComponentService::test_create_component_integrity_error", "tests/test_services/test_component_service.py::TestComponentService::test_create_component_success", "tests/test_services/test_component_service.py::TestComponentService::test_delete_component_not_found", "tests/test_services/test_component_service.py::TestComponentService::test_delete_component_success", "tests/test_services/test_component_service.py::TestComponentService::test_get_component_details_deleted", "tests/test_services/test_component_service.py::TestComponentService::test_get_component_details_not_found", "tests/test_services/test_component_service.py::TestComponentService::test_get_component_details_success", "tests/test_services/test_component_service.py::TestComponentService::test_get_components_list_success", "tests/test_services/test_component_service.py::TestComponentService::test_get_components_list_with_category_filter", "tests/test_services/test_component_service.py::TestComponentService::test_get_components_list_with_search", "tests/test_services/test_component_service.py::TestComponentService::test_update_component_not_found", "tests/test_services/test_component_service.py::TestComponentService::test_update_component_success", "tests/test_services/test_electrical_service.py::TestElectricalService::test_cable_sizing_calculation_error_handling", "tests/test_services/test_electrical_service.py::TestElectricalService::test_cable_sizing_calculation_success", "tests/test_services/test_electrical_service.py::TestElectricalService::test_calculate_load_for_electrical_node_success", "tests/test_services/test_electrical_service.py::TestElectricalService::test_calculate_load_for_nonexistent_node", "tests/test_services/test_electrical_service.py::TestElectricalService::test_electrical_standards_validation_success", "tests/test_services/test_electrical_service.py::TestElectricalService::test_generate_recommendations", "tests/test_services/test_electrical_service.py::TestElectricalService::test_voltage_drop_calculation_error_handling", "tests/test_services/test_electrical_service.py::TestElectricalService::test_voltage_drop_calculation_success", "tests/test_services/test_heat_tracing_service.py::TestHeatTracingServiceCalculations::test_calculate_pipe_heat_loss_pipe_not_found", "tests/test_services/test_heat_tracing_service.py::TestHeatTracingServiceCalculations::test_calculate_pipe_heat_loss_success", "tests/test_services/test_heat_tracing_service.py::TestHeatTracingServiceCalculations::test_validate_standards_compliance_success", "tests/test_services/test_heat_tracing_service.py::TestHeatTracingServiceDesignWorkflow::test_execute_design_workflow_success", "tests/test_services/test_heat_tracing_service.py::TestHeatTracingServicePipeOperations::test_create_pipe_duplicate_error", "tests/test_services/test_heat_tracing_service.py::TestHeatTracingServicePipeOperations::test_create_pipe_success", "tests/test_services/test_heat_tracing_service.py::TestHeatTracingServicePipeOperations::test_create_pipe_validation_error", "tests/test_services/test_heat_tracing_service.py::TestHeatTracingServicePipeOperations::test_delete_pipe_success", "tests/test_services/test_heat_tracing_service.py::TestHeatTracingServicePipeOperations::test_get_pipe_details_deleted", "tests/test_services/test_heat_tracing_service.py::TestHeatTracingServicePipeOperations::test_get_pipe_details_not_found", "tests/test_services/test_heat_tracing_service.py::TestHeatTracingServicePipeOperations::test_get_pipe_details_success", "tests/test_services/test_heat_tracing_service.py::TestHeatTracingServicePipeOperations::test_get_pipes_list_success", "tests/test_services/test_heat_tracing_service.py::TestHeatTracingServicePipeOperations::test_update_pipe_not_found", "tests/test_services/test_heat_tracing_service.py::TestHeatTracingServicePipeOperations::test_update_pipe_success", "tests/test_services/test_heat_tracing_service.py::TestHeatTracingServiceVesselOperations::test_create_vessel_invalid_dimensions", "tests/test_services/test_heat_tracing_service.py::TestHeatTracingServiceVesselOperations::test_create_vessel_success", "tests/test_services/test_heat_tracing_service.py::TestHeatTracingServiceVesselOperations::test_get_vessel_details_success", "tests/test_services/test_project_service.py::TestProjectService::test_create_project_duplicate_name_error", "tests/test_services/test_project_service.py::TestProjectService::test_create_project_success", "tests/test_services/test_project_service.py::TestProjectService::test_create_project_validation_error", "tests/test_services/test_project_service.py::TestProjectService::test_delete_project_success", "tests/test_services/test_project_service.py::TestProjectService::test_get_project_details_by_code", "tests/test_services/test_project_service.py::TestProjectService::test_get_project_details_by_id", "tests/test_services/test_project_service.py::TestProjectService::test_get_project_details_not_found", "tests/test_services/test_project_service.py::TestProjectService::test_get_projects_list_success", "tests/test_services/test_project_service.py::TestProjectService::test_update_project_success", "tests/test_services/test_project_service.py::TestProjectServiceIntegrationPatterns::test_complete_workflow_with_utils_and_di", "tests/test_services/test_project_service.py::TestProjectServiceWithDependencyInjection::test_multiple_services_with_shared_dependencies", "tests/test_services/test_project_service.py::TestProjectServiceWithDependencyInjection::test_service_creation_with_di", "tests/test_services/test_project_service.py::TestProjectServiceWithDependencyInjection::test_service_method_with_repository_di", "tests/test_services/test_project_service.py::TestProjectServiceWithUtilities::test_create_project_with_string_sanitization", "tests/test_services/test_project_service.py::TestProjectServiceWithUtilities::test_get_projects_with_pagination_utils", "tests/test_services/test_project_service.py::TestProjectServiceWithUtilities::test_update_project_with_datetime_utils", "tests/test_services/test_switchboard_service.py::TestSwitchboardService::test_add_switchboard_component_component_not_found", "tests/test_services/test_switchboard_service.py::TestSwitchboardService::test_add_switchboard_component_success", "tests/test_services/test_switchboard_service.py::TestSwitchboardService::test_add_switchboard_component_switchboard_not_found", "tests/test_services/test_switchboard_service.py::TestSwitchboardService::test_create_feeder_success", "tests/test_services/test_switchboard_service.py::TestSwitchboardService::test_create_feeder_switchboard_not_found", "tests/test_services/test_switchboard_service.py::TestSwitchboardService::test_create_switchboard_invalid_voltage", "tests/test_services/test_switchboard_service.py::TestSwitchboardService::test_create_switchboard_success", "tests/test_services/test_switchboard_service.py::TestSwitchboardService::test_delete_switchboard_not_found", "tests/test_services/test_switchboard_service.py::TestSwitchboardService::test_delete_switchboard_success", "tests/test_services/test_switchboard_service.py::TestSwitchboardService::test_get_switchboard_capacity_analysis_success", "tests/test_services/test_switchboard_service.py::TestSwitchboardService::test_get_switchboard_load_summary_success", "tests/test_services/test_switchboard_service.py::TestSwitchboardService::test_get_switchboard_not_found", "tests/test_services/test_switchboard_service.py::TestSwitchboardService::test_get_switchboard_success", "tests/test_services/test_switchboard_service.py::TestSwitchboardService::test_update_switchboard_invalid_voltage", "tests/test_services/test_switchboard_service.py::TestSwitchboardService::test_update_switchboard_success", "tests/test_services/test_user_service.py::TestUserService::test_change_password_invalid_current", "tests/test_services/test_user_service.py::TestUserService::test_change_password_success", "tests/test_services/test_user_service.py::TestUserService::test_count_active_users_success", "tests/test_services/test_user_service.py::TestUserService::test_create_or_update_user_preferences_success", "tests/test_services/test_user_service.py::TestUserService::test_create_user_email_already_exists", "tests/test_services/test_user_service.py::TestUserService::test_create_user_success", "tests/test_services/test_user_service.py::TestUserService::test_deactivate_user_success", "tests/test_services/test_user_service.py::TestUserService::test_get_user_inactive", "tests/test_services/test_user_service.py::TestUserService::test_get_user_not_found", "tests/test_services/test_user_service.py::TestUserService::test_get_user_preferences_not_found", "tests/test_services/test_user_service.py::TestUserService::test_get_user_preferences_success", "tests/test_services/test_user_service.py::TestUserService::test_get_user_success", "tests/test_services/test_user_service.py::TestUserService::test_hash_password", "tests/test_services/test_user_service.py::TestUserService::test_login_inactive_user", "tests/test_services/test_user_service.py::TestUserService::test_login_invalid_email", "tests/test_services/test_user_service.py::TestUserService::test_login_invalid_password", "tests/test_services/test_user_service.py::TestUserService::test_login_success", "tests/test_services/test_user_service.py::TestUserService::test_search_users_success", "tests/test_services/test_user_service.py::TestUserService::test_update_user_success", "tests/test_services/test_user_service.py::TestUserService::test_verify_password"]