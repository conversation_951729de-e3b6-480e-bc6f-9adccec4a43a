"""Add remaining electrical tables and update JSON field types

Revision ID: 85fee5649379
Revises: 2650a50dd726
Create Date: 2025-06-03 23:16:28.834418

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa

# Import for EnumType and JSON types
import sys
import os

project_root = os.path.abspath(
    os.path.join(os.path.dirname(__file__), "..", "..", "..")
)
sys.path.insert(0, project_root)
from core.models.base import EnumType


# revision identifiers, used by Alembic.
revision: str = "85fee5649379"
down_revision: Union[str, None] = "2650a50dd726"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "CableSegment",
        sa.Column("project_id", sa.Integer(), nullable=False),
        sa.Column("cable_route_id", sa.Integer(), nullable=False),
        sa.Column("cable_component_id", sa.Integer(), nullable=False),
        sa.Column("segment_order", sa.Integer(), nullable=False),
        sa.Column("length_m", sa.Float(), nullable=False),
        sa.Column("installation_method", EnumType(), nullable=False),
        sa.Column("ambient_temperature_c", sa.Float(), nullable=True),
        sa.Column("ground_temperature_c", sa.Float(), nullable=True),
        sa.Column("burial_depth_m", sa.Float(), nullable=True),
        sa.Column("conductor_size_mm2", sa.Float(), nullable=True),
        sa.Column("insulation_type", sa.String(), nullable=True),
        sa.Column("sheath_type", sa.String(), nullable=True),
        sa.Column("armour_type", sa.String(), nullable=True),
        sa.Column("calculated_resistance_ohm_per_m", sa.Float(), nullable=True),
        sa.Column("calculated_reactance_ohm_per_m", sa.Float(), nullable=True),
        sa.Column("calculated_current_capacity_a", sa.Float(), nullable=True),
        sa.Column("calculated_voltage_drop_v", sa.Float(), nullable=True),
        sa.Column("calculated_power_loss_w", sa.Float(), nullable=True),
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column("name", sa.String(), nullable=False),
        sa.Column("notes", sa.String(), nullable=True),
        sa.Column("created_at", sa.DateTime(), nullable=False),
        sa.Column("updated_at", sa.DateTime(), nullable=False),
        sa.Column("is_deleted", sa.Boolean(), nullable=False),
        sa.Column("deleted_at", sa.DateTime(), nullable=True),
        sa.Column("deleted_by_user_id", sa.Integer(), nullable=True),
        sa.CheckConstraint("length_m > 0", name="chk_segment_length_positive"),
        sa.CheckConstraint("segment_order > 0", name="chk_segment_order_positive"),
        sa.ForeignKeyConstraint(
            ["cable_component_id"],
            ["Component.id"],
        ),
        sa.ForeignKeyConstraint(
            ["cable_route_id"],
            ["CableRoute.id"],
        ),
        sa.ForeignKeyConstraint(
            ["deleted_by_user_id"],
            ["User.id"],
        ),
        sa.ForeignKeyConstraint(
            ["project_id"],
            ["Project.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint(
            "cable_route_id", "segment_order", name="uq_cable_segment_order"
        ),
    )
    op.create_table(
        "VoltageDropCalculation",
        sa.Column("project_id", sa.Integer(), nullable=False),
        sa.Column("cable_route_id", sa.Integer(), nullable=False),
        sa.Column("load_calculation_id", sa.Integer(), nullable=True),
        sa.Column("supply_voltage_v", sa.Float(), nullable=False),
        sa.Column("load_current_a", sa.Float(), nullable=False),
        sa.Column("cable_length_m", sa.Float(), nullable=False),
        sa.Column("cable_resistance_ohm_per_m", sa.Float(), nullable=False),
        sa.Column("cable_reactance_ohm_per_m", sa.Float(), nullable=False),
        sa.Column("power_factor", sa.Float(), nullable=False),
        sa.Column("ambient_temperature_c", sa.Float(), nullable=False),
        sa.Column("ground_temperature_c", sa.Float(), nullable=True),
        sa.Column("derating_factor", sa.Float(), nullable=False),
        sa.Column("calculated_voltage_drop_v", sa.Float(), nullable=True),
        sa.Column("calculated_voltage_drop_percent", sa.Float(), nullable=True),
        sa.Column("calculated_power_loss_w", sa.Float(), nullable=True),
        sa.Column("calculated_efficiency_percent", sa.Float(), nullable=True),
        sa.Column("max_allowed_voltage_drop_percent", sa.Float(), nullable=False),
        sa.Column("is_compliant", sa.Boolean(), nullable=True),
        sa.Column("compliance_margin_percent", sa.Float(), nullable=True),
        sa.Column("calculation_method", sa.String(), nullable=False),
        sa.Column("calculation_standard", sa.String(), nullable=True),
        sa.Column("calculation_notes", sa.Text(), nullable=True),
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column("name", sa.String(), nullable=False),
        sa.Column("notes", sa.String(), nullable=True),
        sa.Column("created_at", sa.DateTime(), nullable=False),
        sa.Column("updated_at", sa.DateTime(), nullable=False),
        sa.Column("is_deleted", sa.Boolean(), nullable=False),
        sa.Column("deleted_at", sa.DateTime(), nullable=True),
        sa.Column("deleted_by_user_id", sa.Integer(), nullable=True),
        sa.CheckConstraint("cable_length_m > 0", name="chk_cable_length_positive"),
        sa.CheckConstraint(
            "cable_reactance_ohm_per_m >= 0", name="chk_cable_reactance_non_negative"
        ),
        sa.CheckConstraint(
            "cable_resistance_ohm_per_m >= 0", name="chk_cable_resistance_non_negative"
        ),
        sa.CheckConstraint(
            "derating_factor > 0 AND derating_factor <= 1",
            name="chk_derating_factor_range",
        ),
        sa.CheckConstraint("load_current_a > 0", name="chk_load_current_positive"),
        sa.CheckConstraint(
            "max_allowed_voltage_drop_percent > 0 AND max_allowed_voltage_drop_percent <= 50",
            name="chk_max_voltage_drop_range",
        ),
        sa.CheckConstraint(
            "power_factor > 0 AND power_factor <= 1", name="chk_vd_power_factor_range"
        ),
        sa.CheckConstraint("supply_voltage_v > 0", name="chk_supply_voltage_positive"),
        sa.ForeignKeyConstraint(
            ["cable_route_id"],
            ["CableRoute.id"],
        ),
        sa.ForeignKeyConstraint(
            ["deleted_by_user_id"],
            ["User.id"],
        ),
        sa.ForeignKeyConstraint(
            ["load_calculation_id"],
            ["LoadCalculation.id"],
        ),
        sa.ForeignKeyConstraint(
            ["project_id"],
            ["Project.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    # Note: SQLite doesn't support ALTER COLUMN TYPE operations
    # The JSON field type changes will be handled at the application level
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table("VoltageDropCalculation")
    op.drop_table("CableSegment")
    # Note: Column type changes are not reverted due to SQLite limitations
    # ### end Alembic commands ###
