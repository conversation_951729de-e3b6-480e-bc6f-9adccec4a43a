# backend/api/dependencies.py
"""
API Dependencies

This module provides common dependencies for FastAPI routes including:
- Authentication and authorization
- Request validation
- Common API utilities

Database and service dependencies are provided by their respective modules:
- core.database.dependencies for database sessions
- core.repositories.dependencies for repository instances
- core.services.dependencies for service instances
"""

from typing import Dict, Any, Optional
from fastapi import Depends, HTTPException, status
from fastapi.security import OAuth2PasswordBearer

# OAuth2 scheme for token-based authentication
# In production, this would point to your actual token endpoint
oauth2_scheme = OAuth2PasswordBearer(
    tokenUrl="/api/v1/auth/token",
    auto_error=False,  # Allow optional authentication
)


async def get_current_user(
    token: Optional[str] = Depends(oauth2_scheme),
) -> Dict[str, Any]:
    """
    Get current authenticated user from JWT token.

    This is a simplified implementation for development/testing.
    In production, this would validate JWT tokens and return user data.

    Args:
        token: JWT token from OAuth2 scheme

    Returns:
        Dict containing user information

    Raises:
        HTTPException: If authentication fails
    """
    # For development/testing, return a mock admin user
    # In production, this would:
    # 1. Validate JWT token signature and expiration
    # 2. Decode token to get user ID and claims
    # 3. Query database for current user details
    # 4. Return user information

    if not token:
        # For development, allow requests without auth
        return {
            "id": 1,
            "name": "Development User",
            "email": "<EMAIL>",
            "is_admin": True,
            "is_active": True,
        }

    # In production, validate token here using JWT library
    # For now, return mock user data
    return {
        "id": 1,
        "name": "Development User",
        "email": "<EMAIL>",
        "is_admin": True,
        "is_active": True,
    }


async def get_admin_user(
    current_user: Dict[str, Any] = Depends(get_current_user),
) -> Dict[str, Any]:
    """
    Dependency that requires admin privileges.

    Args:
        current_user: Current authenticated user

    Returns:
        Dict containing admin user information

    Raises:
        HTTPException: If user is not an admin
    """
    if not current_user.get("is_admin", False):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN, detail="Admin privileges required"
        )

    return current_user


async def get_active_user(
    current_user: Dict[str, Any] = Depends(get_current_user),
) -> Dict[str, Any]:
    """
    Dependency that requires an active user account.

    Args:
        current_user: Current authenticated user

    Returns:
        Dict containing active user information

    Raises:
        HTTPException: If user account is not active
    """
    if not current_user.get("is_active", False):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN, detail="Active user account required"
        )

    return current_user
