# backend/api/v1/document_routes.py
"""
Document API Routes

This module provides REST API endpoints for document management including:
- Data import/export operations
- Report generation and download
- Calculation standards management
- File upload and download handling
"""

from typing import Optional
from fastapi import APIRouter, Depends, HTTPException, Query, status, UploadFile, File
from fastapi.responses import FileResponse

try:
    from config.logging_config import get_logger

    from core.errors.exceptions import (
        BaseApplicationException,
        NotFoundError,
        DataValidationError,
        DuplicateEntryError,
        DatabaseError,
        InvalidInputError,
    )
    from core.schemas.document_schemas import (
        ImportedDataRevisionCreateSchema,
        ImportedDataRevisionUpdateSchema,
        ImportedDataRevisionReadSchema,
        ImportedDataRevisionListResponseSchema,
        ExportedDocumentCreateSchema,
        ExportedDocumentUpdateSchema,
        ExportedDocumentReadSchema,
        ExportedDocumentListResponseSchema,
        CalculationStandardCreateSchema,
        CalculationStandardUpdateSchema,
        CalculationStandardReadSchema,
        CalculationStandardListResponseSchema,
        DocumentGenerationRequestSchema,
        FileUploadSchema,
        FileDownloadSchema,
    )
    from core.services.document_service import DocumentService
    from core.repositories.document_repository import (
        ImportedDataRevisionRepository,
        ExportedDocumentRepository,
        CalculationStandardRepository,
    )
    from core.repositories.project_repository import ProjectRepository
    from core.repositories.user_repository import UserRepository
except ImportError:
    # For testing and relative imports
    import sys
    import os

    sys.path.insert(
        0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    )
    from config.logging_config import get_logger
    from core.database.session import get_db_session
    from core.errors.exceptions import (
        BaseApplicationException,
        NotFoundError,
        DataValidationError,
        DuplicateEntryError,
        DatabaseError,
        InvalidInputError,
    )
    from core.schemas.document_schemas import (
        ImportedDataRevisionCreateSchema,
        ImportedDataRevisionUpdateSchema,
        ImportedDataRevisionReadSchema,
        ImportedDataRevisionListResponseSchema,
        ExportedDocumentCreateSchema,
        ExportedDocumentUpdateSchema,
        ExportedDocumentReadSchema,
        ExportedDocumentListResponseSchema,
        CalculationStandardCreateSchema,
        CalculationStandardUpdateSchema,
        CalculationStandardReadSchema,
        CalculationStandardListResponseSchema,
        DocumentGenerationRequestSchema,
        FileUploadSchema,
        FileDownloadSchema,
    )
    from core.services.document_service import DocumentService
    from core.repositories.document_repository import (
        ImportedDataRevisionRepository,
        ExportedDocumentRepository,
        CalculationStandardRepository,
    )
    from core.repositories.project_repository import ProjectRepository
    from core.repositories.user_repository import UserRepository

# Initialize router and logger
router = APIRouter(tags=["documents"])
logger = get_logger(__name__)


def handle_document_exceptions(e: BaseApplicationException) -> HTTPException:
    """
    Convert application exceptions to HTTP exceptions for document operations.

    Args:
        e: Application exception

    Returns:
        HTTPException: Appropriate HTTP exception
    """
    if isinstance(e, NotFoundError):
        logger.warning(f"Resource not found: {e.detail}")
        return HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=e.detail,
        )
    elif isinstance(e, (DataValidationError, InvalidInputError)):
        logger.warning(f"Validation error: {e.detail}")
        return HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=e.detail,
        )
    elif isinstance(e, DuplicateEntryError):
        logger.warning(f"Duplicate entry error: {e.detail}")
        return HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail=e.detail,
        )
    elif isinstance(e, DatabaseError):
        logger.error(f"Database error: {e.detail}")
        return HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )
    elif isinstance(e, BaseApplicationException):
        logger.error(f"Application error: {e.detail}")
        return HTTPException(
            status_code=e.status_code,
            detail=e.detail,
        )
    else:
        logger.error(f"Unexpected error: {str(e)}")
        return HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


# Import dependency injection provider
from core.services.dependencies import get_document_service


# ============================================================================
# IMPORTED DATA REVISION ENDPOINTS
# ============================================================================


@router.post(
    "/import-revisions",
    response_model=ImportedDataRevisionReadSchema,
    status_code=status.HTTP_201_CREATED,
    summary="Create imported data revision",
    description="Create a new imported data revision for tracking data imports",
)
async def create_imported_data_revision(
    revision_data: ImportedDataRevisionCreateSchema,
    document_service: DocumentService = Depends(get_document_service),
) -> ImportedDataRevisionReadSchema:
    """Create a new imported data revision."""
    try:
        logger.info(
            f"Creating imported data revision for project {revision_data.project_id}"
        )
        return document_service.create_imported_data_revision(revision_data)
    except BaseApplicationException as e:
        raise handle_document_exceptions(e)


@router.get(
    "/import-revisions/{revision_id}",
    response_model=ImportedDataRevisionReadSchema,
    summary="Get imported data revision",
    description="Retrieve a specific imported data revision by ID",
)
async def get_imported_data_revision(
    revision_id: int,
    document_service: DocumentService = Depends(get_document_service),
) -> ImportedDataRevisionReadSchema:
    """Get imported data revision by ID."""
    try:
        logger.info(f"Retrieving imported data revision {revision_id}")
        return document_service.get_imported_data_revision_by_id(revision_id)
    except BaseApplicationException as e:
        raise handle_document_exceptions(e)


@router.get(
    "/import-revisions",
    response_model=ImportedDataRevisionListResponseSchema,
    summary="List imported data revisions",
    description="Retrieve imported data revisions for a project with pagination",
)
async def list_imported_data_revisions(
    project_id: int = Query(..., description="Project ID to filter by"),
    page: int = Query(1, ge=1, description="Page number"),
    per_page: int = Query(10, ge=1, le=100, description="Items per page"),
    document_service: DocumentService = Depends(get_document_service),
) -> ImportedDataRevisionListResponseSchema:
    """List imported data revisions for a project."""
    try:
        logger.info(f"Listing imported data revisions for project {project_id}")
        skip = (page - 1) * per_page
        revisions, total = document_service.get_imported_data_revisions_by_project(
            project_id, skip, per_page
        )

        return ImportedDataRevisionListResponseSchema(
            revisions=revisions,
            total=total,
            page=page,
            per_page=per_page,
            total_pages=(total + per_page - 1) // per_page,
        )
    except BaseApplicationException as e:
        raise handle_document_exceptions(e)


@router.put(
    "/import-revisions/{revision_id}",
    response_model=ImportedDataRevisionReadSchema,
    summary="Update imported data revision",
    description="Update an existing imported data revision",
)
async def update_imported_data_revision(
    revision_id: int,
    revision_data: ImportedDataRevisionUpdateSchema,
    document_service: DocumentService = Depends(get_document_service),
) -> ImportedDataRevisionReadSchema:
    """Update imported data revision."""
    try:
        logger.info(f"Updating imported data revision {revision_id}")
        return document_service.update_imported_data_revision(
            revision_id, revision_data
        )
    except BaseApplicationException as e:
        raise handle_document_exceptions(e)


@router.delete(
    "/import-revisions/{revision_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="Delete imported data revision",
    description="Soft delete an imported data revision",
)
async def delete_imported_data_revision(
    revision_id: int,
    document_service: DocumentService = Depends(get_document_service),
) -> None:
    """Delete imported data revision."""
    try:
        logger.info(f"Deleting imported data revision {revision_id}")
        document_service.delete_imported_data_revision(revision_id)
    except BaseApplicationException as e:
        raise handle_document_exceptions(e)


# ============================================================================
# EXPORTED DOCUMENT ENDPOINTS
# ============================================================================


@router.post(
    "/export-documents",
    response_model=ExportedDocumentReadSchema,
    status_code=status.HTTP_201_CREATED,
    summary="Create exported document",
    description="Create a new exported document record for tracking generated reports",
)
async def create_exported_document(
    document_data: ExportedDocumentCreateSchema,
    document_service: DocumentService = Depends(get_document_service),
) -> ExportedDocumentReadSchema:
    """Create a new exported document."""
    try:
        logger.info(
            f"Creating exported document for project {document_data.project_id}"
        )
        return document_service.create_exported_document(document_data)
    except BaseApplicationException as e:
        raise handle_document_exceptions(e)


@router.get(
    "/export-documents/{document_id}",
    response_model=ExportedDocumentReadSchema,
    summary="Get exported document",
    description="Retrieve a specific exported document by ID",
)
async def get_exported_document(
    document_id: int,
    document_service: DocumentService = Depends(get_document_service),
) -> ExportedDocumentReadSchema:
    """Get exported document by ID."""
    try:
        logger.info(f"Retrieving exported document {document_id}")
        return document_service.get_exported_document_by_id(document_id)
    except BaseApplicationException as e:
        raise handle_document_exceptions(e)


@router.get(
    "/export-documents",
    response_model=ExportedDocumentListResponseSchema,
    summary="List exported documents",
    description="Retrieve exported documents for a project with pagination",
)
async def list_exported_documents(
    project_id: int = Query(..., description="Project ID to filter by"),
    page: int = Query(1, ge=1, description="Page number"),
    per_page: int = Query(10, ge=1, le=100, description="Items per page"),
    document_service: DocumentService = Depends(get_document_service),
) -> ExportedDocumentListResponseSchema:
    """List exported documents for a project."""
    try:
        logger.info(f"Listing exported documents for project {project_id}")
        skip = (page - 1) * per_page
        documents, total = document_service.get_exported_documents_by_project(
            project_id, skip, per_page
        )

        return ExportedDocumentListResponseSchema(
            documents=documents,
            total=total,
            page=page,
            per_page=per_page,
            total_pages=(total + per_page - 1) // per_page,
        )
    except BaseApplicationException as e:
        raise handle_document_exceptions(e)


# ============================================================================
# CALCULATION STANDARD ENDPOINTS
# ============================================================================


@router.post(
    "/calculation-standards",
    response_model=CalculationStandardReadSchema,
    status_code=status.HTTP_201_CREATED,
    summary="Create calculation standard",
    description="Create a new calculation standard for engineering calculations",
)
async def create_calculation_standard(
    standard_data: CalculationStandardCreateSchema,
    document_service: DocumentService = Depends(get_document_service),
) -> CalculationStandardReadSchema:
    """Create a new calculation standard."""
    try:
        logger.info(f"Creating calculation standard: {standard_data.standard_code}")
        return document_service.create_calculation_standard(standard_data)
    except BaseApplicationException as e:
        raise handle_document_exceptions(e)


@router.get(
    "/calculation-standards/{standard_id}",
    response_model=CalculationStandardReadSchema,
    summary="Get calculation standard",
    description="Retrieve a specific calculation standard by ID",
)
async def get_calculation_standard(
    standard_id: int,
    document_service: DocumentService = Depends(get_document_service),
) -> CalculationStandardReadSchema:
    """Get calculation standard by ID."""
    try:
        logger.info(f"Retrieving calculation standard {standard_id}")
        return document_service.get_calculation_standard_by_id(standard_id)
    except BaseApplicationException as e:
        raise handle_document_exceptions(e)


@router.get(
    "/calculation-standards/by-code/{standard_code}",
    response_model=CalculationStandardReadSchema,
    summary="Get calculation standard by code",
    description="Retrieve a specific calculation standard by code",
)
async def get_calculation_standard_by_code(
    standard_code: str,
    document_service: DocumentService = Depends(get_document_service),
) -> CalculationStandardReadSchema:
    """Get calculation standard by code."""
    try:
        logger.info(f"Retrieving calculation standard by code: {standard_code}")
        return document_service.get_calculation_standard_by_code(standard_code)
    except BaseApplicationException as e:
        raise handle_document_exceptions(e)


@router.get(
    "/calculation-standards",
    response_model=CalculationStandardListResponseSchema,
    summary="List calculation standards",
    description="Retrieve all calculation standards with pagination",
)
async def list_calculation_standards(
    page: int = Query(1, ge=1, description="Page number"),
    per_page: int = Query(10, ge=1, le=100, description="Items per page"),
    document_service: DocumentService = Depends(get_document_service),
) -> CalculationStandardListResponseSchema:
    """List all calculation standards."""
    try:
        logger.info("Listing calculation standards")
        skip = (page - 1) * per_page
        standards, total = document_service.get_all_calculation_standards(
            skip, per_page
        )

        return CalculationStandardListResponseSchema(
            standards=standards,
            total=total,
            page=page,
            per_page=per_page,
            total_pages=(total + per_page - 1) // per_page,
        )
    except BaseApplicationException as e:
        raise handle_document_exceptions(e)


@router.put(
    "/calculation-standards/{standard_id}",
    response_model=CalculationStandardReadSchema,
    summary="Update calculation standard",
    description="Update an existing calculation standard",
)
async def update_calculation_standard(
    standard_id: int,
    standard_data: CalculationStandardUpdateSchema,
    document_service: DocumentService = Depends(get_document_service),
) -> CalculationStandardReadSchema:
    """Update calculation standard."""
    try:
        logger.info(f"Updating calculation standard {standard_id}")
        return document_service.update_calculation_standard(standard_id, standard_data)
    except BaseApplicationException as e:
        raise handle_document_exceptions(e)


@router.delete(
    "/calculation-standards/{standard_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="Delete calculation standard",
    description="Soft delete a calculation standard",
)
async def delete_calculation_standard(
    standard_id: int,
    document_service: DocumentService = Depends(get_document_service),
) -> None:
    """Delete calculation standard."""
    try:
        logger.info(f"Deleting calculation standard {standard_id}")
        document_service.delete_calculation_standard(standard_id)
    except BaseApplicationException as e:
        raise handle_document_exceptions(e)


# ============================================================================
# FILE MANAGEMENT ENDPOINTS
# ============================================================================


@router.post(
    "/upload",
    response_model=dict,
    status_code=status.HTTP_201_CREATED,
    summary="Upload file",
    description="Upload a file for data import processing",
)
async def upload_file(
    file: UploadFile = File(...),
    project_id: int = Query(..., description="Project ID for file association"),
    import_type: Optional[str] = Query(None, description="Type of data import"),
    document_service: DocumentService = Depends(get_document_service),  # ruff: noqa: ARG001
) -> dict:
    """Upload a file for processing."""
    try:
        logger.info(f"Uploading file: {file.filename} for project {project_id}")

        # Basic file validation
        if not file.filename:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="No filename provided"
            )

        # TODO: Implement actual file upload logic
        # This would involve:
        # 1. Validating file type and size
        # 2. Saving file to secure storage
        # 3. Creating ImportedDataRevision record
        # 4. Processing file content if needed

        return {
            "message": "File uploaded successfully",
            "filename": file.filename,
            "project_id": project_id,
            "import_type": import_type,
            "status": "uploaded",
        }
    except BaseApplicationException as e:
        raise handle_document_exceptions(e)


@router.get(
    "/download/{document_id}",
    response_class=FileResponse,
    summary="Download document",
    description="Download a generated document file",
)
async def download_document(
    document_id: int,
    document_service: DocumentService = Depends(get_document_service),
) -> FileResponse:
    """Download a document file."""
    try:
        logger.info(f"Downloading document {document_id}")

        # Get document metadata
        document = document_service.get_exported_document_by_id(document_id)  # ruff: noqa: F841

        # TODO: Implement actual file download logic
        # This would involve:
        # 1. Validating user permissions
        # 2. Checking if file exists
        # 3. Returning file response

        # For now, return a placeholder response
        raise HTTPException(
            status_code=status.HTTP_501_NOT_IMPLEMENTED,
            detail="File download not yet implemented",
        )

    except BaseApplicationException as e:
        raise handle_document_exceptions(e)
