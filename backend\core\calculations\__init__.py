# backend/core/calculations/__init__.py
"""
Calculations Layer - Engineering calculations and algorithms.

This module provides the core engineering calculations required for heat tracing design,
including heat loss calculations, electrical sizing, and circuit design algorithms.

The calculations layer is organized into specialized sub-packages:
- heat_loss: Heat loss calculations for pipes and vessels
- power: Power calculations for electrical and heat tracing systems
- electrical_sizing: Cable sizing and electrical calculations
- circuit_design: Circuit breaker sizing and control logic
- common_properties: Material properties and lookup tables
- utils: Calculation utilities and helpers

All calculations follow strict error handling patterns and integrate with
the standards validation layer.
"""

from .calculation_service import CalculationService

# Import new sub-packages
from . import heat_loss
from . import power
from . import circuit_design
from . import common_properties
from . import utils

__all__ = [
    "CalculationService",
    "heat_loss",
    "power",
    "circuit_design",
    "common_properties",
    "utils",
]
