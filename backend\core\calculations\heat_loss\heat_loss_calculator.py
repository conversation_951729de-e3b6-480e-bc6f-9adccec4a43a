# backend/core/calculations/heat_loss/heat_loss_calculator.py
"""
Heat Loss Calculator Class.

This module provides a class-based interface for heat loss calculations,
wrapping the functional heat loss calculation modules.
"""

import logging
from typing import Dict, Any, Optional
from dataclasses import dataclass

from .pipe_heat_loss import calculate_pipe_heat_loss
from .vessel_heat_loss import calculate_vessel_heat_loss
from .insulation_properties import get_insulation_properties
from core.errors.exceptions import CalculationError, InvalidInputError

logger = logging.getLogger(__name__)


@dataclass
class HeatLossInput:
    """Input parameters for heat loss calculations."""

    pipe_diameter: Optional[float] = None
    insulation_thickness: Optional[float] = None
    maintain_temperature: Optional[float] = None
    ambient_temperature: Optional[float] = None
    vessel_diameter: Optional[float] = None
    vessel_height: Optional[float] = None
    insulation_type: Optional[str] = None
    wind_speed: Optional[float] = None
    surface_emissivity: Optional[float] = None


@dataclass
class HeatLossResult:
    """Result of heat loss calculations."""

    heat_loss_per_meter: float
    thermal_resistance: float
    surface_temperature: float
    calculation_type: str
    input_parameters: Dict[str, Any]
    warnings: list = None


class HeatLossCalculator:
    """
    Calculator for heat loss in pipes and vessels.

    This class provides a unified interface for calculating heat loss
    in various geometries including pipes and vessels.
    """

    def __init__(self):
        """Initialize the heat loss calculator."""
        self.supported_geometries = ["pipe", "vessel"]
        self.default_ambient_temp = 20.0  # °C
        self.default_wind_speed = 3.0  # m/s
        logger.debug("HeatLossCalculator initialized")

    def calculate_heat_loss(self, circuit_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Calculate heat loss for a circuit.

        Args:
            circuit_data: Dictionary containing circuit parameters

        Returns:
            Dictionary containing heat loss calculation results

        Raises:
            InvalidInputError: If required parameters are missing
            CalculationError: If calculation fails
        """
        logger.info("Starting heat loss calculation")

        try:
            # Validate input data
            self._validate_input(circuit_data)

            # Determine calculation type based on available parameters
            if "pipe_diameter" in circuit_data:
                return self._calculate_pipe_heat_loss(circuit_data)
            elif "vessel_diameter" in circuit_data:
                return self._calculate_vessel_heat_loss(circuit_data)
            else:
                raise InvalidInputError(
                    "Must specify either pipe_diameter or vessel_diameter"
                )

        except Exception as e:
            logger.error(f"Heat loss calculation failed: {e}")
            if isinstance(e, (InvalidInputError, CalculationError)):
                raise
            else:
                raise CalculationError(f"Heat loss calculation failed: {str(e)}")

    def _calculate_pipe_heat_loss(self, circuit_data: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate heat loss for pipe geometry."""
        logger.debug("Calculating pipe heat loss")

        # Extract parameters
        pipe_diameter = circuit_data["pipe_diameter"]
        insulation_thickness = circuit_data.get("insulation_thickness", 0.05)
        maintain_temp = circuit_data.get("maintain_temperature", 60.0)
        ambient_temp = circuit_data.get(
            "ambient_temperature", self.default_ambient_temp
        )
        wind_speed = circuit_data.get("wind_speed", self.default_wind_speed)

        # Get insulation properties
        insulation_type = circuit_data.get("insulation_type", "mineral_wool")
        insulation_props = get_insulation_properties(insulation_type)
        insulation_conductivity = insulation_props["thermal_conductivity"]

        # Call the functional calculation
        heat_loss_rate = calculate_pipe_heat_loss(
            diameter=pipe_diameter,
            fluid_temp=maintain_temp,
            ambient_temp=ambient_temp,
            insulation_thickness=insulation_thickness,
            insulation_conductivity=insulation_conductivity,
            wind_speed=wind_speed,
        )

        # Format result
        return {
            "heat_loss_per_meter": heat_loss_rate,
            "thermal_resistance": 0.0,  # Could be calculated if needed
            "surface_temperature": ambient_temp,  # Simplified for now
            "calculation_type": "pipe",
            "input_parameters": circuit_data,
            "success": True,
            "warnings": [],
        }

    def _calculate_vessel_heat_loss(
        self, circuit_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Calculate heat loss for vessel geometry."""
        logger.debug("Calculating vessel heat loss")

        # Extract parameters
        vessel_diameter = circuit_data["vessel_diameter"]
        vessel_height = circuit_data.get("vessel_height", 1.0)
        insulation_thickness = circuit_data.get("insulation_thickness", 0.05)
        maintain_temp = circuit_data.get("maintain_temperature", 60.0)
        ambient_temp = circuit_data.get(
            "ambient_temperature", self.default_ambient_temp
        )

        # Get insulation properties
        insulation_type = circuit_data.get("insulation_type", "mineral_wool")
        insulation_props = get_insulation_properties(insulation_type)
        insulation_conductivity = insulation_props["thermal_conductivity"]

        # Call the functional calculation
        result = calculate_vessel_heat_loss(
            diameter=vessel_diameter,
            height=vessel_height,
            fluid_temp=maintain_temp,
            ambient_temp=ambient_temp,
            insulation_thickness=insulation_thickness,
            insulation_conductivity=insulation_conductivity,
        )

        # Format result
        return {
            "heat_loss_per_meter": result.get("heat_loss_total", 0.0),
            "thermal_resistance": result.get("thermal_resistance", 0.0),
            "surface_temperature": result.get("surface_temperature", ambient_temp),
            "calculation_type": "vessel",
            "input_parameters": circuit_data,
            "success": True,
            "warnings": [],
        }

    def _validate_input(self, circuit_data: Dict[str, Any]) -> None:
        """Validate input parameters."""
        if not isinstance(circuit_data, dict):
            raise InvalidInputError("Circuit data must be a dictionary")

        # Check for required parameters
        has_pipe = "pipe_diameter" in circuit_data
        has_vessel = "vessel_diameter" in circuit_data

        if not (has_pipe or has_vessel):
            raise InvalidInputError(
                "Must specify either pipe_diameter or vessel_diameter"
            )

        # Validate numeric parameters
        numeric_params = [
            "pipe_diameter",
            "vessel_diameter",
            "vessel_height",
            "insulation_thickness",
            "maintain_temperature",
            "ambient_temperature",
            "wind_speed",
        ]

        for param in numeric_params:
            if param in circuit_data:
                value = circuit_data[param]
                if not isinstance(value, (int, float)) or value < 0:
                    raise InvalidInputError(
                        f"Parameter '{param}' must be a positive number"
                    )

    def get_insulation_properties(self, insulation_type: str) -> Dict[str, Any]:
        """
        Get insulation properties for a given type.

        Args:
            insulation_type: Type of insulation material

        Returns:
            Dictionary containing insulation properties
        """
        logger.debug(f"Getting insulation properties for: {insulation_type}")

        try:
            return get_insulation_properties(insulation_type)
        except Exception as e:
            logger.error(f"Failed to get insulation properties: {e}")
            raise CalculationError(f"Failed to get insulation properties: {str(e)}")

    def calculate_multiple_circuits(self, circuits: list) -> Dict[str, Any]:
        """
        Calculate heat loss for multiple circuits.

        Args:
            circuits: List of circuit data dictionaries

        Returns:
            Dictionary containing results for all circuits
        """
        logger.info(f"Calculating heat loss for {len(circuits)} circuits")

        results = []
        errors = []

        for i, circuit in enumerate(circuits):
            try:
                result = self.calculate_heat_loss(circuit)
                result["circuit_index"] = i
                results.append(result)
            except Exception as e:
                error_info = {
                    "circuit_index": i,
                    "error": str(e),
                    "circuit_data": circuit,
                }
                errors.append(error_info)
                logger.warning(f"Circuit {i} calculation failed: {e}")

        return {
            "results": results,
            "errors": errors,
            "total_circuits": len(circuits),
            "successful_calculations": len(results),
            "failed_calculations": len(errors),
        }
