# backend/core/calculations/power/electrical_power.py
"""
Electrical Power Calculation Functions.

This module provides functional interfaces for electrical power calculations.
"""

import logging
import math
from typing import Dict, Any, Optional

from core.errors.exceptions import CalculationError, InvalidInputError

logger = logging.getLogger(__name__)


def calculate_electrical_power(
    voltage: Optional[float] = None,
    current: Optional[float] = None,
    resistance: Optional[float] = None,
    power_factor: float = 0.85
) -> Dict[str, Any]:
    """
    Calculate electrical power using available parameters.

    Args:
        voltage: Voltage in volts
        current: Current in amperes
        resistance: Resistance in ohms
        power_factor: Power factor (default: 0.85)

    Returns:
        Dictionary containing calculated electrical parameters

    Raises:
        InvalidInputError: If insufficient parameters provided
        CalculationError: If calculation fails
    """
    logger.debug("Calculating electrical power")
    
    try:
        # Validate inputs
        if sum(x is not None for x in [voltage, current, resistance]) < 2:
            raise InvalidInputError(
                "At least two parameters required: voltage, current, or resistance"
            )
        
        # Calculate missing parameters
        if voltage is not None and current is not None:
            # V and I known
            power = voltage * current * power_factor
            if resistance is None:
                resistance = voltage / current if current != 0 else float('inf')
        elif voltage is not None and resistance is not None:
            # V and R known
            current = voltage / resistance if resistance != 0 else 0
            power = voltage * current * power_factor
        elif current is not None and resistance is not None:
            # I and R known
            voltage = current * resistance
            power = voltage * current * power_factor
        else:
            raise InvalidInputError("Invalid parameter combination")
        
        # Calculate additional parameters
        apparent_power = voltage * current if voltage and current else 0
        reactive_power = math.sqrt(max(0, apparent_power**2 - power**2)) if apparent_power > power else 0
        
        return {
            "power_watts": power,
            "voltage_volts": voltage,
            "current_amperes": current,
            "resistance_ohms": resistance,
            "power_factor": power_factor,
            "apparent_power_va": apparent_power,
            "reactive_power_var": reactive_power,
            "success": True
        }
        
    except Exception as e:
        logger.error(f"Electrical power calculation failed: {e}")
        if isinstance(e, InvalidInputError):
            raise
        else:
            raise CalculationError(f"Electrical power calculation failed: {str(e)}")


def calculate_three_phase_power(
    line_voltage: float,
    line_current: float,
    power_factor: float = 0.85,
    connection_type: str = "wye"
) -> Dict[str, Any]:
    """
    Calculate three-phase electrical power.

    Args:
        line_voltage: Line-to-line voltage in volts
        line_current: Line current in amperes
        power_factor: Power factor (default: 0.85)
        connection_type: Connection type ("wye" or "delta")

    Returns:
        Dictionary containing three-phase power calculations

    Raises:
        InvalidInputError: If invalid parameters provided
        CalculationError: If calculation fails
    """
    logger.debug(f"Calculating three-phase power ({connection_type} connection)")
    
    try:
        if line_voltage <= 0 or line_current <= 0:
            raise InvalidInputError("Voltage and current must be positive")
        
        if connection_type.lower() not in ["wye", "delta"]:
            raise InvalidInputError("Connection type must be 'wye' or 'delta'")
        
        # Three-phase power calculations
        apparent_power = math.sqrt(3) * line_voltage * line_current
        real_power = apparent_power * power_factor
        reactive_power = math.sqrt(max(0, apparent_power**2 - real_power**2))
        
        # Phase values depend on connection type
        if connection_type.lower() == "wye":
            phase_voltage = line_voltage / math.sqrt(3)
            phase_current = line_current
        else:  # delta
            phase_voltage = line_voltage
            phase_current = line_current / math.sqrt(3)
        
        return {
            "real_power_watts": real_power,
            "apparent_power_va": apparent_power,
            "reactive_power_var": reactive_power,
            "line_voltage": line_voltage,
            "line_current": line_current,
            "phase_voltage": phase_voltage,
            "phase_current": phase_current,
            "power_factor": power_factor,
            "connection_type": connection_type,
            "success": True
        }
        
    except Exception as e:
        logger.error(f"Three-phase power calculation failed: {e}")
        if isinstance(e, InvalidInputError):
            raise
        else:
            raise CalculationError(f"Three-phase power calculation failed: {str(e)}")


def calculate_motor_power(
    mechanical_power_hp: float,
    efficiency: float = 0.90,
    power_factor: float = 0.85,
    voltage: Optional[float] = None
) -> Dict[str, Any]:
    """
    Calculate electrical power requirements for a motor.

    Args:
        mechanical_power_hp: Mechanical power output in horsepower
        efficiency: Motor efficiency (default: 0.90)
        power_factor: Motor power factor (default: 0.85)
        voltage: Supply voltage in volts (optional)

    Returns:
        Dictionary containing motor power calculations

    Raises:
        InvalidInputError: If invalid parameters provided
        CalculationError: If calculation fails
    """
    logger.debug("Calculating motor power requirements")
    
    try:
        if mechanical_power_hp <= 0:
            raise InvalidInputError("Mechanical power must be positive")
        
        if not (0 < efficiency <= 1):
            raise InvalidInputError("Efficiency must be between 0 and 1")
        
        if not (0 < power_factor <= 1):
            raise InvalidInputError("Power factor must be between 0 and 1")
        
        # Convert HP to watts (1 HP = 746 watts)
        mechanical_power_watts = mechanical_power_hp * 746
        
        # Calculate electrical power
        electrical_power = mechanical_power_watts / efficiency
        apparent_power = electrical_power / power_factor
        reactive_power = math.sqrt(max(0, apparent_power**2 - electrical_power**2))
        
        result = {
            "mechanical_power_hp": mechanical_power_hp,
            "mechanical_power_watts": mechanical_power_watts,
            "electrical_power_watts": electrical_power,
            "apparent_power_va": apparent_power,
            "reactive_power_var": reactive_power,
            "efficiency": efficiency,
            "power_factor": power_factor,
            "success": True
        }
        
        # Calculate current if voltage is provided
        if voltage:
            current = electrical_power / voltage
            result.update({
                "voltage": voltage,
                "current": current
            })
        
        return result
        
    except Exception as e:
        logger.error(f"Motor power calculation failed: {e}")
        if isinstance(e, InvalidInputError):
            raise
        else:
            raise CalculationError(f"Motor power calculation failed: {str(e)}")


def calculate_power_loss(
    current: float,
    resistance: float,
    length: Optional[float] = None
) -> Dict[str, Any]:
    """
    Calculate power loss in conductors.

    Args:
        current: Current in amperes
        resistance: Resistance per unit length in ohms/meter
        length: Length in meters (optional, default: 1 meter)

    Returns:
        Dictionary containing power loss calculations

    Raises:
        InvalidInputError: If invalid parameters provided
        CalculationError: If calculation fails
    """
    logger.debug("Calculating power loss")
    
    try:
        if current < 0 or resistance < 0:
            raise InvalidInputError("Current and resistance must be non-negative")
        
        if length is None:
            length = 1.0
        elif length <= 0:
            raise InvalidInputError("Length must be positive")
        
        # Calculate power loss: P = I²R
        total_resistance = resistance * length
        power_loss = current**2 * total_resistance
        voltage_drop = current * total_resistance
        
        return {
            "power_loss_watts": power_loss,
            "voltage_drop_volts": voltage_drop,
            "current_amperes": current,
            "resistance_ohms_per_meter": resistance,
            "total_resistance_ohms": total_resistance,
            "length_meters": length,
            "success": True
        }
        
    except Exception as e:
        logger.error(f"Power loss calculation failed: {e}")
        if isinstance(e, InvalidInputError):
            raise
        else:
            raise CalculationError(f"Power loss calculation failed: {str(e)}")
