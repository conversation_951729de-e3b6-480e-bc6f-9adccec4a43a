# backend/core/calculations/power/heat_tracing_power.py
"""
Heat Tracing Power Calculation Functions.

This module provides functional interfaces for heat tracing power calculations.
"""

import logging
import math
from typing import Dict, Any, Optional

from core.errors.exceptions import CalculationError, InvalidInputError

logger = logging.getLogger(__name__)


def calculate_heat_tracing_power(
    heat_loss_per_meter: float,
    length: float,
    safety_factor: float = 1.3,
    efficiency: float = 0.90,
    voltage: Optional[float] = None
) -> Dict[str, Any]:
    """
    Calculate power requirements for heat tracing systems.

    Args:
        heat_loss_per_meter: Heat loss per meter in watts/meter
        length: Total length in meters
        safety_factor: Safety factor for sizing (default: 1.3)
        efficiency: System efficiency (default: 0.90)
        voltage: Supply voltage in volts (optional)

    Returns:
        Dictionary containing heat tracing power calculations

    Raises:
        InvalidInputError: If invalid parameters provided
        CalculationError: If calculation fails
    """
    logger.debug("Calculating heat tracing power requirements")
    
    try:
        # Validate inputs
        if heat_loss_per_meter <= 0:
            raise InvalidInputError("Heat loss per meter must be positive")
        
        if length <= 0:
            raise InvalidInputError("Length must be positive")
        
        if safety_factor <= 0:
            raise InvalidInputError("Safety factor must be positive")
        
        if not (0 < efficiency <= 1):
            raise InvalidInputError("Efficiency must be between 0 and 1")
        
        # Calculate total heat loss
        total_heat_loss = heat_loss_per_meter * length
        
        # Calculate required electrical power
        required_power = (total_heat_loss * safety_factor) / efficiency
        
        result = {
            "heat_loss_per_meter": heat_loss_per_meter,
            "total_heat_loss_watts": total_heat_loss,
            "required_power_watts": required_power,
            "length_meters": length,
            "safety_factor": safety_factor,
            "efficiency": efficiency,
            "success": True
        }
        
        # Calculate electrical parameters if voltage is provided
        if voltage:
            if voltage <= 0:
                raise InvalidInputError("Voltage must be positive")
            
            current = required_power / voltage
            resistance = voltage / current if current > 0 else float('inf')
            
            result.update({
                "voltage_volts": voltage,
                "current_amperes": current,
                "resistance_ohms": resistance,
                "power_density_watts_per_meter": required_power / length
            })
        
        return result
        
    except Exception as e:
        logger.error(f"Heat tracing power calculation failed: {e}")
        if isinstance(e, InvalidInputError):
            raise
        else:
            raise CalculationError(f"Heat tracing power calculation failed: {str(e)}")


def calculate_cable_power_output(
    cable_power_rating: float,
    length: float,
    derating_factors: Optional[Dict[str, float]] = None
) -> Dict[str, Any]:
    """
    Calculate actual power output of heat tracing cable.

    Args:
        cable_power_rating: Cable power rating in watts/meter
        length: Cable length in meters
        derating_factors: Dictionary of derating factors (optional)

    Returns:
        Dictionary containing cable power output calculations

    Raises:
        InvalidInputError: If invalid parameters provided
        CalculationError: If calculation fails
    """
    logger.debug("Calculating heat tracing cable power output")
    
    try:
        if cable_power_rating <= 0:
            raise InvalidInputError("Cable power rating must be positive")
        
        if length <= 0:
            raise InvalidInputError("Length must be positive")
        
        # Default derating factors
        if derating_factors is None:
            derating_factors = {
                "temperature": 1.0,
                "grouping": 1.0,
                "installation": 1.0
            }
        
        # Validate derating factors
        for factor_name, factor_value in derating_factors.items():
            if not (0 < factor_value <= 1):
                raise InvalidInputError(f"Derating factor '{factor_name}' must be between 0 and 1")
        
        # Calculate overall derating factor
        overall_derating = 1.0
        for factor_value in derating_factors.values():
            overall_derating *= factor_value
        
        # Calculate derated power output
        derated_power_rating = cable_power_rating * overall_derating
        total_power_output = derated_power_rating * length
        
        return {
            "cable_power_rating_watts_per_meter": cable_power_rating,
            "derated_power_rating_watts_per_meter": derated_power_rating,
            "total_power_output_watts": total_power_output,
            "length_meters": length,
            "derating_factors": derating_factors,
            "overall_derating_factor": overall_derating,
            "success": True
        }
        
    except Exception as e:
        logger.error(f"Cable power output calculation failed: {e}")
        if isinstance(e, InvalidInputError):
            raise
        else:
            raise CalculationError(f"Cable power output calculation failed: {str(e)}")


def calculate_circuit_design(
    required_power: float,
    cable_power_rating: float,
    max_circuit_length: float = 200.0,
    voltage: Optional[float] = None
) -> Dict[str, Any]:
    """
    Design heat tracing circuits based on power requirements.

    Args:
        required_power: Total required power in watts
        cable_power_rating: Cable power rating in watts/meter
        max_circuit_length: Maximum circuit length in meters (default: 200)
        voltage: Supply voltage in volts (optional)

    Returns:
        Dictionary containing circuit design calculations

    Raises:
        InvalidInputError: If invalid parameters provided
        CalculationError: If calculation fails
    """
    logger.debug("Designing heat tracing circuits")
    
    try:
        if required_power <= 0:
            raise InvalidInputError("Required power must be positive")
        
        if cable_power_rating <= 0:
            raise InvalidInputError("Cable power rating must be positive")
        
        if max_circuit_length <= 0:
            raise InvalidInputError("Maximum circuit length must be positive")
        
        # Calculate required cable length
        required_length = required_power / cable_power_rating
        
        # Calculate number of circuits needed
        num_circuits = math.ceil(required_length / max_circuit_length)
        
        # Calculate actual circuit parameters
        length_per_circuit = required_length / num_circuits
        power_per_circuit = length_per_circuit * cable_power_rating
        
        result = {
            "required_power_watts": required_power,
            "cable_power_rating_watts_per_meter": cable_power_rating,
            "required_cable_length_meters": required_length,
            "max_circuit_length_meters": max_circuit_length,
            "number_of_circuits": num_circuits,
            "length_per_circuit_meters": length_per_circuit,
            "power_per_circuit_watts": power_per_circuit,
            "total_installed_power_watts": num_circuits * power_per_circuit,
            "success": True
        }
        
        # Calculate electrical parameters if voltage is provided
        if voltage:
            if voltage <= 0:
                raise InvalidInputError("Voltage must be positive")
            
            current_per_circuit = power_per_circuit / voltage
            total_current = num_circuits * current_per_circuit
            
            result.update({
                "voltage_volts": voltage,
                "current_per_circuit_amperes": current_per_circuit,
                "total_current_amperes": total_current
            })
        
        return result
        
    except Exception as e:
        logger.error(f"Circuit design calculation failed: {e}")
        if isinstance(e, InvalidInputError):
            raise
        else:
            raise CalculationError(f"Circuit design calculation failed: {str(e)}")


def calculate_startup_power(
    steady_state_power: float,
    startup_factor: float = 1.5,
    startup_duration_hours: float = 2.0
) -> Dict[str, Any]:
    """
    Calculate startup power requirements for heat tracing systems.

    Args:
        steady_state_power: Steady state power in watts
        startup_factor: Startup power factor (default: 1.5)
        startup_duration_hours: Startup duration in hours (default: 2.0)

    Returns:
        Dictionary containing startup power calculations

    Raises:
        InvalidInputError: If invalid parameters provided
        CalculationError: If calculation fails
    """
    logger.debug("Calculating startup power requirements")
    
    try:
        if steady_state_power <= 0:
            raise InvalidInputError("Steady state power must be positive")
        
        if startup_factor <= 0:
            raise InvalidInputError("Startup factor must be positive")
        
        if startup_duration_hours <= 0:
            raise InvalidInputError("Startup duration must be positive")
        
        # Calculate startup power
        startup_power = steady_state_power * startup_factor
        
        # Calculate energy consumption
        startup_energy = startup_power * startup_duration_hours
        daily_energy = steady_state_power * 24  # Assuming 24-hour operation
        
        return {
            "steady_state_power_watts": steady_state_power,
            "startup_power_watts": startup_power,
            "startup_factor": startup_factor,
            "startup_duration_hours": startup_duration_hours,
            "startup_energy_wh": startup_energy,
            "daily_energy_wh": daily_energy,
            "peak_power_watts": startup_power,
            "success": True
        }
        
    except Exception as e:
        logger.error(f"Startup power calculation failed: {e}")
        if isinstance(e, InvalidInputError):
            raise
        else:
            raise CalculationError(f"Startup power calculation failed: {str(e)}")
