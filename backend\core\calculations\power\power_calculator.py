# backend/core/calculations/power/power_calculator.py
"""
Power Calculator Class.

This module provides a class-based interface for power calculations
in electrical and heat tracing systems.
"""

import logging
import math
from typing import Dict, Any, Optional, List
from dataclasses import dataclass

from core.errors.exceptions import CalculationError, InvalidInputError

logger = logging.getLogger(__name__)


@dataclass
class PowerInput:
    """Input parameters for power calculations."""
    voltage: Optional[float] = None
    current: Optional[float] = None
    resistance: Optional[float] = None
    power_factor: Optional[float] = None
    efficiency: Optional[float] = None
    load_type: Optional[str] = None


@dataclass
class PowerResult:
    """Result of power calculations."""
    power_watts: float
    voltage: float
    current: float
    power_factor: float
    apparent_power: float
    reactive_power: float
    calculation_type: str
    input_parameters: Dict[str, Any]


class PowerCalculator:
    """
    Calculator for electrical power calculations.
    
    This class provides methods for calculating power, voltage, current,
    and related electrical parameters for various load types.
    """

    def __init__(self):
        """Initialize the power calculator."""
        self.default_power_factor = 0.85
        self.default_efficiency = 0.90
        self.supported_load_types = ['resistive', 'inductive', 'capacitive', 'mixed']
        logger.debug("PowerCalculator initialized")

    def calculate_power(self, circuit_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Calculate electrical power for a circuit.

        Args:
            circuit_data: Dictionary containing circuit parameters

        Returns:
            Dictionary containing power calculation results

        Raises:
            InvalidInputError: If required parameters are missing
            CalculationError: If calculation fails
        """
        logger.info("Starting power calculation")
        
        try:
            # Validate input data
            self._validate_input(circuit_data)
            
            # Extract parameters
            voltage = circuit_data.get('voltage')
            current = circuit_data.get('current')
            resistance = circuit_data.get('resistance')
            power = circuit_data.get('power')
            power_factor = circuit_data.get('power_factor', self.default_power_factor)
            
            # Calculate based on available parameters
            if power and voltage:
                # Calculate current from power and voltage
                current = power / (voltage * power_factor)
                resistance = voltage / current if current > 0 else None
            elif voltage and current:
                # Calculate power from voltage and current
                power = voltage * current * power_factor
                resistance = voltage / current if current > 0 else None
            elif voltage and resistance:
                # Calculate power and current from voltage and resistance
                current = voltage / resistance
                power = voltage * current * power_factor
            elif current and resistance:
                # Calculate power and voltage from current and resistance
                voltage = current * resistance
                power = voltage * current * power_factor
            else:
                raise InvalidInputError(
                    "Insufficient parameters. Need at least two of: voltage, current, resistance, power"
                )
            
            # Calculate additional parameters
            apparent_power = voltage * current if voltage and current else 0
            reactive_power = math.sqrt(max(0, apparent_power**2 - power**2)) if apparent_power > power else 0
            
            return {
                "power_watts": power,
                "voltage": voltage,
                "current": current,
                "resistance": resistance,
                "power_factor": power_factor,
                "apparent_power": apparent_power,
                "reactive_power": reactive_power,
                "calculation_type": "electrical_power",
                "input_parameters": circuit_data,
                "success": True,
                "warnings": []
            }

        except Exception as e:
            logger.error(f"Power calculation failed: {e}")
            if isinstance(e, (InvalidInputError, CalculationError)):
                raise
            else:
                raise CalculationError(f"Power calculation failed: {str(e)}")

    def calculate_three_phase_power(self, circuit_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Calculate three-phase electrical power.

        Args:
            circuit_data: Dictionary containing three-phase circuit parameters

        Returns:
            Dictionary containing three-phase power calculation results
        """
        logger.debug("Calculating three-phase power")
        
        try:
            voltage = circuit_data.get('line_voltage')
            current = circuit_data.get('line_current')
            power_factor = circuit_data.get('power_factor', self.default_power_factor)
            
            if not voltage or not current:
                raise InvalidInputError("Three-phase calculation requires line_voltage and line_current")
            
            # Three-phase power calculation
            power = math.sqrt(3) * voltage * current * power_factor
            apparent_power = math.sqrt(3) * voltage * current
            reactive_power = math.sqrt(max(0, apparent_power**2 - power**2))
            
            return {
                "power_watts": power,
                "line_voltage": voltage,
                "line_current": current,
                "power_factor": power_factor,
                "apparent_power": apparent_power,
                "reactive_power": reactive_power,
                "calculation_type": "three_phase_power",
                "input_parameters": circuit_data,
                "success": True,
                "warnings": []
            }

        except Exception as e:
            logger.error(f"Three-phase power calculation failed: {e}")
            raise CalculationError(f"Three-phase power calculation failed: {str(e)}")

    def calculate_heat_tracing_power(self, circuit_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Calculate power requirements for heat tracing.

        Args:
            circuit_data: Dictionary containing heat tracing parameters

        Returns:
            Dictionary containing heat tracing power calculation results
        """
        logger.debug("Calculating heat tracing power")
        
        try:
            heat_loss = circuit_data.get('heat_loss_per_meter', 0)
            length = circuit_data.get('length', 0)
            safety_factor = circuit_data.get('safety_factor', 1.3)
            efficiency = circuit_data.get('efficiency', self.default_efficiency)
            
            if heat_loss <= 0 or length <= 0:
                raise InvalidInputError("Heat tracing calculation requires positive heat_loss_per_meter and length")
            
            # Calculate required power
            total_heat_loss = heat_loss * length
            required_power = (total_heat_loss * safety_factor) / efficiency
            
            # Calculate electrical parameters if voltage is provided
            voltage = circuit_data.get('voltage')
            if voltage:
                current = required_power / voltage
                resistance = voltage / current if current > 0 else None
            else:
                current = None
                resistance = None
            
            return {
                "power_watts": required_power,
                "heat_loss_total": total_heat_loss,
                "voltage": voltage,
                "current": current,
                "resistance": resistance,
                "safety_factor": safety_factor,
                "efficiency": efficiency,
                "calculation_type": "heat_tracing_power",
                "input_parameters": circuit_data,
                "success": True,
                "warnings": []
            }

        except Exception as e:
            logger.error(f"Heat tracing power calculation failed: {e}")
            raise CalculationError(f"Heat tracing power calculation failed: {str(e)}")

    def _validate_input(self, circuit_data: Dict[str, Any]) -> None:
        """Validate input parameters."""
        if not isinstance(circuit_data, dict):
            raise InvalidInputError("Circuit data must be a dictionary")
        
        # Validate numeric parameters
        numeric_params = [
            'voltage', 'current', 'resistance', 'power', 'power_factor',
            'efficiency', 'heat_loss_per_meter', 'length', 'safety_factor'
        ]
        
        for param in numeric_params:
            if param in circuit_data:
                value = circuit_data[param]
                if not isinstance(value, (int, float)):
                    raise InvalidInputError(f"Parameter '{param}' must be a number")
                
                # Check for reasonable ranges
                if param in ['power_factor', 'efficiency'] and not (0 < value <= 1):
                    raise InvalidInputError(f"Parameter '{param}' must be between 0 and 1")
                
                if param in ['voltage', 'current', 'resistance', 'power'] and value < 0:
                    raise InvalidInputError(f"Parameter '{param}' must be positive")

    def calculate_load_analysis(self, loads: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Perform load analysis for multiple loads.

        Args:
            loads: List of load data dictionaries

        Returns:
            Dictionary containing load analysis results
        """
        logger.info(f"Performing load analysis for {len(loads)} loads")
        
        total_power = 0
        total_current = 0
        results = []
        errors = []
        
        for i, load in enumerate(loads):
            try:
                result = self.calculate_power(load)
                results.append(result)
                total_power += result.get('power_watts', 0)
                total_current += result.get('current', 0)
            except Exception as e:
                error_info = {
                    'load_index': i,
                    'error': str(e),
                    'load_data': load
                }
                errors.append(error_info)
                logger.warning(f"Load {i} calculation failed: {e}")
        
        return {
            'individual_results': results,
            'errors': errors,
            'total_power_watts': total_power,
            'total_current': total_current,
            'total_loads': len(loads),
            'successful_calculations': len(results),
            'failed_calculations': len(errors),
            'load_diversity_factor': 0.8,  # Typical value
            'demand_power_watts': total_power * 0.8
        }
