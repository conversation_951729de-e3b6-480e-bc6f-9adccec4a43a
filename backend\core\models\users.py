from typing import TYPE_CHECKING
from sqlalchemy import <PERSON><PERSON><PERSON>, Foreign<PERSON>ey, UniqueConstraint
from sqlalchemy.orm import Mapped, mapped_column, relationship

# from .activity_log import ActivityLog
from .base import (  # SoftDeleteColumns is used by UserPreference
    Base,
    CommonColumns,
    SoftDeleteColumns,
)
from core.utils.json_validation import FlexibleJSON

if TYPE_CHECKING:
    from .documents import ExportedDocument, ImportedDataRevision


class User(
    CommonColumns, Base
):  # User itself does not need soft delete, as it's the actor
    __tablename__ = "User"

    email: Mapped[str | None] = mapped_column(unique=True, nullable=True)
    password_hash: Mapped[str | None] = mapped_column(
        nullable=True
    )  # Store hashed passwords, not plain text
    is_active: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False)

    # Relationships
    preferences: Mapped["UserPreference | None"] = relationship(
        "UserPreference",
        back_populates="user",
        cascade="all, delete-orphan",
        uselist=False,
        foreign_keys="[UserPreference.user_id]",
    )
    # TODO: Uncomment when ActivityLog model is implemented
    # activity_logs: Mapped[list["ActivityLog"]] = relationship(
    #     "ActivityLog", back_populates="user"
    # )
    imported_data_revisions: Mapped[list["ImportedDataRevision"]] = relationship(
        "ImportedDataRevision",
        back_populates="imported_by_user",
        foreign_keys="ImportedDataRevision.imported_by_user_id",
    )
    exported_documents: Mapped[list["ExportedDocument"]] = relationship(
        "ExportedDocument",
        back_populates="generated_by_user",
        foreign_keys="ExportedDocument.generated_by_user_id",
    )

    __table_args__ = (UniqueConstraint("name", name="uq_user_name"),)

    def __repr__(self):
        return f"<User(id={self.id}, name='{self.name}', email='{self.email}')>"


class UserPreference(CommonColumns, SoftDeleteColumns, Base):
    __tablename__ = "UserPreference"

    user_id: Mapped[int] = mapped_column(
        ForeignKey("User.id"), nullable=False, unique=True
    )

    ui_theme: Mapped[str] = mapped_column(default="light", nullable=False)

    default_min_ambient_temp_c: Mapped[float | None] = mapped_column(nullable=True)
    default_max_ambient_temp_c: Mapped[float | None] = mapped_column(nullable=True)
    default_desired_maintenance_temp_c: Mapped[float | None] = mapped_column(
        nullable=True
    )
    default_safety_margin_percent: Mapped[float] = mapped_column(
        default=0.0, nullable=False
    )

    preferred_cable_manufacturers_json: Mapped[str | None] = mapped_column(
        FlexibleJSON, nullable=True
    )
    preferred_control_device_manufacturers_json: Mapped[str | None] = mapped_column(
        FlexibleJSON, nullable=True
    )

    # Relationships
    user: Mapped["User"] = relationship(
        back_populates="preferences", foreign_keys=[user_id]
    )

    def __repr__(self):
        return f"<UserPreference(id={self.id}, user_id={self.user_id}, ui_theme='{self.ui_theme}')>"
