# backend/core/repositories/base_repository.py
from typing import Any, Dict, Generic, List, Optional, Type, TypeVar

from sqlalchemy import select
from sqlalchemy.exc import IntegrityError, NoResultFound, SQLAlchemyError
from sqlalchemy.orm import Session

# Import pagination utilities for enhanced repository functionality
from core.utils.pagination_utils import (
    PaginationParams,
    PaginationResult,
    SortParams,
    paginate_query,
)
from core.utils.query_utils import QueryBuilder

try:
    from core.errors.exceptions import (
        DatabaseError,
        DuplicateEntryError,
        NotFoundError,
    )
except ImportError:
    from core.errors.exceptions import (
        DatabaseError,
        DuplicateEntryError,
        NotFoundError,
    )
try:
    from core.models.base import Base
except ImportError:
    from core.models.base import Base

# Define a generic type for SQLAlchemy models
ModelType = TypeVar("ModelType", bound=Base)  # type: ignore


class BaseRepository(Generic[ModelType]):
    def __init__(self, db_session: Session, model: Type[ModelType]):
        self.db_session = db_session
        self.model = model

    def _handle_db_exception(
        self, e: SQLAlchemyError, entity_name: str = "resource"
    ) -> None:
        if isinstance(e, IntegrityError):
            raise DuplicateEntryError(
                message=f"A {entity_name} with the given unique constraint already exists.",
                original_exception=e,
            )
        elif isinstance(e, NoResultFound):
            raise NotFoundError(
                code=f"{entity_name.upper()}_NOT_FOUND",
                detail=f"{entity_name.capitalize()} not found.",
            )
        else:
            raise DatabaseError(
                reason=f"An unexpected database error occurred during {entity_name} operation.",
                original_exception=e,
            ) from e

    def get_by_id(self, item_id: int) -> Optional[ModelType]:
        try:
            stmt = select(self.model).where(self.model.id == item_id)
            return self.db_session.scalar(stmt)
        except SQLAlchemyError as e:
            self._handle_db_exception(e, self.model.__tablename__)
            return None  # This line will never be reached due to exception, but satisfies type checker

    def get_all(self, skip: int = 0, limit: int = 100) -> List[ModelType]:
        try:
            stmt = select(self.model).offset(skip).limit(limit)
            return list(self.db_session.scalars(stmt).all())
        except SQLAlchemyError as e:
            self._handle_db_exception(e, self.model.__tablename__)
            return []  # This line will never be reached due to exception, but satisfies type checker

    def create(self, data: Dict[str, Any]) -> ModelType:
        try:
            item = self.model(**data)
            self.db_session.add(item)
            # Note: db_session.commit() is typically done by the service layer,
            # but for simplicity, we might do it here if it's a single operation
            # self.db_session.commit()
            # self.db_session.refresh(item)
            return item
        except SQLAlchemyError as e:
            self._handle_db_exception(e, self.model.__tablename__)
            raise  # This line will never be reached due to exception, but satisfies type checker

    def update(self, item_id: int, data: Dict[str, Any]) -> Optional[ModelType]:
        """
        Update an existing item by ID.

        Args:
            item_id: ID of the item to update
            data: Dictionary of fields to update

        Returns:
            Optional[ModelType]: Updated item or None if not found

        Raises:
            DatabaseError: If database operation fails
        """
        try:
            # Get the existing item
            item = self.get_by_id(item_id)
            if not item:
                return None

            # Update the item attributes
            for key, value in data.items():
                if hasattr(item, key):
                    setattr(item, key, value)

            return item
        except SQLAlchemyError as e:
            self._handle_db_exception(e, self.model.__tablename__)
            raise

    def delete(self, item_id: int) -> bool:
        """
        Delete an item by ID.

        Args:
            item_id: ID of the item to delete

        Returns:
            bool: True if item was deleted, False if not found

        Raises:
            DatabaseError: If database operation fails
        """
        try:
            item = self.get_by_id(item_id)
            if not item:
                return False

            self.db_session.delete(item)
            return True
        except SQLAlchemyError as e:
            self._handle_db_exception(e, self.model.__tablename__)
            raise

    def get_paginated(
        self,
        pagination_params: PaginationParams,
        sort_params: Optional[SortParams] = None,
        filters: Optional[Dict[str, Any]] = None,
    ) -> PaginationResult:
        """
        Get paginated results with optional filters and sorting.

        Args:
            pagination_params: Pagination parameters (page, per_page)
            sort_params: Sort parameters (optional)
            filters: Dictionary of filters to apply (optional)

        Returns:
            PaginationResult: Paginated results with metadata

        Raises:
            DatabaseError: If database operation fails
        """
        try:
            # Start with base query
            query = select(self.model)

            # Apply filters if provided
            if filters:
                builder = QueryBuilder(self.db_session, self.model)
                for field, value in filters.items():
                    if hasattr(self.model, field) and value is not None:
                        builder.filter_by_field(field, value)
                query = builder.build()

            # Apply pagination and sorting
            return paginate_query(
                self.db_session, query, self.model, pagination_params, sort_params
            )

        except SQLAlchemyError as e:
            self._handle_db_exception(e, self.model.__tablename__)
            raise

    def search_paginated(
        self,
        search_term: str,
        searchable_fields: List[str],
        pagination_params: PaginationParams,
        sort_params: Optional[SortParams] = None,
        additional_filters: Optional[Dict[str, Any]] = None,
    ) -> PaginationResult:
        """
        Search entities with pagination.

        Args:
            search_term: Term to search for
            searchable_fields: List of fields to search in
            pagination_params: Pagination parameters
            sort_params: Sort parameters (optional)
            additional_filters: Additional filters to apply (optional)

        Returns:
            PaginationResult: Paginated search results

        Raises:
            DatabaseError: If database operation fails
        """
        try:
            # Build search query using QueryBuilder
            builder = QueryBuilder(self.db_session, self.model)

            # Add search conditions
            if search_term and searchable_fields:
                search_conditions = []
                for field in searchable_fields:
                    if hasattr(self.model, field):
                        field_attr = getattr(self.model, field)
                        search_conditions.append(field_attr.ilike(f"%{search_term}%"))

                if search_conditions:
                    from sqlalchemy import or_

                    builder.query = builder.query.where(or_(*search_conditions))

            # Apply additional filters
            if additional_filters:
                for field, value in additional_filters.items():
                    if hasattr(self.model, field) and value is not None:
                        builder.filter_by_field(field, value)

            query = builder.build()

            # Apply pagination and sorting
            return paginate_query(
                self.db_session, query, self.model, pagination_params, sort_params
            )

        except SQLAlchemyError as e:
            self._handle_db_exception(e, self.model.__tablename__)
            raise

    # ... other common CRUD methods like filter, etc.
