# backend/core/repositories/dependencies.py
"""
Repository Dependencies

This module provides repository dependency injection providers for FastAPI.
Each repository receives a database session and is properly instantiated
for use in the service layer.
"""

from fastapi import Depends
from sqlalchemy.orm import Session

from core.database.dependencies import get_db


def get_project_repository(db: Session = Depends(get_db)):
    """
    Dependency provider for ProjectRepository.
    
    Args:
        db: Database session from dependency injection
        
    Returns:
        ProjectRepository: Configured repository instance
    """
    from core.repositories.project_repository import ProjectRepository
    return ProjectRepository(db)


def get_user_repository(db: Session = Depends(get_db)):
    """
    Dependency provider for UserRepository.
    
    Args:
        db: Database session from dependency injection
        
    Returns:
        UserRepository: Configured repository instance
    """
    from core.repositories.user_repository import UserRepository
    return UserRepository(db)


def get_component_repository(db: Session = Depends(get_db)):
    """
    Dependency provider for ComponentRepository.
    
    Args:
        db: Database session from dependency injection
        
    Returns:
        ComponentRepository: Configured repository instance
    """
    from core.repositories.component_repository import ComponentRepository
    return ComponentRepository(db)


def get_component_category_repository(db: Session = Depends(get_db)):
    """
    Dependency provider for ComponentCategoryRepository.
    
    Args:
        db: Database session from dependency injection
        
    Returns:
        ComponentCategoryRepository: Configured repository instance
    """
    from core.repositories.component_category_repository import ComponentCategoryRepository
    return ComponentCategoryRepository(db)


def get_heat_tracing_repository(db: Session = Depends(get_db)):
    """
    Dependency provider for HeatTracingRepository.
    
    Args:
        db: Database session from dependency injection
        
    Returns:
        HeatTracingRepository: Configured repository instance
    """
    from core.repositories.heat_tracing_repository import HeatTracingRepository
    return HeatTracingRepository(db)


def get_imported_data_revision_repository(db: Session = Depends(get_db)):
    """
    Dependency provider for ImportedDataRevisionRepository.
    
    Args:
        db: Database session from dependency injection
        
    Returns:
        ImportedDataRevisionRepository: Configured repository instance
    """
    from core.repositories.imported_data_revision_repository import ImportedDataRevisionRepository
    return ImportedDataRevisionRepository(db)


def get_exported_document_repository(db: Session = Depends(get_db)):
    """
    Dependency provider for ExportedDocumentRepository.
    
    Args:
        db: Database session from dependency injection
        
    Returns:
        ExportedDocumentRepository: Configured repository instance
    """
    from core.repositories.exported_document_repository import ExportedDocumentRepository
    return ExportedDocumentRepository(db)


def get_calculation_standard_repository(db: Session = Depends(get_db)):
    """
    Dependency provider for CalculationStandardRepository.
    
    Args:
        db: Database session from dependency injection
        
    Returns:
        CalculationStandardRepository: Configured repository instance
    """
    from core.repositories.calculation_standard_repository import CalculationStandardRepository
    return CalculationStandardRepository(db)
