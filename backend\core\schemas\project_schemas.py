# backend/core/schemas/project_schemas.py
"""
Pydantic schemas for Project entity validation, serialization, and deserialization.

This module defines the data contracts for Project-related operations including:
- ProjectCreateSchema: For creating new projects
- ProjectUpdateSchema: For updating existing projects
- ProjectReadSchema: For reading/displaying project data
- ProjectListSchema: For listing projects with pagination
"""

from datetime import datetime
from typing import List, Optional, Any
from pydantic import BaseModel, Field, field_validator, field_serializer, ConfigDict
from core.models.enums import InstallationEnvironment

# Import JSON validation utilities for enhanced validation
from core.utils import validate_json_data, JSONValidationError


class AvailableVoltagesSchema(BaseModel):
    """Schema for validating available voltages JSON data."""

    voltages: List[float] = Field(
        ..., description="List of available voltages", min_length=1
    )

    @field_validator("voltages")
    @classmethod
    def validate_voltages(cls, v: List[float]) -> List[float]:
        """Validate that all voltages are positive numbers."""
        for voltage in v:
            if voltage <= 0:
                raise ValueError("All voltages must be positive numbers")
        return v


class ProjectBaseSchema(BaseModel):
    """Base schema with common Project fields."""

    name: str = Field(
        ..., min_length=3, max_length=100, description="Project name (must be unique)"
    )
    project_number: str = Field(
        ...,
        min_length=3,
        max_length=50,
        description="Project number/code (must be unique)",
    )
    description: Optional[str] = Field(
        None, max_length=2000, description="Project description"
    )
    designer: Optional[str] = Field(
        None, max_length=100, description="Project designer name"
    )
    notes: Optional[str] = Field(
        None, max_length=1000, description="Additional project notes"
    )

    @field_validator("name")
    @classmethod
    def validate_name(cls, v: str) -> str:
        """Validate project name."""
        if not v or not v.strip():
            raise ValueError("Project name cannot be empty or whitespace only")
        return v.strip()

    @field_validator("project_number")
    @classmethod
    def validate_project_number(cls, v: str) -> str:
        """Validate project number format."""
        if not v or not v.strip():
            raise ValueError("Project number cannot be empty or whitespace only")
        # Allow alphanumeric, hyphens, underscores, and dots
        import re

        if not re.match(r"^[A-Za-z0-9._-]+$", v.strip()):
            raise ValueError(
                "Project number can only contain letters, numbers, dots, hyphens, and underscores"
            )
        return v.strip().upper()  # Normalize to uppercase


class ProjectEnvironmentalSchema(BaseModel):
    """Schema for environmental parameters."""

    min_ambient_temp_c: float = Field(
        ..., ge=-50, le=50, description="Minimum ambient temperature in Celsius"
    )
    max_ambient_temp_c: float = Field(
        ..., ge=-50, le=80, description="Maximum ambient temperature in Celsius"
    )
    desired_maintenance_temp_c: float = Field(
        ..., ge=0, le=300, description="Desired maintenance temperature in Celsius"
    )
    wind_speed_ms: Optional[float] = Field(
        None, ge=0, le=50, description="Wind speed in meters per second"
    )
    installation_environment: Optional[InstallationEnvironment] = Field(
        None, description="Installation environment (indoor/outdoor)"
    )

    @field_validator("max_ambient_temp_c")
    @classmethod
    def validate_temp_range(cls, v: float, info) -> float:
        """Validate that max temp is greater than min temp."""
        if "min_ambient_temp_c" in info.data:
            min_temp = info.data["min_ambient_temp_c"]
            if v <= min_temp:
                raise ValueError(
                    "Maximum ambient temperature must be greater than minimum ambient temperature"
                )
        return v


class ProjectDefaultsSchema(BaseModel):
    """Schema for project defaults."""

    available_voltages_json: Optional[str] = Field(
        None,
        max_length=500,
        description="Available voltages as JSON string (e.g., '[120, 240, 480]')",
    )
    default_cable_manufacturer: Optional[str] = Field(
        None, max_length=100, description="Default cable manufacturer"
    )
    default_control_device_manufacturer: Optional[str] = Field(
        None, max_length=100, description="Default control device manufacturer"
    )

    @field_validator("available_voltages_json")
    @classmethod
    def validate_voltages_json(cls, v: Optional[str]) -> Optional[str]:
        """Validate that voltages JSON is valid using enhanced utilities."""
        if v is None:
            return v

        try:
            # Parse JSON and validate structure
            import json

            voltages_data = json.loads(v)

            # Create data in expected format for schema validation
            if isinstance(voltages_data, list):
                schema_data = {"voltages": voltages_data}
            else:
                raise ValueError("Voltages must be a JSON array")

            # Use our JSON validation utility with the schema
            validated = validate_json_data(schema_data, AvailableVoltagesSchema)

            # Return the original JSON string if validation passes
            return v

        except json.JSONDecodeError:
            raise ValueError("Invalid JSON format for available voltages")
        except JSONValidationError as e:
            # Extract specific error messages from validation_errors
            if e.validation_errors and "voltages" in e.validation_errors:
                raise ValueError(e.validation_errors["voltages"])
            else:
                raise ValueError(f"Voltage validation failed: {e.message}")
        except Exception as e:
            raise ValueError(f"Voltage validation error: {str(e)}")


class ProjectCreateSchema(
    ProjectBaseSchema, ProjectEnvironmentalSchema, ProjectDefaultsSchema
):
    """Schema for creating a new project."""

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "name": "Heat Tracing Project Alpha",
                "project_number": "HT-2024-001",
                "description": "Main process piping heat tracing design",
                "designer": "John Smith",
                "notes": "Initial design phase",
                "min_ambient_temp_c": -20.0,
                "max_ambient_temp_c": 40.0,
                "desired_maintenance_temp_c": 65.0,
                "wind_speed_ms": 5.0,
                "installation_environment": "outdoor",
                "available_voltages_json": "[120, 240, 480]",
                "default_cable_manufacturer": "Chromalox",
                "default_control_device_manufacturer": "Honeywell",
            }
        }
    )


class ProjectUpdateSchema(BaseModel):
    """Schema for updating an existing project."""

    name: Optional[str] = Field(
        None, min_length=3, max_length=100, description="Project name (must be unique)"
    )
    project_number: Optional[str] = Field(
        None,
        min_length=3,
        max_length=50,
        description="Project number/code (must be unique)",
    )
    description: Optional[str] = Field(
        None, max_length=2000, description="Project description"
    )
    designer: Optional[str] = Field(
        None, max_length=100, description="Project designer name"
    )
    notes: Optional[str] = Field(
        None, max_length=1000, description="Additional project notes"
    )
    min_ambient_temp_c: Optional[float] = Field(
        None, ge=-50, le=50, description="Minimum ambient temperature in Celsius"
    )
    max_ambient_temp_c: Optional[float] = Field(
        None, ge=-50, le=80, description="Maximum ambient temperature in Celsius"
    )
    desired_maintenance_temp_c: Optional[float] = Field(
        None, ge=0, le=300, description="Desired maintenance temperature in Celsius"
    )
    wind_speed_ms: Optional[float] = Field(
        None, ge=0, le=50, description="Wind speed in meters per second"
    )
    installation_environment: Optional[InstallationEnvironment] = Field(
        None, description="Installation environment (indoor/outdoor)"
    )
    available_voltages_json: Optional[str] = Field(
        None, max_length=500, description="Available voltages as JSON string"
    )
    default_cable_manufacturer: Optional[str] = Field(
        None, max_length=100, description="Default cable manufacturer"
    )
    default_control_device_manufacturer: Optional[str] = Field(
        None, max_length=100, description="Default control device manufacturer"
    )

    # Apply the same validators as create schema
    _validate_name = field_validator("name")(ProjectBaseSchema.validate_name)
    _validate_project_number = field_validator("project_number")(
        ProjectBaseSchema.validate_project_number
    )
    _validate_voltages_json = field_validator("available_voltages_json")(
        ProjectDefaultsSchema.validate_voltages_json
    )

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "name": "Updated Heat Tracing Project Alpha",
                "description": "Updated main process piping heat tracing design",
                "designer": "Jane Doe",
                "max_ambient_temp_c": 45.0,
                "default_cable_manufacturer": "Raychem",
            }
        }
    )


class ProjectReadSchema(
    ProjectBaseSchema, ProjectEnvironmentalSchema, ProjectDefaultsSchema
):
    """Schema for reading/displaying project data."""

    id: int = Field(..., description="Unique project identifier")
    created_at: datetime = Field(..., description="Project creation timestamp")
    updated_at: datetime = Field(..., description="Project last update timestamp")
    is_deleted: bool = Field(..., description="Soft delete flag")
    deleted_at: Optional[datetime] = Field(None, description="Deletion timestamp")
    deleted_by_user_id: Optional[int] = Field(
        None, description="ID of user who deleted the project"
    )

    @field_serializer("available_voltages_json")
    def serialize_voltages_json(self, value: Any) -> Optional[str]:
        """Serialize VoltagesSchema object to JSON string for API responses."""
        if value is None:
            return None

        # If it's already a string, return as-is
        if isinstance(value, str):
            return value

        # If it's a VoltagesSchema object, convert to JSON string
        if hasattr(value, "voltages"):
            import json

            return json.dumps(value.voltages)

        # If it's a dict with voltages key, convert to JSON string
        if isinstance(value, dict) and "voltages" in value:
            import json

            return json.dumps(value["voltages"])

        # Fallback: convert to string
        return str(value)

    model_config = ConfigDict(
        from_attributes=True,  # Enable ORM mode for SQLAlchemy model conversion
        json_schema_extra={
            "example": {
                "id": 1,
                "name": "Heat Tracing Project Alpha",
                "project_number": "HT-2024-001",
                "description": "Main process piping heat tracing design",
                "designer": "John Smith",
                "notes": "Initial design phase",
                "min_ambient_temp_c": -20.0,
                "max_ambient_temp_c": 40.0,
                "desired_maintenance_temp_c": 65.0,
                "wind_speed_ms": 5.0,
                "installation_environment": "outdoor",
                "available_voltages_json": "[120, 240, 480]",
                "default_cable_manufacturer": "Chromalox",
                "default_control_device_manufacturer": "Honeywell",
                "created_at": "2024-01-15T10:30:00Z",
                "updated_at": "2024-01-15T10:30:00Z",
                "is_deleted": False,
                "deleted_at": None,
                "deleted_by_user_id": None,
            }
        },
    )


class ProjectSummarySchema(BaseModel):
    """Schema for project summary in lists."""

    id: int = Field(..., description="Unique project identifier")
    name: str = Field(..., description="Project name")
    project_number: str = Field(..., description="Project number/code")
    description: Optional[str] = Field(None, description="Project description")
    designer: Optional[str] = Field(None, description="Project designer name")
    created_at: datetime = Field(..., description="Project creation timestamp")
    updated_at: datetime = Field(..., description="Project last update timestamp")

    model_config = ConfigDict(from_attributes=True)


class ProjectListResponseSchema(BaseModel):
    """Schema for paginated project list response."""

    projects: List[ProjectSummarySchema] = Field(..., description="List of projects")
    total: int = Field(..., description="Total number of projects")
    page: int = Field(..., description="Current page number")
    per_page: int = Field(..., description="Number of projects per page")
    total_pages: int = Field(..., description="Total number of pages")

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "projects": [
                    {
                        "id": 1,
                        "name": "Heat Tracing Project Alpha",
                        "project_number": "HT-2024-001",
                        "description": "Main process piping heat tracing design",
                        "designer": "John Smith",
                        "created_at": "2024-01-15T10:30:00Z",
                        "updated_at": "2024-01-15T10:30:00Z",
                    }
                ],
                "total": 25,
                "page": 1,
                "per_page": 10,
                "total_pages": 3,
            }
        }
    )
