# backend/core/services/dependencies.py
"""
Service Dependencies

This module provides service dependency injection providers for FastAPI.
Services receive their required repositories through dependency injection,
following the layered architecture pattern.
"""

from fastapi import Depends

from core.repositories.dependencies import (
    get_project_repository,
    get_user_repository,
    get_component_repository,
    get_component_category_repository,
    get_heat_tracing_repository,
    get_imported_data_revision_repository,
    get_exported_document_repository,
    get_calculation_standard_repository,
)


def get_project_service(
    project_repo=Depends(get_project_repository)
):
    """
    Dependency provider for ProjectService.
    
    Args:
        project_repo: ProjectRepository from dependency injection
        
    Returns:
        ProjectService: Configured service instance
    """
    from core.services.project_service import ProjectService
    return ProjectService(project_repo)


def get_user_service(
    user_repo=Depends(get_user_repository)
):
    """
    Dependency provider for UserService.
    
    Args:
        user_repo: UserRepository from dependency injection
        
    Returns:
        UserService: Configured service instance
    """
    from core.services.user_service import UserService
    return UserService(user_repo)


def get_component_service(
    component_repo=Depends(get_component_repository),
    category_repo=Depends(get_component_category_repository)
):
    """
    Dependency provider for ComponentService.
    
    Args:
        component_repo: ComponentRepository from dependency injection
        category_repo: ComponentCategoryRepository from dependency injection
        
    Returns:
        ComponentService: Configured service instance
    """
    from core.services.component_service import ComponentService
    return ComponentService(component_repo, category_repo)


def get_component_category_service(
    category_repo=Depends(get_component_category_repository)
):
    """
    Dependency provider for ComponentCategoryService.
    
    Args:
        category_repo: ComponentCategoryRepository from dependency injection
        
    Returns:
        ComponentCategoryService: Configured service instance
    """
    from core.services.component_category_service import ComponentCategoryService
    return ComponentCategoryService(category_repo)


def get_heat_tracing_service(
    heat_tracing_repo=Depends(get_heat_tracing_repository)
):
    """
    Dependency provider for HeatTracingService.
    
    Args:
        heat_tracing_repo: HeatTracingRepository from dependency injection
        
    Returns:
        HeatTracingService: Configured service instance
    """
    from core.services.heat_tracing_service import HeatTracingService
    return HeatTracingService(heat_tracing_repo)


def get_document_service(
    imported_data_revision_repo=Depends(get_imported_data_revision_repository),
    exported_document_repo=Depends(get_exported_document_repository),
    calculation_standard_repo=Depends(get_calculation_standard_repository),
    project_repo=Depends(get_project_repository),
    user_repo=Depends(get_user_repository)
):
    """
    Dependency provider for DocumentService.
    
    Args:
        imported_data_revision_repo: ImportedDataRevisionRepository from dependency injection
        exported_document_repo: ExportedDocumentRepository from dependency injection
        calculation_standard_repo: CalculationStandardRepository from dependency injection
        project_repo: ProjectRepository from dependency injection
        user_repo: UserRepository from dependency injection
        
    Returns:
        DocumentService: Configured service instance
    """
    from core.services.document_service import DocumentService
    return DocumentService(
        imported_data_revision_repository=imported_data_revision_repo,
        exported_document_repository=exported_document_repo,
        calculation_standard_repository=calculation_standard_repo,
        project_repository=project_repo,
        user_repository=user_repo,
    )
