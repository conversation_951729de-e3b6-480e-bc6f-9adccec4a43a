# How to Upgrade Database - Ultimate Electrical Designer Backend

This guide provides comprehensive instructions for upgrading the database using Alembic migrations in the Ultimate Electrical Designer backend.

## Overview

The Ultimate Electrical Designer backend uses Alembic for database schema migrations. The system supports automatic fallback from SQL Server to SQLite for development environments, ensuring robust database connectivity.

## Prerequisites

- Python environment with all dependencies installed
- Access to the backend directory
- Proper database configuration in settings

## Quick Start

### 1. Check Current Migration Status

```bash
cd backend
python -m alembic current
```

This shows the current migration revision applied to your database.

### 2. View Available Migrations

```bash
python -m alembic history
```

This displays all available migrations and their status.

### 3. Upgrade Database

```bash
python -m alembic upgrade head
```

This applies all pending migrations to bring your database to the latest schema.

## Detailed Migration Procedures

### Using Alembic Commands (Recommended)

#### Check Migration Status
```bash
# Show current migration version
python -m alembic current

# Show migration history
python -m alembic history

# Show detailed migration history
python -m alembic history --verbose
```

#### Apply Migrations
```bash
# Upgrade to latest migration
python -m alembic upgrade head

# Upgrade to specific migration
python -m alembic upgrade <revision_id>

# Upgrade by relative steps
python -m alembic upgrade +2  # Upgrade 2 steps forward
```

#### Downgrade Migrations (Use with Caution)
```bash
# Downgrade to previous migration
python -m alembic downgrade -1

# Downgrade to specific migration
python -m alembic downgrade <revision_id>

# Downgrade to base (removes all migrations)
python -m alembic downgrade base
```

### Using CLI Commands

The backend provides a CLI command for migrations:

```bash
# From project root
python backend/main.py migrate
```

**Note**: This command changes to the backend directory automatically and runs Alembic migrations.

### Using Application Startup

The application can automatically run migrations on startup:

```python
# In app.py lifespan function
engine = initialize_database(run_migrations=True, create_tables_if_needed=True)
```

## Database Configuration

### Environment-Based Configuration

The system uses different database configurations based on environment:

- **Production**: Uses `DATABASE_URL` if provided
- **Development/Testing**: Attempts `DATABASE_URL`, falls back to SQLite
- **Fallback**: Always uses SQLite at `./data/app.db`

### Configuration Settings

```python
# In config/settings.py
DATABASE_URL: Optional[str] = None  # Primary database URL
SQLITE_DATABASE_PATH: str = "./data/app.db"  # SQLite fallback path
DB_ECHO: bool = False  # Enable SQL logging
```

## Creating New Migrations

### Auto-Generate Migrations

When you modify SQLAlchemy models, create a new migration:

```bash
# Generate migration automatically
python -m alembic revision --autogenerate -m "Description of changes"

# Review the generated migration file
# Edit if necessary before applying
```

### Manual Migrations

For complex changes, create empty migration files:

```bash
# Create empty migration
python -m alembic revision -m "Manual migration description"

# Edit the generated file to add custom migration logic
```

## Troubleshooting

### Common Issues

#### 1. Migration Fails Due to Database Connection

**Symptoms**: Connection errors, authentication failures

**Solution**: 
- Check database configuration
- Verify connection strings
- System will automatically fall back to SQLite

#### 2. Migration Conflicts

**Symptoms**: Multiple heads, conflicting migrations

**Solution**:
```bash
# Check for multiple heads
python -m alembic heads

# Merge conflicting migrations
python -m alembic merge -m "Merge conflicts" <head1> <head2>
```

#### 3. Schema Drift

**Symptoms**: Database schema doesn't match models

**Solution**:
```bash
# Check for differences
python -m alembic revision --autogenerate -m "Fix schema drift"

# Review and apply the generated migration
python -m alembic upgrade head
```

### Recovery Procedures

#### Reset Database (Development Only)

```bash
# WARNING: This will destroy all data
rm data/app.db  # Remove SQLite database
python -m alembic upgrade head  # Recreate from migrations
```

#### Stamp Database

If you need to mark a database as being at a specific migration without running it:

```bash
python -m alembic stamp head  # Mark as current
python -m alembic stamp <revision_id>  # Mark as specific revision
```

## Best Practices

### 1. Always Backup Before Migrations

```bash
# For SQLite
cp data/app.db data/app.db.backup

# For SQL Server
# Use appropriate backup procedures
```

### 2. Test Migrations

```bash
# Test on development database first
python -m alembic upgrade head

# Verify application works correctly
python main.py run
```

### 3. Review Generated Migrations

Always review auto-generated migrations before applying:
- Check for data loss operations
- Verify foreign key constraints
- Ensure proper indexing

### 4. Use Descriptive Migration Messages

```bash
# Good
python -m alembic revision --autogenerate -m "Add user preferences table"

# Bad
python -m alembic revision --autogenerate -m "Update"
```

## Migration File Structure

```
backend/alembic/
├── alembic.ini          # Alembic configuration
├── env.py              # Migration environment setup
├── script.py.mako      # Migration template
└── versions/           # Migration files
    └── a07f0695d364_initial_migration_with_all_models.py
```

## Verification

After upgrading, verify the migration was successful:

```bash
# Check current status
python -m alembic current

# Verify database tables exist
python -c "
from core.database.engine import create_engine
from sqlalchemy import text
engine = create_engine()
with engine.connect() as conn:
    result = conn.execute(text('SELECT name FROM sqlite_master WHERE type=\"table\";'))
    print([row[0] for row in result])
"
```

## Support

For additional help:
- Check the [Database Architecture](../core/database/architecture.md) documentation
- Review the [Migration Architecture](../core/database/migrations-architecture.md) specification
- Consult Alembic documentation: https://alembic.sqlalchemy.org/

## Related Documentation

- [Database Architecture](../core/database/architecture.md)
- [Migration Architecture](../core/database/migrations-architecture.md)
- [Configuration Guide](how-to_config.md)
- [Main Application Guide](how-to_main.md)
