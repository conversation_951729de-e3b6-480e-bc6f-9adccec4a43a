<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Ultimate Electrical Designer Backend Coverage Report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_718ce007.css" type="text/css">
    <script src="coverage_html_cb_497bf287.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Ultimate Electrical Designer Backend Coverage Report:
            <span class="pc_cov">23.59%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>n</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>b</kbd>
                        <kbd>p</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button" href="index.html">Files</a>
                <a class="button" href="function_index.html">Functions</a>
                <a class="button current">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.0">coverage.py v7.8.0</a>,
            created at 2025-06-03 23:24 +0300
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="region" class="name left" aria-sort="none" data-default-sort-order="ascending" data-shortcut="n">class<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="branches" aria-sort="none" data-default-sort-order="descending" data-shortcut="b">branches<span class="arrows"></span></th>
                <th id="partial" aria-sort="none" data-default-sort-order="descending" data-shortcut="p">partial<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_10fae538ba4e8521_dependencies_py.html">api\dependencies.py</a></td>
                <td class="name left"><a href="z_10fae538ba4e8521_dependencies_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>16</td>
                <td>9</td>
                <td>0</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="7 22">31.82%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_10fae538ba4e8521_main_router_py.html">api\main_router.py</a></td>
                <td class="name left"><a href="z_10fae538ba4e8521_main_router_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>24</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="24 24">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3c8229fafc2171e7_activity_log_routes_py.html">api\v1\activity_log_routes.py</a></td>
                <td class="name left"><a href="z_3c8229fafc2171e7_activity_log_routes_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>147</td>
                <td>108</td>
                <td>18</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="39 159">24.53%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3c8229fafc2171e7_component_routes_py.html">api\v1\component_routes.py</a></td>
                <td class="name left"><a href="z_3c8229fafc2171e7_component_routes_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>137</td>
                <td>108</td>
                <td>14</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="29 139">20.86%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3c8229fafc2171e7_document_routes_py.html">api\v1\document_routes.py</a></td>
                <td class="name left"><a href="z_3c8229fafc2171e7_document_routes_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>149</td>
                <td>102</td>
                <td>16</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="47 161">29.19%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3c8229fafc2171e7_electrical_routes_py.html">api\v1\electrical_routes.py</a></td>
                <td class="name left"><a href="z_3c8229fafc2171e7_electrical_routes_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>243</td>
                <td>192</td>
                <td>32</td>
                <td>36</td>
                <td>0</td>
                <td class="right" data-ratio="51 279">18.28%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3c8229fafc2171e7_heat_tracing_routes_py.html">api\v1\heat_tracing_routes.py</a></td>
                <td class="name left"><a href="z_3c8229fafc2171e7_heat_tracing_routes_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>186</td>
                <td>147</td>
                <td>22</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="39 188">20.74%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3c8229fafc2171e7_import_export_routes_py.html#t29">api\v1\import_export_routes.py</a></td>
                <td class="name left"><a href="z_3c8229fafc2171e7_import_export_routes_py.html#t29"><data value='ImportRequest'>ImportRequest</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3c8229fafc2171e7_import_export_routes_py.html#t39">api\v1\import_export_routes.py</a></td>
                <td class="name left"><a href="z_3c8229fafc2171e7_import_export_routes_py.html#t39"><data value='MultiSheetImportRequest'>MultiSheetImportRequest</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3c8229fafc2171e7_import_export_routes_py.html#t46">api\v1\import_export_routes.py</a></td>
                <td class="name left"><a href="z_3c8229fafc2171e7_import_export_routes_py.html#t46"><data value='TemplateRequest'>TemplateRequest</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3c8229fafc2171e7_import_export_routes_py.html#t53">api\v1\import_export_routes.py</a></td>
                <td class="name left"><a href="z_3c8229fafc2171e7_import_export_routes_py.html#t53"><data value='ImportResponse'>ImportResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3c8229fafc2171e7_import_export_routes_py.html">api\v1\import_export_routes.py</a></td>
                <td class="name left"><a href="z_3c8229fafc2171e7_import_export_routes_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>225</td>
                <td>167</td>
                <td>12</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="58 233">24.89%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3c8229fafc2171e7_project_routes_py.html">api\v1\project_routes.py</a></td>
                <td class="name left"><a href="z_3c8229fafc2171e7_project_routes_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>70</td>
                <td>48</td>
                <td>1</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="22 72">30.56%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3c8229fafc2171e7_reports_routes_py.html#t27">api\v1\reports_routes.py</a></td>
                <td class="name left"><a href="z_3c8229fafc2171e7_reports_routes_py.html#t27"><data value='ReportGenerationRequest'>ReportGenerationRequest</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3c8229fafc2171e7_reports_routes_py.html#t38">api\v1\reports_routes.py</a></td>
                <td class="name left"><a href="z_3c8229fafc2171e7_reports_routes_py.html#t38"><data value='CableScheduleRequest'>CableScheduleRequest</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3c8229fafc2171e7_reports_routes_py.html#t47">api\v1\reports_routes.py</a></td>
                <td class="name left"><a href="z_3c8229fafc2171e7_reports_routes_py.html#t47"><data value='DashboardRequest'>DashboardRequest</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3c8229fafc2171e7_reports_routes_py.html#t56">api\v1\reports_routes.py</a></td>
                <td class="name left"><a href="z_3c8229fafc2171e7_reports_routes_py.html#t56"><data value='DataExportRequest'>DataExportRequest</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3c8229fafc2171e7_reports_routes_py.html#t64">api\v1\reports_routes.py</a></td>
                <td class="name left"><a href="z_3c8229fafc2171e7_reports_routes_py.html#t64"><data value='ReportPackageRequest'>ReportPackageRequest</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3c8229fafc2171e7_reports_routes_py.html#t74">api\v1\reports_routes.py</a></td>
                <td class="name left"><a href="z_3c8229fafc2171e7_reports_routes_py.html#t74"><data value='ReportResponse'>ReportResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3c8229fafc2171e7_reports_routes_py.html">api\v1\reports_routes.py</a></td>
                <td class="name left"><a href="z_3c8229fafc2171e7_reports_routes_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>168</td>
                <td>97</td>
                <td>10</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="71 174">40.80%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3c8229fafc2171e7_standards_routes_py.html#t25">api\v1\standards_routes.py</a></td>
                <td class="name left"><a href="z_3c8229fafc2171e7_standards_routes_py.html#t25"><data value='DesignValidationRequest'>DesignValidationRequest</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3c8229fafc2171e7_standards_routes_py.html#t32">api\v1\standards_routes.py</a></td>
                <td class="name left"><a href="z_3c8229fafc2171e7_standards_routes_py.html#t32"><data value='ParameterCalculationRequest'>ParameterCalculationRequest</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3c8229fafc2171e7_standards_routes_py.html#t39">api\v1\standards_routes.py</a></td>
                <td class="name left"><a href="z_3c8229fafc2171e7_standards_routes_py.html#t39"><data value='StandardsRecommendationRequest'>StandardsRecommendationRequest</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3c8229fafc2171e7_standards_routes_py.html#t45">api\v1\standards_routes.py</a></td>
                <td class="name left"><a href="z_3c8229fafc2171e7_standards_routes_py.html#t45"><data value='ValidationResponse'>ValidationResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3c8229fafc2171e7_standards_routes_py.html#t55">api\v1\standards_routes.py</a></td>
                <td class="name left"><a href="z_3c8229fafc2171e7_standards_routes_py.html#t55"><data value='CalculationResponse'>CalculationResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3c8229fafc2171e7_standards_routes_py.html">api\v1\standards_routes.py</a></td>
                <td class="name left"><a href="z_3c8229fafc2171e7_standards_routes_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>148</td>
                <td>97</td>
                <td>11</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="51 150">34.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3c8229fafc2171e7_switchboard_routes_py.html">api\v1\switchboard_routes.py</a></td>
                <td class="name left"><a href="z_3c8229fafc2171e7_switchboard_routes_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>140</td>
                <td>104</td>
                <td>17</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="36 152">23.68%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3c8229fafc2171e7_user_routes_py.html">api\v1\user_routes.py</a></td>
                <td class="name left"><a href="z_3c8229fafc2171e7_user_routes_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>112</td>
                <td>79</td>
                <td>97</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="33 128">25.78%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1ea838694151cac8_logging_config_py.html">config\logging_config.py</a></td>
                <td class="name left"><a href="z_1ea838694151cac8_logging_config_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>47</td>
                <td>5</td>
                <td>1</td>
                <td>12</td>
                <td>5</td>
                <td class="right" data-ratio="49 59">83.05%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1ea838694151cac8_settings_py.html#t11">config\settings.py</a></td>
                <td class="name left"><a href="z_1ea838694151cac8_settings_py.html#t11"><data value='Settings'>Settings</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1ea838694151cac8_settings_py.html">config\settings.py</a></td>
                <td class="name left"><a href="z_1ea838694151cac8_settings_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>29</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="29 29">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d51c265d7ed55fb9___init___py.html">core\calculations\__init__.py</a></td>
                <td class="name left"><a href="z_d51c265d7ed55fb9___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d51c265d7ed55fb9_calculation_service_py.html#t40">core\calculations\calculation_service.py</a></td>
                <td class="name left"><a href="z_d51c265d7ed55fb9_calculation_service_py.html#t40"><data value='CalculationInput'>CalculationInput</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d51c265d7ed55fb9_calculation_service_py.html#t47">core\calculations\calculation_service.py</a></td>
                <td class="name left"><a href="z_d51c265d7ed55fb9_calculation_service_py.html#t47"><data value='CalculationResult'>CalculationResult</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d51c265d7ed55fb9_calculation_service_py.html#t64">core\calculations\calculation_service.py</a></td>
                <td class="name left"><a href="z_d51c265d7ed55fb9_calculation_service_py.html#t64"><data value='HeatLossInput'>HeatLossInput</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d51c265d7ed55fb9_calculation_service_py.html#t78">core\calculations\calculation_service.py</a></td>
                <td class="name left"><a href="z_d51c265d7ed55fb9_calculation_service_py.html#t78"><data value='HeatLossResult'>HeatLossResult</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d51c265d7ed55fb9_calculation_service_py.html#t88">core\calculations\calculation_service.py</a></td>
                <td class="name left"><a href="z_d51c265d7ed55fb9_calculation_service_py.html#t88"><data value='CableSizingInput'>CableSizingInput</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d51c265d7ed55fb9_calculation_service_py.html#t100">core\calculations\calculation_service.py</a></td>
                <td class="name left"><a href="z_d51c265d7ed55fb9_calculation_service_py.html#t100"><data value='CableSizingResult'>CableSizingResult</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d51c265d7ed55fb9_calculation_service_py.html#t111">core\calculations\calculation_service.py</a></td>
                <td class="name left"><a href="z_d51c265d7ed55fb9_calculation_service_py.html#t111"><data value='CalculationService'>CalculationService</data></a></td>
                <td>330</td>
                <td>330</td>
                <td>22</td>
                <td>138</td>
                <td>0</td>
                <td class="right" data-ratio="0 468">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d51c265d7ed55fb9_calculation_service_py.html">core\calculations\calculation_service.py</a></td>
                <td class="name left"><a href="z_d51c265d7ed55fb9_calculation_service_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>81</td>
                <td>4</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="77 81">95.06%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0a7bfb953e6da477___init___py.html">core\calculations\circuit_design\__init__.py</a></td>
                <td class="name left"><a href="z_0a7bfb953e6da477___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0a7bfb953e6da477_circuit_breaker_sizing_py.html#t58">core\calculations\circuit_design\circuit_breaker_sizing.py</a></td>
                <td class="name left"><a href="z_0a7bfb953e6da477_circuit_breaker_sizing_py.html#t58"><data value='BreakerSizingResult'>BreakerSizingResult</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0a7bfb953e6da477_circuit_breaker_sizing_py.html">core\calculations\circuit_design\circuit_breaker_sizing.py</a></td>
                <td class="name left"><a href="z_0a7bfb953e6da477_circuit_breaker_sizing_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>104</td>
                <td>79</td>
                <td>6</td>
                <td>34</td>
                <td>0</td>
                <td class="right" data-ratio="25 138">18.12%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0a7bfb953e6da477_control_circuit_logic_py.html#t20">core\calculations\circuit_design\control_circuit_logic.py</a></td>
                <td class="name left"><a href="z_0a7bfb953e6da477_control_circuit_logic_py.html#t20"><data value='ControlType'>ControlType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0a7bfb953e6da477_control_circuit_logic_py.html#t28">core\calculations\circuit_design\control_circuit_logic.py</a></td>
                <td class="name left"><a href="z_0a7bfb953e6da477_control_circuit_logic_py.html#t28"><data value='ContactorType'>ContactorType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0a7bfb953e6da477_control_circuit_logic_py.html#t36">core\calculations\circuit_design\control_circuit_logic.py</a></td>
                <td class="name left"><a href="z_0a7bfb953e6da477_control_circuit_logic_py.html#t36"><data value='ControlCircuitDesign'>ControlCircuitDesign</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0a7bfb953e6da477_control_circuit_logic_py.html">core\calculations\circuit_design\control_circuit_logic.py</a></td>
                <td class="name left"><a href="z_0a7bfb953e6da477_control_circuit_logic_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>153</td>
                <td>117</td>
                <td>5</td>
                <td>44</td>
                <td>0</td>
                <td class="right" data-ratio="36 197">18.27%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bf815d1c1bf6cbfb___init___py.html">core\calculations\common_properties\__init__.py</a></td>
                <td class="name left"><a href="z_bf815d1c1bf6cbfb___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bf815d1c1bf6cbfb_fluid_properties_py.html">core\calculations\common_properties\fluid_properties.py</a></td>
                <td class="name left"><a href="z_bf815d1c1bf6cbfb_fluid_properties_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>102</td>
                <td>88</td>
                <td>8</td>
                <td>32</td>
                <td>0</td>
                <td class="right" data-ratio="14 134">10.45%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bf815d1c1bf6cbfb_material_data_py.html">core\calculations\common_properties\material_data.py</a></td>
                <td class="name left"><a href="z_bf815d1c1bf6cbfb_material_data_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>68</td>
                <td>55</td>
                <td>13</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="13 82">15.85%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_52a2768ca9a74860___init___py.html">core\calculations\electrical_sizing\__init__.py</a></td>
                <td class="name left"><a href="z_52a2768ca9a74860___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_52a2768ca9a74860_cable_sizing_py.html">core\calculations\electrical_sizing\cable_sizing.py</a></td>
                <td class="name left"><a href="z_52a2768ca9a74860_cable_sizing_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>77</td>
                <td>66</td>
                <td>5</td>
                <td>26</td>
                <td>0</td>
                <td class="right" data-ratio="11 103">10.68%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_52a2768ca9a74860_voltage_drop_py.html">core\calculations\electrical_sizing\voltage_drop.py</a></td>
                <td class="name left"><a href="z_52a2768ca9a74860_voltage_drop_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>74</td>
                <td>64</td>
                <td>8</td>
                <td>26</td>
                <td>0</td>
                <td class="right" data-ratio="10 100">10.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_df3ce16558279790___init___py.html">core\calculations\heat_loss\__init__.py</a></td>
                <td class="name left"><a href="z_df3ce16558279790___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_df3ce16558279790_insulation_properties_py.html">core\calculations\heat_loss\insulation_properties.py</a></td>
                <td class="name left"><a href="z_df3ce16558279790_insulation_properties_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>69</td>
                <td>56</td>
                <td>7</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="13 89">14.61%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_df3ce16558279790_pipe_heat_loss_py.html">core\calculations\heat_loss\pipe_heat_loss.py</a></td>
                <td class="name left"><a href="z_df3ce16558279790_pipe_heat_loss_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>96</td>
                <td>81</td>
                <td>3</td>
                <td>34</td>
                <td>0</td>
                <td class="right" data-ratio="15 130">11.54%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_df3ce16558279790_vessel_heat_loss_py.html">core\calculations\heat_loss\vessel_heat_loss.py</a></td>
                <td class="name left"><a href="z_df3ce16558279790_vessel_heat_loss_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>62</td>
                <td>53</td>
                <td>2</td>
                <td>22</td>
                <td>0</td>
                <td class="right" data-ratio="9 84">10.71%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e4d2cb2bb52b2279___init___py.html">core\calculations\utils\__init__.py</a></td>
                <td class="name left"><a href="z_e4d2cb2bb52b2279___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e4d2cb2bb52b2279_input_parser_py.html">core\calculations\utils\input_parser.py</a></td>
                <td class="name left"><a href="z_e4d2cb2bb52b2279_input_parser_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>153</td>
                <td>134</td>
                <td>15</td>
                <td>70</td>
                <td>0</td>
                <td class="right" data-ratio="19 223">8.52%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e4d2cb2bb52b2279_math_helpers_py.html">core\calculations\utils\math_helpers.py</a></td>
                <td class="name left"><a href="z_e4d2cb2bb52b2279_math_helpers_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>162</td>
                <td>145</td>
                <td>17</td>
                <td>58</td>
                <td>0</td>
                <td class="right" data-ratio="17 220">7.73%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e4d2cb2bb52b2279_units_conversion_py.html">core\calculations\utils\units_conversion.py</a></td>
                <td class="name left"><a href="z_e4d2cb2bb52b2279_units_conversion_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>146</td>
                <td>124</td>
                <td>16</td>
                <td>48</td>
                <td>0</td>
                <td class="right" data-ratio="22 194">11.34%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e4d2cb2bb52b2279_validation_rules_py.html">core\calculations\utils\validation_rules.py</a></td>
                <td class="name left"><a href="z_e4d2cb2bb52b2279_validation_rules_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>120</td>
                <td>102</td>
                <td>10</td>
                <td>48</td>
                <td>0</td>
                <td class="right" data-ratio="18 168">10.71%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_dd0cc83923a11228___init___py.html">core\data_import\__init__.py</a></td>
                <td class="name left"><a href="z_dd0cc83923a11228___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_dd0cc83923a11228_global_importer_py.html#t23">core\data_import\global_importer.py</a></td>
                <td class="name left"><a href="z_dd0cc83923a11228_global_importer_py.html#t23"><data value='GlobalImporter'>GlobalImporter</data></a></td>
                <td>123</td>
                <td>123</td>
                <td>12</td>
                <td>46</td>
                <td>0</td>
                <td class="right" data-ratio="0 169">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_dd0cc83923a11228_global_importer_py.html">core\data_import\global_importer.py</a></td>
                <td class="name left"><a href="z_dd0cc83923a11228_global_importer_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>21</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="21 21">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_dd0cc83923a11228_import_service_py.html#t37">core\data_import\import_service.py</a></td>
                <td class="name left"><a href="z_dd0cc83923a11228_import_service_py.html#t37"><data value='ImportService'>ImportService</data></a></td>
                <td>121</td>
                <td>121</td>
                <td>12</td>
                <td>36</td>
                <td>0</td>
                <td class="right" data-ratio="0 157">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_dd0cc83923a11228_import_service_py.html">core\data_import\import_service.py</a></td>
                <td class="name left"><a href="z_dd0cc83923a11228_import_service_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>21</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="21 21">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f712d5acc85bf35f___init___py.html">core\data_import\mappers\__init__.py</a></td>
                <td class="name left"><a href="z_f712d5acc85bf35f___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f712d5acc85bf35f_catalog_data_mapper_py.html#t19">core\data_import\mappers\catalog_data_mapper.py</a></td>
                <td class="name left"><a href="z_f712d5acc85bf35f_catalog_data_mapper_py.html#t19"><data value='CatalogDataMapper'>CatalogDataMapper</data></a></td>
                <td>159</td>
                <td>159</td>
                <td>9</td>
                <td>72</td>
                <td>0</td>
                <td class="right" data-ratio="0 231">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f712d5acc85bf35f_catalog_data_mapper_py.html">core\data_import\mappers\catalog_data_mapper.py</a></td>
                <td class="name left"><a href="z_f712d5acc85bf35f_catalog_data_mapper_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>20</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="20 20">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f712d5acc85bf35f_project_data_mapper_py.html#t19">core\data_import\mappers\project_data_mapper.py</a></td>
                <td class="name left"><a href="z_f712d5acc85bf35f_project_data_mapper_py.html#t19"><data value='ProjectDataMapper'>ProjectDataMapper</data></a></td>
                <td>141</td>
                <td>141</td>
                <td>9</td>
                <td>52</td>
                <td>0</td>
                <td class="right" data-ratio="0 193">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f712d5acc85bf35f_project_data_mapper_py.html">core\data_import\mappers\project_data_mapper.py</a></td>
                <td class="name left"><a href="z_f712d5acc85bf35f_project_data_mapper_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>20</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="20 20">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b263df31e4f5f767___init___py.html">core\data_import\parsers\__init__.py</a></td>
                <td class="name left"><a href="z_b263df31e4f5f767___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b263df31e4f5f767_csv_parser_py.html#t21">core\data_import\parsers\csv_parser.py</a></td>
                <td class="name left"><a href="z_b263df31e4f5f767_csv_parser_py.html#t21"><data value='CSVParser'>CSVParser</data></a></td>
                <td>150</td>
                <td>150</td>
                <td>14</td>
                <td>54</td>
                <td>0</td>
                <td class="right" data-ratio="0 204">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b263df31e4f5f767_csv_parser_py.html">core\data_import\parsers\csv_parser.py</a></td>
                <td class="name left"><a href="z_b263df31e4f5f767_csv_parser_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>18</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="18 18">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b263df31e4f5f767_json_parser_py.html#t21">core\data_import\parsers\json_parser.py</a></td>
                <td class="name left"><a href="z_b263df31e4f5f767_json_parser_py.html#t21"><data value='JSONParser'>JSONParser</data></a></td>
                <td>171</td>
                <td>171</td>
                <td>13</td>
                <td>68</td>
                <td>0</td>
                <td class="right" data-ratio="0 239">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b263df31e4f5f767_json_parser_py.html">core\data_import\parsers\json_parser.py</a></td>
                <td class="name left"><a href="z_b263df31e4f5f767_json_parser_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>18</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="18 18">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b263df31e4f5f767_xlsx_parser_py.html#t21">core\data_import\parsers\xlsx_parser.py</a></td>
                <td class="name left"><a href="z_b263df31e4f5f767_xlsx_parser_py.html#t21"><data value='XLSXParser'>XLSXParser</data></a></td>
                <td>113</td>
                <td>113</td>
                <td>10</td>
                <td>38</td>
                <td>0</td>
                <td class="right" data-ratio="0 151">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b263df31e4f5f767_xlsx_parser_py.html">core\data_import\parsers\xlsx_parser.py</a></td>
                <td class="name left"><a href="z_b263df31e4f5f767_xlsx_parser_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>17</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="17 17">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_dd0cc83923a11228_project_importer_py.html#t22">core\data_import\project_importer.py</a></td>
                <td class="name left"><a href="z_dd0cc83923a11228_project_importer_py.html#t22"><data value='ProjectImporter'>ProjectImporter</data></a></td>
                <td>168</td>
                <td>168</td>
                <td>14</td>
                <td>56</td>
                <td>0</td>
                <td class="right" data-ratio="0 224">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_dd0cc83923a11228_project_importer_py.html">core\data_import\project_importer.py</a></td>
                <td class="name left"><a href="z_dd0cc83923a11228_project_importer_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>25</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="25 25">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4343209610adb865___init___py.html">core\data_import\validators\__init__.py</a></td>
                <td class="name left"><a href="z_4343209610adb865___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4343209610adb865_import_data_validator_py.html#t19">core\data_import\validators\import_data_validator.py</a></td>
                <td class="name left"><a href="z_4343209610adb865_import_data_validator_py.html#t19"><data value='ValidationResult'>ValidationResult</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4343209610adb865_import_data_validator_py.html#t77">core\data_import\validators\import_data_validator.py</a></td>
                <td class="name left"><a href="z_4343209610adb865_import_data_validator_py.html#t77"><data value='ImportDataValidator'>ImportDataValidator</data></a></td>
                <td>159</td>
                <td>159</td>
                <td>9</td>
                <td>134</td>
                <td>0</td>
                <td class="right" data-ratio="0 293">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4343209610adb865_import_data_validator_py.html">core\data_import\validators\import_data_validator.py</a></td>
                <td class="name left"><a href="z_4343209610adb865_import_data_validator_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>27</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="27 27">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2e48d8fb49f22d07___init___py.html">core\database\__init__.py</a></td>
                <td class="name left"><a href="z_2e48d8fb49f22d07___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2e48d8fb49f22d07_dependencies_py.html">core\database\dependencies.py</a></td>
                <td class="name left"><a href="z_2e48d8fb49f22d07_dependencies_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>7</td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 7">71.43%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2e48d8fb49f22d07_engine_py.html">core\database\engine.py</a></td>
                <td class="name left"><a href="z_2e48d8fb49f22d07_engine_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>77</td>
                <td>59</td>
                <td>6</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="18 97">18.56%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2e48d8fb49f22d07_initialization_py.html">core\database\initialization.py</a></td>
                <td class="name left"><a href="z_2e48d8fb49f22d07_initialization_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>94</td>
                <td>73</td>
                <td>12</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="21 100">21.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2e48d8fb49f22d07_session_py.html">core\database\session.py</a></td>
                <td class="name left"><a href="z_2e48d8fb49f22d07_session_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>71</td>
                <td>50</td>
                <td>7</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="21 83">25.30%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5d678fac7219f0d6_error_factory_py.html#t6">core\errors\error_factory.py</a></td>
                <td class="name left"><a href="z_5d678fac7219f0d6_error_factory_py.html#t6"><data value='ErrorFactory'>ErrorFactory</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5d678fac7219f0d6_error_factory_py.html">core\errors\error_factory.py</a></td>
                <td class="name left"><a href="z_5d678fac7219f0d6_error_factory_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5d678fac7219f0d6_error_registry_py.html">core\errors\error_registry.py</a></td>
                <td class="name left"><a href="z_5d678fac7219f0d6_error_registry_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5d678fac7219f0d6_error_templates_py.html">core\errors\error_templates.py</a></td>
                <td class="name left"><a href="z_5d678fac7219f0d6_error_templates_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5d678fac7219f0d6_exceptions_py.html#t5">core\errors\exceptions.py</a></td>
                <td class="name left"><a href="z_5d678fac7219f0d6_exceptions_py.html#t5"><data value='BaseApplicationException'>BaseApplicationException</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5d678fac7219f0d6_exceptions_py.html#t25">core\errors\exceptions.py</a></td>
                <td class="name left"><a href="z_5d678fac7219f0d6_exceptions_py.html#t25"><data value='NotFoundError'>NotFoundError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5d678fac7219f0d6_exceptions_py.html#t29">core\errors\exceptions.py</a></td>
                <td class="name left"><a href="z_5d678fac7219f0d6_exceptions_py.html#t29"><data value='ProjectNotFoundError'>ProjectNotFoundError</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5d678fac7219f0d6_exceptions_py.html#t40">core\errors\exceptions.py</a></td>
                <td class="name left"><a href="z_5d678fac7219f0d6_exceptions_py.html#t40"><data value='DataValidationError'>DataValidationError</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5d678fac7219f0d6_exceptions_py.html#t51">core\errors\exceptions.py</a></td>
                <td class="name left"><a href="z_5d678fac7219f0d6_exceptions_py.html#t51"><data value='InvalidInputError'>InvalidInputError</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5d678fac7219f0d6_exceptions_py.html#t62">core\errors\exceptions.py</a></td>
                <td class="name left"><a href="z_5d678fac7219f0d6_exceptions_py.html#t62"><data value='DuplicateEntryError'>DuplicateEntryError</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5d678fac7219f0d6_exceptions_py.html#t78">core\errors\exceptions.py</a></td>
                <td class="name left"><a href="z_5d678fac7219f0d6_exceptions_py.html#t78"><data value='DatabaseError'>DatabaseError</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5d678fac7219f0d6_exceptions_py.html#t94">core\errors\exceptions.py</a></td>
                <td class="name left"><a href="z_5d678fac7219f0d6_exceptions_py.html#t94"><data value='ComponentNotFoundError'>ComponentNotFoundError</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5d678fac7219f0d6_exceptions_py.html#t105">core\errors\exceptions.py</a></td>
                <td class="name left"><a href="z_5d678fac7219f0d6_exceptions_py.html#t105"><data value='CalculationError'>CalculationError</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5d678fac7219f0d6_exceptions_py.html#t116">core\errors\exceptions.py</a></td>
                <td class="name left"><a href="z_5d678fac7219f0d6_exceptions_py.html#t116"><data value='StandardComplianceError'>StandardComplianceError</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5d678fac7219f0d6_exceptions_py.html#t123">core\errors\exceptions.py</a></td>
                <td class="name left"><a href="z_5d678fac7219f0d6_exceptions_py.html#t123"><data value='BusinessLogicError'>BusinessLogicError</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5d678fac7219f0d6_exceptions_py.html">core\errors\exceptions.py</a></td>
                <td class="name left"><a href="z_5d678fac7219f0d6_exceptions_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>22</td>
                <td>0</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="22 22">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f2bf686f1e66ce65___init___py.html">core\models\__init__.py</a></td>
                <td class="name left"><a href="z_f2bf686f1e66ce65___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f2bf686f1e66ce65_activity_log_py.html#t11">core\models\activity_log.py</a></td>
                <td class="name left"><a href="z_f2bf686f1e66ce65_activity_log_py.html#t11"><data value='ActivityLog'>ActivityLog</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f2bf686f1e66ce65_activity_log_py.html">core\models\activity_log.py</a></td>
                <td class="name left"><a href="z_f2bf686f1e66ce65_activity_log_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>13</td>
                <td>0</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="13 13">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f2bf686f1e66ce65_base_py.html#t15">core\models\base.py</a></td>
                <td class="name left"><a href="z_f2bf686f1e66ce65_base_py.html#t15"><data value='CommonColumns'>CommonColumns</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f2bf686f1e66ce65_base_py.html#t40">core\models\base.py</a></td>
                <td class="name left"><a href="z_f2bf686f1e66ce65_base_py.html#t40"><data value='SoftDeleteColumns'>SoftDeleteColumns</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f2bf686f1e66ce65_base_py.html#t65">core\models\base.py</a></td>
                <td class="name left"><a href="z_f2bf686f1e66ce65_base_py.html#t65"><data value='EnumType'>EnumType</data></a></td>
                <td>16</td>
                <td>14</td>
                <td>0</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="2 24">8.33%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f2bf686f1e66ce65_base_py.html">core\models\base.py</a></td>
                <td class="name left"><a href="z_f2bf686f1e66ce65_base_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>33</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="33 33">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f2bf686f1e66ce65_components_py.html#t15">core\models\components.py</a></td>
                <td class="name left"><a href="z_f2bf686f1e66ce65_components_py.html#t15"><data value='ComponentCategory'>ComponentCategory</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f2bf686f1e66ce65_components_py.html#t44">core\models\components.py</a></td>
                <td class="name left"><a href="z_f2bf686f1e66ce65_components_py.html#t44"><data value='Component'>Component</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f2bf686f1e66ce65_components_py.html">core\models\components.py</a></td>
                <td class="name left"><a href="z_f2bf686f1e66ce65_components_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>21</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="21 21">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f2bf686f1e66ce65_documents_py.html#t10">core\models\documents.py</a></td>
                <td class="name left"><a href="z_f2bf686f1e66ce65_documents_py.html#t10"><data value='ImportedDataRevision'>ImportedDataRevision</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f2bf686f1e66ce65_documents_py.html#t47">core\models\documents.py</a></td>
                <td class="name left"><a href="z_f2bf686f1e66ce65_documents_py.html#t47"><data value='ExportedDocument'>ExportedDocument</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f2bf686f1e66ce65_documents_py.html#t88">core\models\documents.py</a></td>
                <td class="name left"><a href="z_f2bf686f1e66ce65_documents_py.html#t88"><data value='CalculationStandard'>CalculationStandard</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f2bf686f1e66ce65_documents_py.html">core\models\documents.py</a></td>
                <td class="name left"><a href="z_f2bf686f1e66ce65_documents_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>36</td>
                <td>0</td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="36 36">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f2bf686f1e66ce65_electrical_py.html#t13">core\models\electrical.py</a></td>
                <td class="name left"><a href="z_f2bf686f1e66ce65_electrical_py.html#t13"><data value='ElectricalNode'>ElectricalNode</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f2bf686f1e66ce65_electrical_py.html#t85">core\models\electrical.py</a></td>
                <td class="name left"><a href="z_f2bf686f1e66ce65_electrical_py.html#t85"><data value='CableRoute'>CableRoute</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f2bf686f1e66ce65_electrical_py.html#t147">core\models\electrical.py</a></td>
                <td class="name left"><a href="z_f2bf686f1e66ce65_electrical_py.html#t147"><data value='CableSegment'>CableSegment</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f2bf686f1e66ce65_electrical_py.html#t204">core\models\electrical.py</a></td>
                <td class="name left"><a href="z_f2bf686f1e66ce65_electrical_py.html#t204"><data value='LoadCalculation'>LoadCalculation</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f2bf686f1e66ce65_electrical_py.html#t301">core\models\electrical.py</a></td>
                <td class="name left"><a href="z_f2bf686f1e66ce65_electrical_py.html#t301"><data value='VoltageDropCalculation'>VoltageDropCalculation</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f2bf686f1e66ce65_electrical_py.html">core\models\electrical.py</a></td>
                <td class="name left"><a href="z_f2bf686f1e66ce65_electrical_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>129</td>
                <td>0</td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="129 129">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f2bf686f1e66ce65_enums_py.html#t3">core\models\enums.py</a></td>
                <td class="name left"><a href="z_f2bf686f1e66ce65_enums_py.html#t3"><data value='InstallationEnvironment'>InstallationEnvironment</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f2bf686f1e66ce65_enums_py.html#t7">core\models\enums.py</a></td>
                <td class="name left"><a href="z_f2bf686f1e66ce65_enums_py.html#t7"><data value='HTCircuitApplicationType'>HTCircuitApplicationType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f2bf686f1e66ce65_enums_py.html#t11">core\models\enums.py</a></td>
                <td class="name left"><a href="z_f2bf686f1e66ce65_enums_py.html#t11"><data value='HeatingMethodType'>HeatingMethodType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f2bf686f1e66ce65_enums_py.html#t18">core\models\enums.py</a></td>
                <td class="name left"><a href="z_f2bf686f1e66ce65_enums_py.html#t18"><data value='SwitchboardType'>SwitchboardType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f2bf686f1e66ce65_enums_py.html#t22">core\models\enums.py</a></td>
                <td class="name left"><a href="z_f2bf686f1e66ce65_enums_py.html#t22"><data value='PipeMaterialType'>PipeMaterialType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f2bf686f1e66ce65_enums_py.html#t27">core\models\enums.py</a></td>
                <td class="name left"><a href="z_f2bf686f1e66ce65_enums_py.html#t27"><data value='ControlCircuitType'>ControlCircuitType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f2bf686f1e66ce65_enums_py.html#t31">core\models\enums.py</a></td>
                <td class="name left"><a href="z_f2bf686f1e66ce65_enums_py.html#t31"><data value='SensorType'>SensorType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f2bf686f1e66ce65_enums_py.html#t37">core\models\enums.py</a></td>
                <td class="name left"><a href="z_f2bf686f1e66ce65_enums_py.html#t37"><data value='HeatTracingCableType'>HeatTracingCableType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f2bf686f1e66ce65_enums_py.html#t44">core\models\enums.py</a></td>
                <td class="name left"><a href="z_f2bf686f1e66ce65_enums_py.html#t44"><data value='ElectricalComponentType'>ElectricalComponentType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f2bf686f1e66ce65_enums_py.html#t68">core\models\enums.py</a></td>
                <td class="name left"><a href="z_f2bf686f1e66ce65_enums_py.html#t68"><data value='ElectricalNodeType'>ElectricalNodeType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f2bf686f1e66ce65_enums_py.html#t80">core\models\enums.py</a></td>
                <td class="name left"><a href="z_f2bf686f1e66ce65_enums_py.html#t80"><data value='CableInstallationMethod'>CableInstallationMethod</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f2bf686f1e66ce65_enums_py.html">core\models\enums.py</a></td>
                <td class="name left"><a href="z_f2bf686f1e66ce65_enums_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>75</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="75 75">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f2bf686f1e66ce65_heat_tracing_py.html#t20">core\models\heat_tracing.py</a></td>
                <td class="name left"><a href="z_f2bf686f1e66ce65_heat_tracing_py.html#t20"><data value='Pipe'>Pipe</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f2bf686f1e66ce65_heat_tracing_py.html#t81">core\models\heat_tracing.py</a></td>
                <td class="name left"><a href="z_f2bf686f1e66ce65_heat_tracing_py.html#t81"><data value='Vessel'>Vessel</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f2bf686f1e66ce65_heat_tracing_py.html#t136">core\models\heat_tracing.py</a></td>
                <td class="name left"><a href="z_f2bf686f1e66ce65_heat_tracing_py.html#t136"><data value='ControlCircuit'>ControlCircuit</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f2bf686f1e66ce65_heat_tracing_py.html#t180">core\models\heat_tracing.py</a></td>
                <td class="name left"><a href="z_f2bf686f1e66ce65_heat_tracing_py.html#t180"><data value='HTCircuit'>HTCircuit</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f2bf686f1e66ce65_heat_tracing_py.html#t241">core\models\heat_tracing.py</a></td>
                <td class="name left"><a href="z_f2bf686f1e66ce65_heat_tracing_py.html#t241"><data value='ControlCircuitComponent'>ControlCircuitComponent</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f2bf686f1e66ce65_heat_tracing_py.html#t269">core\models\heat_tracing.py</a></td>
                <td class="name left"><a href="z_f2bf686f1e66ce65_heat_tracing_py.html#t269"><data value='HTCircuitComponent'>HTCircuitComponent</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f2bf686f1e66ce65_heat_tracing_py.html">core\models\heat_tracing.py</a></td>
                <td class="name left"><a href="z_f2bf686f1e66ce65_heat_tracing_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>123</td>
                <td>0</td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="123 123">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f2bf686f1e66ce65_project_py.html#t15">core\models\project.py</a></td>
                <td class="name left"><a href="z_f2bf686f1e66ce65_project_py.html#t15"><data value='VoltagesSchema'>VoltagesSchema</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f2bf686f1e66ce65_project_py.html#t29">core\models\project.py</a></td>
                <td class="name left"><a href="z_f2bf686f1e66ce65_project_py.html#t29"><data value='Project'>Project</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f2bf686f1e66ce65_project_py.html">core\models\project.py</a></td>
                <td class="name left"><a href="z_f2bf686f1e66ce65_project_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>78</td>
                <td>33</td>
                <td>2</td>
                <td>24</td>
                <td>0</td>
                <td class="right" data-ratio="45 102">44.12%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f2bf686f1e66ce65_switchboard_py.html#t12">core\models\switchboard.py</a></td>
                <td class="name left"><a href="z_f2bf686f1e66ce65_switchboard_py.html#t12"><data value='Switchboard'>Switchboard</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f2bf686f1e66ce65_switchboard_py.html#t43">core\models\switchboard.py</a></td>
                <td class="name left"><a href="z_f2bf686f1e66ce65_switchboard_py.html#t43"><data value='Feeder'>Feeder</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f2bf686f1e66ce65_switchboard_py.html#t71">core\models\switchboard.py</a></td>
                <td class="name left"><a href="z_f2bf686f1e66ce65_switchboard_py.html#t71"><data value='SwitchboardComponent'>SwitchboardComponent</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f2bf686f1e66ce65_switchboard_py.html#t99">core\models\switchboard.py</a></td>
                <td class="name left"><a href="z_f2bf686f1e66ce65_switchboard_py.html#t99"><data value='FeederComponent'>FeederComponent</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f2bf686f1e66ce65_switchboard_py.html">core\models\switchboard.py</a></td>
                <td class="name left"><a href="z_f2bf686f1e66ce65_switchboard_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>45</td>
                <td>0</td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="45 45">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f2bf686f1e66ce65_users_py.html#t17">core\models\users.py</a></td>
                <td class="name left"><a href="z_f2bf686f1e66ce65_users_py.html#t17"><data value='User'>User</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f2bf686f1e66ce65_users_py.html#t57">core\models\users.py</a></td>
                <td class="name left"><a href="z_f2bf686f1e66ce65_users_py.html#t57"><data value='UserPreference'>UserPreference</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f2bf686f1e66ce65_users_py.html">core\models\users.py</a></td>
                <td class="name left"><a href="z_f2bf686f1e66ce65_users_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>24</td>
                <td>0</td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="24 24">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a6617d05fb563bba___init___py.html">core\reports\__init__.py</a></td>
                <td class="name left"><a href="z_a6617d05fb563bba___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a6617d05fb563bba_data_exporter_py.html#t24">core\reports\data_exporter.py</a></td>
                <td class="name left"><a href="z_a6617d05fb563bba_data_exporter_py.html#t24"><data value='DataExporter'>DataExporter</data></a></td>
                <td>171</td>
                <td>171</td>
                <td>12</td>
                <td>78</td>
                <td>0</td>
                <td class="right" data-ratio="0 249">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a6617d05fb563bba_data_exporter_py.html">core\reports\data_exporter.py</a></td>
                <td class="name left"><a href="z_a6617d05fb563bba_data_exporter_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>25</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="25 25">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ded8937c655412b8___init___py.html">core\reports\data_preparation\__init__.py</a></td>
                <td class="name left"><a href="z_ded8937c655412b8___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ded8937c655412b8_calculation_data_processor_py.html#t19">core\reports\data_preparation\calculation_data_processor.py</a></td>
                <td class="name left"><a href="z_ded8937c655412b8_calculation_data_processor_py.html#t19"><data value='CalculationDataProcessor'>CalculationDataProcessor</data></a></td>
                <td>141</td>
                <td>141</td>
                <td>9</td>
                <td>34</td>
                <td>0</td>
                <td class="right" data-ratio="0 175">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ded8937c655412b8_calculation_data_processor_py.html">core\reports\data_preparation\calculation_data_processor.py</a></td>
                <td class="name left"><a href="z_ded8937c655412b8_calculation_data_processor_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>19</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="19 19">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ded8937c655412b8_export_data_formatter_py.html#t21">core\reports\data_preparation\export_data_formatter.py</a></td>
                <td class="name left"><a href="z_ded8937c655412b8_export_data_formatter_py.html#t21"><data value='ExportDataFormatter'>ExportDataFormatter</data></a></td>
                <td>143</td>
                <td>143</td>
                <td>6</td>
                <td>64</td>
                <td>0</td>
                <td class="right" data-ratio="0 207">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ded8937c655412b8_export_data_formatter_py.html">core\reports\data_preparation\export_data_formatter.py</a></td>
                <td class="name left"><a href="z_ded8937c655412b8_export_data_formatter_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>29</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="29 29">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ded8937c655412b8_report_data_aggregator_py.html#t19">core\reports\data_preparation\report_data_aggregator.py</a></td>
                <td class="name left"><a href="z_ded8937c655412b8_report_data_aggregator_py.html#t19"><data value='ReportDataAggregator'>ReportDataAggregator</data></a></td>
                <td>127</td>
                <td>127</td>
                <td>9</td>
                <td>34</td>
                <td>0</td>
                <td class="right" data-ratio="0 161">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ded8937c655412b8_report_data_aggregator_py.html">core\reports\data_preparation\report_data_aggregator.py</a></td>
                <td class="name left"><a href="z_ded8937c655412b8_report_data_aggregator_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>20</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="20 20">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a6617d05fb563bba_document_generator_py.html#t23">core\reports\document_generator.py</a></td>
                <td class="name left"><a href="z_a6617d05fb563bba_document_generator_py.html#t23"><data value='DocumentGenerator'>DocumentGenerator</data></a></td>
                <td>187</td>
                <td>187</td>
                <td>15</td>
                <td>68</td>
                <td>0</td>
                <td class="right" data-ratio="0 255">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a6617d05fb563bba_document_generator_py.html">core\reports\document_generator.py</a></td>
                <td class="name left"><a href="z_a6617d05fb563bba_document_generator_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>23</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="23 23">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bd29078fd467125e___init___py.html">core\reports\generators\__init__.py</a></td>
                <td class="name left"><a href="z_bd29078fd467125e___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bd29078fd467125e_excel_generator_py.html#t19">core\reports\generators\excel_generator.py</a></td>
                <td class="name left"><a href="z_bd29078fd467125e_excel_generator_py.html#t19"><data value='ExcelGenerator'>ExcelGenerator</data></a></td>
                <td>161</td>
                <td>161</td>
                <td>6</td>
                <td>60</td>
                <td>0</td>
                <td class="right" data-ratio="0 221">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bd29078fd467125e_excel_generator_py.html">core\reports\generators\excel_generator.py</a></td>
                <td class="name left"><a href="z_bd29078fd467125e_excel_generator_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>25</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="25 25">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bd29078fd467125e_html_generator_py.html#t19">core\reports\generators\html_generator.py</a></td>
                <td class="name left"><a href="z_bd29078fd467125e_html_generator_py.html#t19"><data value='HTMLGenerator'>HTMLGenerator</data></a></td>
                <td>75</td>
                <td>75</td>
                <td>7</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 85">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bd29078fd467125e_html_generator_py.html">core\reports\generators\html_generator.py</a></td>
                <td class="name left"><a href="z_bd29078fd467125e_html_generator_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>20</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="20 20">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bd29078fd467125e_pdf_generator_py.html#t20">core\reports\generators\pdf_generator.py</a></td>
                <td class="name left"><a href="z_bd29078fd467125e_pdf_generator_py.html#t20"><data value='PDFGenerator'>PDFGenerator</data></a></td>
                <td>85</td>
                <td>85</td>
                <td>5</td>
                <td>24</td>
                <td>0</td>
                <td class="right" data-ratio="0 109">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bd29078fd467125e_pdf_generator_py.html">core\reports\generators\pdf_generator.py</a></td>
                <td class="name left"><a href="z_bd29078fd467125e_pdf_generator_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>18</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="18 18">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a6617d05fb563bba_report_service_py.html#t24">core\reports\report_service.py</a></td>
                <td class="name left"><a href="z_a6617d05fb563bba_report_service_py.html#t24"><data value='ReportService'>ReportService</data></a></td>
                <td>137</td>
                <td>137</td>
                <td>17</td>
                <td>42</td>
                <td>0</td>
                <td class="right" data-ratio="0 179">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a6617d05fb563bba_report_service_py.html">core\reports\report_service.py</a></td>
                <td class="name left"><a href="z_a6617d05fb563bba_report_service_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>25</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="25 25">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_29755ff45d857bc4___init___py.html">core\reports\templates\__init__.py</a></td>
                <td class="name left"><a href="z_29755ff45d857bc4___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_29755ff45d857bc4_document_templates_py.html#t16">core\reports\templates\document_templates.py</a></td>
                <td class="name left"><a href="z_29755ff45d857bc4_document_templates_py.html#t16"><data value='DocumentTemplates'>DocumentTemplates</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_29755ff45d857bc4_document_templates_py.html">core\reports\templates\document_templates.py</a></td>
                <td class="name left"><a href="z_29755ff45d857bc4_document_templates_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>17</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="17 17">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_29755ff45d857bc4_template_manager_py.html#t20">core\reports\templates\template_manager.py</a></td>
                <td class="name left"><a href="z_29755ff45d857bc4_template_manager_py.html#t20"><data value='TemplateManager'>TemplateManager</data></a></td>
                <td>141</td>
                <td>141</td>
                <td>12</td>
                <td>48</td>
                <td>0</td>
                <td class="right" data-ratio="0 189">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_29755ff45d857bc4_template_manager_py.html">core\reports\templates\template_manager.py</a></td>
                <td class="name left"><a href="z_29755ff45d857bc4_template_manager_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>21</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="21 21">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_29755ff45d857bc4_template_renderer_py.html#t23">core\reports\templates\template_renderer.py</a></td>
                <td class="name left"><a href="z_29755ff45d857bc4_template_renderer_py.html#t23"><data value='TemplateRenderer'>TemplateRenderer</data></a></td>
                <td>120</td>
                <td>120</td>
                <td>8</td>
                <td>26</td>
                <td>0</td>
                <td class="right" data-ratio="0 146">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_29755ff45d857bc4_template_renderer_py.html">core\reports\templates\template_renderer.py</a></td>
                <td class="name left"><a href="z_29755ff45d857bc4_template_renderer_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>27</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="27 27">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ac369222432f63da_activity_log_repository_py.html#t45">core\repositories\activity_log_repository.py</a></td>
                <td class="name left"><a href="z_ac369222432f63da_activity_log_repository_py.html#t45"><data value='ActivityLogRepository'>ActivityLogRepository</data></a></td>
                <td>165</td>
                <td>165</td>
                <td>30</td>
                <td>32</td>
                <td>0</td>
                <td class="right" data-ratio="0 197">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ac369222432f63da_activity_log_repository_py.html">core\repositories\activity_log_repository.py</a></td>
                <td class="name left"><a href="z_ac369222432f63da_activity_log_repository_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>33</td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="28 33">84.85%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ac369222432f63da_base_repository_py.html#t38">core\repositories\base_repository.py</a></td>
                <td class="name left"><a href="z_ac369222432f63da_base_repository_py.html#t38"><data value='BaseRepository'>BaseRepository</data></a></td>
                <td>78</td>
                <td>78</td>
                <td>0</td>
                <td>32</td>
                <td>0</td>
                <td class="right" data-ratio="0 110">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ac369222432f63da_base_repository_py.html">core\repositories\base_repository.py</a></td>
                <td class="name left"><a href="z_ac369222432f63da_base_repository_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>25</td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="21 25">84.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ac369222432f63da_component_repository_py.html#t21">core\repositories\component_repository.py</a></td>
                <td class="name left"><a href="z_ac369222432f63da_component_repository_py.html#t21"><data value='ComponentRepository'>ComponentRepository</data></a></td>
                <td>51</td>
                <td>51</td>
                <td>12</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 57">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ac369222432f63da_component_repository_py.html#t257">core\repositories\component_repository.py</a></td>
                <td class="name left"><a href="z_ac369222432f63da_component_repository_py.html#t257"><data value='ComponentCategoryRepository'>ComponentCategoryRepository</data></a></td>
                <td>26</td>
                <td>26</td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 26">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ac369222432f63da_component_repository_py.html">core\repositories\component_repository.py</a></td>
                <td class="name left"><a href="z_ac369222432f63da_component_repository_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>20</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="20 20">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ac369222432f63da_dependencies_py.html">core\repositories\dependencies.py</a></td>
                <td class="name left"><a href="z_ac369222432f63da_dependencies_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>27</td>
                <td>16</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="11 27">40.74%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ac369222432f63da_document_repository_py.html#t30">core\repositories\document_repository.py</a></td>
                <td class="name left"><a href="z_ac369222432f63da_document_repository_py.html#t30"><data value='ImportedDataRevisionRepository'>ImportedDataRevisionRepository</data></a></td>
                <td>59</td>
                <td>59</td>
                <td>15</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 61">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ac369222432f63da_document_repository_py.html#t314">core\repositories\document_repository.py</a></td>
                <td class="name left"><a href="z_ac369222432f63da_document_repository_py.html#t314"><data value='ExportedDocumentRepository'>ExportedDocumentRepository</data></a></td>
                <td>58</td>
                <td>58</td>
                <td>15</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 60">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ac369222432f63da_document_repository_py.html#t595">core\repositories\document_repository.py</a></td>
                <td class="name left"><a href="z_ac369222432f63da_document_repository_py.html#t595"><data value='CalculationStandardRepository'>CalculationStandardRepository</data></a></td>
                <td>44</td>
                <td>44</td>
                <td>13</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 44">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ac369222432f63da_document_repository_py.html">core\repositories\document_repository.py</a></td>
                <td class="name left"><a href="z_ac369222432f63da_document_repository_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>32</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="32 32">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ac369222432f63da_electrical_repository_py.html#t47">core\repositories\electrical_repository.py</a></td>
                <td class="name left"><a href="z_ac369222432f63da_electrical_repository_py.html#t47"><data value='ElectricalNodeRepository'>ElectricalNodeRepository</data></a></td>
                <td>65</td>
                <td>65</td>
                <td>17</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 65">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ac369222432f63da_electrical_repository_py.html#t376">core\repositories\electrical_repository.py</a></td>
                <td class="name left"><a href="z_ac369222432f63da_electrical_repository_py.html#t376"><data value='CableRouteRepository'>CableRouteRepository</data></a></td>
                <td>72</td>
                <td>72</td>
                <td>17</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 76">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ac369222432f63da_electrical_repository_py.html#t714">core\repositories\electrical_repository.py</a></td>
                <td class="name left"><a href="z_ac369222432f63da_electrical_repository_py.html#t714"><data value='CableSegmentRepository'>CableSegmentRepository</data></a></td>
                <td>33</td>
                <td>33</td>
                <td>9</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 33">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ac369222432f63da_electrical_repository_py.html#t909">core\repositories\electrical_repository.py</a></td>
                <td class="name left"><a href="z_ac369222432f63da_electrical_repository_py.html#t909"><data value='LoadCalculationRepository'>LoadCalculationRepository</data></a></td>
                <td>34</td>
                <td>34</td>
                <td>9</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 34">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ac369222432f63da_electrical_repository_py.html#t1114">core\repositories\electrical_repository.py</a></td>
                <td class="name left"><a href="z_ac369222432f63da_electrical_repository_py.html#t1114"><data value='VoltageDropCalculationRepository'>VoltageDropCalculationRepository</data></a></td>
                <td>51</td>
                <td>51</td>
                <td>13</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 51">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ac369222432f63da_electrical_repository_py.html">core\repositories\electrical_repository.py</a></td>
                <td class="name left"><a href="z_ac369222432f63da_electrical_repository_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>54</td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="47 54">87.04%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ac369222432f63da_heat_tracing_repository_py.html#t45">core\repositories\heat_tracing_repository.py</a></td>
                <td class="name left"><a href="z_ac369222432f63da_heat_tracing_repository_py.html#t45"><data value='PipeRepository'>PipeRepository</data></a></td>
                <td>53</td>
                <td>53</td>
                <td>15</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 53">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ac369222432f63da_heat_tracing_repository_py.html#t305">core\repositories\heat_tracing_repository.py</a></td>
                <td class="name left"><a href="z_ac369222432f63da_heat_tracing_repository_py.html#t305"><data value='VesselRepository'>VesselRepository</data></a></td>
                <td>45</td>
                <td>45</td>
                <td>13</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 45">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ac369222432f63da_heat_tracing_repository_py.html#t534">core\repositories\heat_tracing_repository.py</a></td>
                <td class="name left"><a href="z_ac369222432f63da_heat_tracing_repository_py.html#t534"><data value='HTCircuitRepository'>HTCircuitRepository</data></a></td>
                <td>55</td>
                <td>55</td>
                <td>16</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 55">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ac369222432f63da_heat_tracing_repository_py.html#t798">core\repositories\heat_tracing_repository.py</a></td>
                <td class="name left"><a href="z_ac369222432f63da_heat_tracing_repository_py.html#t798"><data value='ControlCircuitRepository'>ControlCircuitRepository</data></a></td>
                <td>36</td>
                <td>36</td>
                <td>9</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 38">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ac369222432f63da_heat_tracing_repository_py.html#t996">core\repositories\heat_tracing_repository.py</a></td>
                <td class="name left"><a href="z_ac369222432f63da_heat_tracing_repository_py.html#t996"><data value='HeatTracingRepository'>HeatTracingRepository</data></a></td>
                <td>26</td>
                <td>26</td>
                <td>5</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 30">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ac369222432f63da_heat_tracing_repository_py.html">core\repositories\heat_tracing_repository.py</a></td>
                <td class="name left"><a href="z_ac369222432f63da_heat_tracing_repository_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>49</td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="42 49">85.71%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ac369222432f63da_project_repository_py.html#t24">core\repositories\project_repository.py</a></td>
                <td class="name left"><a href="z_ac369222432f63da_project_repository_py.html#t24"><data value='ProjectRepository'>ProjectRepository</data></a></td>
                <td>79</td>
                <td>79</td>
                <td>22</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 79">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ac369222432f63da_project_repository_py.html">core\repositories\project_repository.py</a></td>
                <td class="name left"><a href="z_ac369222432f63da_project_repository_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>19</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="19 19">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ac369222432f63da_switchboard_repository_py.html#t45">core\repositories\switchboard_repository.py</a></td>
                <td class="name left"><a href="z_ac369222432f63da_switchboard_repository_py.html#t45"><data value='SwitchboardRepository'>SwitchboardRepository</data></a></td>
                <td>57</td>
                <td>57</td>
                <td>15</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 57">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ac369222432f63da_switchboard_repository_py.html#t345">core\repositories\switchboard_repository.py</a></td>
                <td class="name left"><a href="z_ac369222432f63da_switchboard_repository_py.html#t345"><data value='FeederRepository'>FeederRepository</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ac369222432f63da_switchboard_repository_py.html#t446">core\repositories\switchboard_repository.py</a></td>
                <td class="name left"><a href="z_ac369222432f63da_switchboard_repository_py.html#t446"><data value='SwitchboardComponentRepository'>SwitchboardComponentRepository</data></a></td>
                <td>25</td>
                <td>25</td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 25">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ac369222432f63da_switchboard_repository_py.html#t582">core\repositories\switchboard_repository.py</a></td>
                <td class="name left"><a href="z_ac369222432f63da_switchboard_repository_py.html#t582"><data value='FeederComponentRepository'>FeederComponentRepository</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ac369222432f63da_switchboard_repository_py.html">core\repositories\switchboard_repository.py</a></td>
                <td class="name left"><a href="z_ac369222432f63da_switchboard_repository_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>37</td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="30 37">81.08%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ac369222432f63da_user_repository_py.html#t35">core\repositories\user_repository.py</a></td>
                <td class="name left"><a href="z_ac369222432f63da_user_repository_py.html#t35"><data value='UserRepository'>UserRepository</data></a></td>
                <td>54</td>
                <td>54</td>
                <td>51</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 54">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ac369222432f63da_user_repository_py.html#t305">core\repositories\user_repository.py</a></td>
                <td class="name left"><a href="z_ac369222432f63da_user_repository_py.html#t305"><data value='UserPreferenceRepository'>UserPreferenceRepository</data></a></td>
                <td>37</td>
                <td>37</td>
                <td>10</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 39">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ac369222432f63da_user_repository_py.html">core\repositories\user_repository.py</a></td>
                <td class="name left"><a href="z_ac369222432f63da_user_repository_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>29</td>
                <td>7</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="22 29">75.86%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_activity_log_schemas_py.html#t33">core\schemas\activity_log_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_activity_log_schemas_py.html#t33"><data value='EventTypeEnum'>EventTypeEnum</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_activity_log_schemas_py.html#t71">core\schemas\activity_log_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_activity_log_schemas_py.html#t71"><data value='EntityTypeEnum'>EntityTypeEnum</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_activity_log_schemas_py.html#t88">core\schemas\activity_log_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_activity_log_schemas_py.html#t88"><data value='EventCategoryEnum'>EventCategoryEnum</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_activity_log_schemas_py.html#t106">core\schemas\activity_log_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_activity_log_schemas_py.html#t106"><data value='ActivityLogBaseSchema'>ActivityLogBaseSchema</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>1</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_activity_log_schemas_py.html#t148">core\schemas\activity_log_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_activity_log_schemas_py.html#t148"><data value='ActivityLogCreateSchema'>ActivityLogCreateSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_activity_log_schemas_py.html#t168">core\schemas\activity_log_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_activity_log_schemas_py.html#t168"><data value='ActivityLogUpdateSchema'>ActivityLogUpdateSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_activity_log_schemas_py.html#t181">core\schemas\activity_log_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_activity_log_schemas_py.html#t181"><data value='ActivityLogReadSchema'>ActivityLogReadSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_activity_log_schemas_py.html#t208">core\schemas\activity_log_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_activity_log_schemas_py.html#t208"><data value='ActivityLogSummarySchema'>ActivityLogSummarySchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_activity_log_schemas_py.html#t226">core\schemas\activity_log_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_activity_log_schemas_py.html#t226"><data value='ActivityLogFilterSchema'>ActivityLogFilterSchema</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_activity_log_schemas_py.html#t282">core\schemas\activity_log_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_activity_log_schemas_py.html#t282"><data value='AuditReportRequestSchema'>AuditReportRequestSchema</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_activity_log_schemas_py.html#t356">core\schemas\activity_log_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_activity_log_schemas_py.html#t356"><data value='AuditReportSummarySchema'>AuditReportSummarySchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_activity_log_schemas_py.html#t386">core\schemas\activity_log_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_activity_log_schemas_py.html#t386"><data value='AuditReportResponseSchema'>AuditReportResponseSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_activity_log_schemas_py.html#t414">core\schemas\activity_log_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_activity_log_schemas_py.html#t414"><data value='SecurityEventSchema'>SecurityEventSchema</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_activity_log_schemas_py.html#t467">core\schemas\activity_log_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_activity_log_schemas_py.html#t467"><data value='ActivityLogPaginatedResponseSchema'>ActivityLogPaginatedResponseSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_activity_log_schemas_py.html#t501">core\schemas\activity_log_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_activity_log_schemas_py.html#t501"><data value='EventCategoryMappingSchema'>EventCategoryMappingSchema</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_activity_log_schemas_py.html">core\schemas\activity_log_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_activity_log_schemas_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>146</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="146 146">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_base_py.html#t14">core\schemas\base.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_base_py.html#t14"><data value='BaseSchema'>BaseSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_base_py.html#t24">core\schemas\base.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_base_py.html#t24"><data value='BaseReadSchema'>BaseReadSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_base_py.html#t32">core\schemas\base.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_base_py.html#t32"><data value='BaseSoftDeleteSchema'>BaseSoftDeleteSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_base_py.html#t40">core\schemas\base.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_base_py.html#t40"><data value='BaseNamedSchema'>BaseNamedSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_base_py.html#t47">core\schemas\base.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_base_py.html#t47"><data value='PaginationSchema'>PaginationSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_base_py.html#t54">core\schemas\base.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_base_py.html#t54"><data value='PaginatedResponseSchema'>PaginatedResponseSchema</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_base_py.html#t77">core\schemas\base.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_base_py.html#t77"><data value='TimestampMixin'>TimestampMixin</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_base_py.html#t84">core\schemas\base.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_base_py.html#t84"><data value='SoftDeleteMixin'>SoftDeleteMixin</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_base_py.html#t92">core\schemas\base.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_base_py.html#t92"><data value='NamedEntityMixin'>NamedEntityMixin</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_base_py.html">core\schemas\base.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_base_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>39</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="39 39">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_component_schemas_py.html#t17">core\schemas\component_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_component_schemas_py.html#t17"><data value='ComponentCategoryBaseSchema'>ComponentCategoryBaseSchema</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_component_schemas_py.html#t41">core\schemas\component_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_component_schemas_py.html#t41"><data value='ComponentCategoryCreateSchema'>ComponentCategoryCreateSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_component_schemas_py.html#t59">core\schemas\component_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_component_schemas_py.html#t59"><data value='ComponentCategoryUpdateSchema'>ComponentCategoryUpdateSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_component_schemas_py.html#t83">core\schemas\component_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_component_schemas_py.html#t83"><data value='ComponentCategoryReadSchema'>ComponentCategoryReadSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_component_schemas_py.html#t106">core\schemas\component_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_component_schemas_py.html#t106"><data value='ComponentBaseSchema'>ComponentBaseSchema</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_component_schemas_py.html#t153">core\schemas\component_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_component_schemas_py.html#t153"><data value='ComponentCreateSchema'>ComponentCreateSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_component_schemas_py.html#t172">core\schemas\component_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_component_schemas_py.html#t172"><data value='ComponentUpdateSchema'>ComponentUpdateSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_component_schemas_py.html#t210">core\schemas\component_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_component_schemas_py.html#t210"><data value='ComponentReadSchema'>ComponentReadSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_component_schemas_py.html#t236">core\schemas\component_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_component_schemas_py.html#t236"><data value='ComponentSummarySchema'>ComponentSummarySchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_component_schemas_py.html#t248">core\schemas\component_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_component_schemas_py.html#t248"><data value='ComponentListResponseSchema'>ComponentListResponseSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_component_schemas_py.html#t276">core\schemas\component_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_component_schemas_py.html#t276"><data value='ComponentCategoryListResponseSchema'>ComponentCategoryListResponseSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_component_schemas_py.html">core\schemas\component_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_component_schemas_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>63</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="63 63">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_document_schemas_py.html#t23">core\schemas\document_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_document_schemas_py.html#t23"><data value='ImportType'>ImportType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_document_schemas_py.html#t33">core\schemas\document_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_document_schemas_py.html#t33"><data value='DocumentType'>DocumentType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_document_schemas_py.html#t44">core\schemas\document_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_document_schemas_py.html#t44"><data value='FileFormat'>FileFormat</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_document_schemas_py.html#t58">core\schemas\document_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_document_schemas_py.html#t58"><data value='ImportedDataRevisionBaseSchema'>ImportedDataRevisionBaseSchema</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 23">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_document_schemas_py.html#t107">core\schemas\document_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_document_schemas_py.html#t107"><data value='ImportedDataRevisionCreateSchema'>ImportedDataRevisionCreateSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_document_schemas_py.html#t131">core\schemas\document_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_document_schemas_py.html#t131"><data value='ImportedDataRevisionUpdateSchema'>ImportedDataRevisionUpdateSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_document_schemas_py.html#t147">core\schemas\document_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_document_schemas_py.html#t147"><data value='ImportedDataRevisionReadSchema'>ImportedDataRevisionReadSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_document_schemas_py.html#t178">core\schemas\document_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_document_schemas_py.html#t178"><data value='ImportedDataRevisionSummarySchema'>ImportedDataRevisionSummarySchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_document_schemas_py.html#t198">core\schemas\document_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_document_schemas_py.html#t198"><data value='ExportedDocumentBaseSchema'>ExportedDocumentBaseSchema</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_document_schemas_py.html#t238">core\schemas\document_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_document_schemas_py.html#t238"><data value='ExportedDocumentCreateSchema'>ExportedDocumentCreateSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_document_schemas_py.html#t264">core\schemas\document_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_document_schemas_py.html#t264"><data value='ExportedDocumentUpdateSchema'>ExportedDocumentUpdateSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_document_schemas_py.html#t278">core\schemas\document_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_document_schemas_py.html#t278"><data value='ExportedDocumentReadSchema'>ExportedDocumentReadSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_document_schemas_py.html#t311">core\schemas\document_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_document_schemas_py.html#t311"><data value='ExportedDocumentSummarySchema'>ExportedDocumentSummarySchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_document_schemas_py.html#t331">core\schemas\document_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_document_schemas_py.html#t331"><data value='CalculationStandardBaseSchema'>CalculationStandardBaseSchema</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 28">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_document_schemas_py.html#t385">core\schemas\document_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_document_schemas_py.html#t385"><data value='CalculationStandardCreateSchema'>CalculationStandardCreateSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_document_schemas_py.html#t400">core\schemas\document_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_document_schemas_py.html#t400"><data value='CalculationStandardUpdateSchema'>CalculationStandardUpdateSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_document_schemas_py.html#t422">core\schemas\document_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_document_schemas_py.html#t422"><data value='CalculationStandardReadSchema'>CalculationStandardReadSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_document_schemas_py.html#t446">core\schemas\document_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_document_schemas_py.html#t446"><data value='CalculationStandardSummarySchema'>CalculationStandardSummarySchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_document_schemas_py.html#t463">core\schemas\document_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_document_schemas_py.html#t463"><data value='FileUploadSchema'>FileUploadSchema</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_document_schemas_py.html#t498">core\schemas\document_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_document_schemas_py.html#t498"><data value='FileDownloadSchema'>FileDownloadSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_document_schemas_py.html#t507">core\schemas\document_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_document_schemas_py.html#t507"><data value='DocumentGenerationRequestSchema'>DocumentGenerationRequestSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_document_schemas_py.html#t543">core\schemas\document_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_document_schemas_py.html#t543"><data value='ImportedDataRevisionListResponseSchema'>ImportedDataRevisionListResponseSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_document_schemas_py.html#t572">core\schemas\document_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_document_schemas_py.html#t572"><data value='ExportedDocumentListResponseSchema'>ExportedDocumentListResponseSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_document_schemas_py.html#t601">core\schemas\document_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_document_schemas_py.html#t601"><data value='CalculationStandardListResponseSchema'>CalculationStandardListResponseSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_document_schemas_py.html">core\schemas\document_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_document_schemas_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>147</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="147 147">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_electrical_schemas_py.html#t31">core\schemas\electrical_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_electrical_schemas_py.html#t31"><data value='CableSizingCalculationInputSchema'>CableSizingCalculationInputSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_electrical_schemas_py.html#t73">core\schemas\electrical_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_electrical_schemas_py.html#t73"><data value='CableSizingCalculationResultSchema'>CableSizingCalculationResultSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_electrical_schemas_py.html#t92">core\schemas\electrical_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_electrical_schemas_py.html#t92"><data value='VoltageDropCalculationInputSchema'>VoltageDropCalculationInputSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_electrical_schemas_py.html#t134">core\schemas\electrical_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_electrical_schemas_py.html#t134"><data value='VoltageDropCalculationResultSchema'>VoltageDropCalculationResultSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_electrical_schemas_py.html#t155">core\schemas\electrical_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_electrical_schemas_py.html#t155"><data value='ElectricalStandardsValidationInputSchema'>ElectricalStandardsValidationInputSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_electrical_schemas_py.html#t170">core\schemas\electrical_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_electrical_schemas_py.html#t170"><data value='ElectricalStandardsValidationResultSchema'>ElectricalStandardsValidationResultSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_electrical_schemas_py.html#t195">core\schemas\electrical_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_electrical_schemas_py.html#t195"><data value='ElectricalNodeBaseSchema'>ElectricalNodeBaseSchema</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_electrical_schemas_py.html#t219">core\schemas\electrical_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_electrical_schemas_py.html#t219"><data value='ElectricalNodeCreateSchema'>ElectricalNodeCreateSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_electrical_schemas_py.html#t251">core\schemas\electrical_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_electrical_schemas_py.html#t251"><data value='ElectricalNodeUpdateSchema'>ElectricalNodeUpdateSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_electrical_schemas_py.html#t287">core\schemas\electrical_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_electrical_schemas_py.html#t287"><data value='ElectricalNodeReadSchema'>ElectricalNodeReadSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_electrical_schemas_py.html#t326">core\schemas\electrical_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_electrical_schemas_py.html#t326"><data value='ElectricalNodeSummarySchema'>ElectricalNodeSummarySchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_electrical_schemas_py.html#t346">core\schemas\electrical_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_electrical_schemas_py.html#t346"><data value='CableRouteBaseSchema'>CableRouteBaseSchema</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_electrical_schemas_py.html#t386">core\schemas\electrical_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_electrical_schemas_py.html#t386"><data value='CableRouteCreateSchema'>CableRouteCreateSchema</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_electrical_schemas_py.html#t419">core\schemas\electrical_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_electrical_schemas_py.html#t419"><data value='CableRouteUpdateSchema'>CableRouteUpdateSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_electrical_schemas_py.html#t448">core\schemas\electrical_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_electrical_schemas_py.html#t448"><data value='CableRouteReadSchema'>CableRouteReadSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_electrical_schemas_py.html#t487">core\schemas\electrical_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_electrical_schemas_py.html#t487"><data value='CableRouteSummarySchema'>CableRouteSummarySchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_electrical_schemas_py.html#t511">core\schemas\electrical_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_electrical_schemas_py.html#t511"><data value='CableSegmentBaseSchema'>CableSegmentBaseSchema</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_electrical_schemas_py.html#t549">core\schemas\electrical_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_electrical_schemas_py.html#t549"><data value='CableSegmentCreateSchema'>CableSegmentCreateSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_electrical_schemas_py.html#t578">core\schemas\electrical_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_electrical_schemas_py.html#t578"><data value='CableSegmentUpdateSchema'>CableSegmentUpdateSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_electrical_schemas_py.html#t616">core\schemas\electrical_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_electrical_schemas_py.html#t616"><data value='CableSegmentReadSchema'>CableSegmentReadSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_electrical_schemas_py.html#t663">core\schemas\electrical_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_electrical_schemas_py.html#t663"><data value='CableSegmentSummarySchema'>CableSegmentSummarySchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_electrical_schemas_py.html#t686">core\schemas\electrical_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_electrical_schemas_py.html#t686"><data value='LoadCalculationBaseSchema'>LoadCalculationBaseSchema</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_electrical_schemas_py.html#t746">core\schemas\electrical_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_electrical_schemas_py.html#t746"><data value='LoadCalculationCreateSchema'>LoadCalculationCreateSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_electrical_schemas_py.html#t781">core\schemas\electrical_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_electrical_schemas_py.html#t781"><data value='LoadCalculationUpdateSchema'>LoadCalculationUpdateSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_electrical_schemas_py.html#t829">core\schemas\electrical_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_electrical_schemas_py.html#t829"><data value='LoadCalculationReadSchema'>LoadCalculationReadSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_electrical_schemas_py.html#t878">core\schemas\electrical_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_electrical_schemas_py.html#t878"><data value='LoadCalculationSummarySchema'>LoadCalculationSummarySchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_electrical_schemas_py.html#t898">core\schemas\electrical_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_electrical_schemas_py.html#t898"><data value='VoltageDropCalculationBaseSchema'>VoltageDropCalculationBaseSchema</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_electrical_schemas_py.html#t942">core\schemas\electrical_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_electrical_schemas_py.html#t942"><data value='VoltageDropCalculationCreateSchema'>VoltageDropCalculationCreateSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_electrical_schemas_py.html#t972">core\schemas\electrical_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_electrical_schemas_py.html#t972"><data value='VoltageDropCalculationUpdateSchema'>VoltageDropCalculationUpdateSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_electrical_schemas_py.html#t1023">core\schemas\electrical_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_electrical_schemas_py.html#t1023"><data value='VoltageDropCalculationReadSchema'>VoltageDropCalculationReadSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_electrical_schemas_py.html#t1078">core\schemas\electrical_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_electrical_schemas_py.html#t1078"><data value='VoltageDropCalculationSummarySchema'>VoltageDropCalculationSummarySchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_electrical_schemas_py.html#t1102">core\schemas\electrical_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_electrical_schemas_py.html#t1102"><data value='ElectricalDesignInputSchema'>ElectricalDesignInputSchema</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_electrical_schemas_py.html#t1146">core\schemas\electrical_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_electrical_schemas_py.html#t1146"><data value='ElectricalDesignResultSchema'>ElectricalDesignResultSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_electrical_schemas_py.html#t1183">core\schemas\electrical_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_electrical_schemas_py.html#t1183"><data value='ElectricalNodeListResponseSchema'>ElectricalNodeListResponseSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_electrical_schemas_py.html#t1212">core\schemas\electrical_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_electrical_schemas_py.html#t1212"><data value='CableRouteListResponseSchema'>CableRouteListResponseSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_electrical_schemas_py.html#t1243">core\schemas\electrical_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_electrical_schemas_py.html#t1243"><data value='CableSegmentListResponseSchema'>CableSegmentListResponseSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_electrical_schemas_py.html#t1273">core\schemas\electrical_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_electrical_schemas_py.html#t1273"><data value='LoadCalculationListResponseSchema'>LoadCalculationListResponseSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_electrical_schemas_py.html#t1302">core\schemas\electrical_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_electrical_schemas_py.html#t1302"><data value='VoltageDropCalculationListResponseSchema'>VoltageDropCalculationListResponseSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_electrical_schemas_py.html">core\schemas\electrical_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_electrical_schemas_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>379</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="379 379">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_error_py.html#t5">core\schemas\error.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_error_py.html#t5"><data value='ErrorResponseSchema'>ErrorResponseSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_error_py.html#t12">core\schemas\error.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_error_py.html#t12"><data value='Config'>ErrorResponseSchema.Config</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_error_py.html#t24">core\schemas\error.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_error_py.html#t24"><data value='ValidationErrorDetail'>ValidationErrorDetail</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_error_py.html#t29">core\schemas\error.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_error_py.html#t29"><data value='ValidationErrorsResponseSchema'>ValidationErrorsResponseSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_error_py.html#t32">core\schemas\error.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_error_py.html#t32"><data value='Config'>ValidationErrorsResponseSchema.Config</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_error_py.html">core\schemas\error.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_error_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>18</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="18 18">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_heat_tracing_schemas_py.html#t32">core\schemas\heat_tracing_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_heat_tracing_schemas_py.html#t32"><data value='HeatLossCalculationInputSchema'>HeatLossCalculationInputSchema</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_heat_tracing_schemas_py.html#t64">core\schemas\heat_tracing_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_heat_tracing_schemas_py.html#t64"><data value='HeatLossCalculationResultSchema'>HeatLossCalculationResultSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_heat_tracing_schemas_py.html#t79">core\schemas\heat_tracing_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_heat_tracing_schemas_py.html#t79"><data value='StandardsValidationInputSchema'>StandardsValidationInputSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_heat_tracing_schemas_py.html#t90">core\schemas\heat_tracing_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_heat_tracing_schemas_py.html#t90"><data value='StandardsValidationResultSchema'>StandardsValidationResultSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_heat_tracing_schemas_py.html#t112">core\schemas\heat_tracing_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_heat_tracing_schemas_py.html#t112"><data value='PipeBaseSchema'>PipeBaseSchema</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_heat_tracing_schemas_py.html#t172">core\schemas\heat_tracing_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_heat_tracing_schemas_py.html#t172"><data value='PipeCreateSchema'>PipeCreateSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_heat_tracing_schemas_py.html#t209">core\schemas\heat_tracing_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_heat_tracing_schemas_py.html#t209"><data value='PipeUpdateSchema'>PipeUpdateSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_heat_tracing_schemas_py.html#t258">core\schemas\heat_tracing_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_heat_tracing_schemas_py.html#t258"><data value='PipeReadSchema'>PipeReadSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_heat_tracing_schemas_py.html#t304">core\schemas\heat_tracing_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_heat_tracing_schemas_py.html#t304"><data value='PipeSummarySchema'>PipeSummarySchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_heat_tracing_schemas_py.html#t324">core\schemas\heat_tracing_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_heat_tracing_schemas_py.html#t324"><data value='VesselBaseSchema'>VesselBaseSchema</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 22">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_heat_tracing_schemas_py.html#t381">core\schemas\heat_tracing_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_heat_tracing_schemas_py.html#t381"><data value='VesselCreateSchema'>VesselCreateSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_heat_tracing_schemas_py.html#t412">core\schemas\heat_tracing_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_heat_tracing_schemas_py.html#t412"><data value='VesselUpdateSchema'>VesselUpdateSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_heat_tracing_schemas_py.html#t454">core\schemas\heat_tracing_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_heat_tracing_schemas_py.html#t454"><data value='VesselReadSchema'>VesselReadSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_heat_tracing_schemas_py.html#t497">core\schemas\heat_tracing_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_heat_tracing_schemas_py.html#t497"><data value='VesselSummarySchema'>VesselSummarySchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_heat_tracing_schemas_py.html#t517">core\schemas\heat_tracing_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_heat_tracing_schemas_py.html#t517"><data value='ControlCircuitBaseSchema'>ControlCircuitBaseSchema</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_heat_tracing_schemas_py.html#t557">core\schemas\heat_tracing_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_heat_tracing_schemas_py.html#t557"><data value='ControlCircuitCreateSchema'>ControlCircuitCreateSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_heat_tracing_schemas_py.html#t578">core\schemas\heat_tracing_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_heat_tracing_schemas_py.html#t578"><data value='ControlCircuitUpdateSchema'>ControlCircuitUpdateSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_heat_tracing_schemas_py.html#t607">core\schemas\heat_tracing_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_heat_tracing_schemas_py.html#t607"><data value='ControlCircuitReadSchema'>ControlCircuitReadSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_heat_tracing_schemas_py.html#t635">core\schemas\heat_tracing_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_heat_tracing_schemas_py.html#t635"><data value='ControlCircuitSummarySchema'>ControlCircuitSummarySchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_heat_tracing_schemas_py.html#t652">core\schemas\heat_tracing_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_heat_tracing_schemas_py.html#t652"><data value='HTCircuitBaseSchema'>HTCircuitBaseSchema</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_heat_tracing_schemas_py.html#t689">core\schemas\heat_tracing_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_heat_tracing_schemas_py.html#t689"><data value='HTCircuitCreateSchema'>HTCircuitCreateSchema</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_heat_tracing_schemas_py.html#t735">core\schemas\heat_tracing_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_heat_tracing_schemas_py.html#t735"><data value='HTCircuitUpdateSchema'>HTCircuitUpdateSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_heat_tracing_schemas_py.html#t776">core\schemas\heat_tracing_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_heat_tracing_schemas_py.html#t776"><data value='HTCircuitReadSchema'>HTCircuitReadSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_heat_tracing_schemas_py.html#t827">core\schemas\heat_tracing_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_heat_tracing_schemas_py.html#t827"><data value='HTCircuitSummarySchema'>HTCircuitSummarySchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_heat_tracing_schemas_py.html#t850">core\schemas\heat_tracing_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_heat_tracing_schemas_py.html#t850"><data value='HeatTracingDesignInputSchema'>HeatTracingDesignInputSchema</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_heat_tracing_schemas_py.html#t883">core\schemas\heat_tracing_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_heat_tracing_schemas_py.html#t883"><data value='HeatTracingDesignResultSchema'>HeatTracingDesignResultSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_heat_tracing_schemas_py.html#t914">core\schemas\heat_tracing_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_heat_tracing_schemas_py.html#t914"><data value='PipeListResponseSchema'>PipeListResponseSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_heat_tracing_schemas_py.html#t941">core\schemas\heat_tracing_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_heat_tracing_schemas_py.html#t941"><data value='VesselListResponseSchema'>VesselListResponseSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_heat_tracing_schemas_py.html#t968">core\schemas\heat_tracing_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_heat_tracing_schemas_py.html#t968"><data value='HTCircuitListResponseSchema'>HTCircuitListResponseSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_heat_tracing_schemas_py.html#t998">core\schemas\heat_tracing_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_heat_tracing_schemas_py.html#t998"><data value='ControlCircuitListResponseSchema'>ControlCircuitListResponseSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_heat_tracing_schemas_py.html">core\schemas\heat_tracing_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_heat_tracing_schemas_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>283</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="283 283">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_project_schemas_py.html#t21">core\schemas\project_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_project_schemas_py.html#t21"><data value='AvailableVoltagesSchema'>AvailableVoltagesSchema</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_project_schemas_py.html#t38">core\schemas\project_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_project_schemas_py.html#t38"><data value='ProjectBaseSchema'>ProjectBaseSchema</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_project_schemas_py.html#t84">core\schemas\project_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_project_schemas_py.html#t84"><data value='ProjectEnvironmentalSchema'>ProjectEnvironmentalSchema</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_project_schemas_py.html#t116">core\schemas\project_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_project_schemas_py.html#t116"><data value='ProjectDefaultsSchema'>ProjectDefaultsSchema</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>1</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 20">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_project_schemas_py.html#t164">core\schemas\project_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_project_schemas_py.html#t164"><data value='ProjectCreateSchema'>ProjectCreateSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_project_schemas_py.html#t190">core\schemas\project_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_project_schemas_py.html#t190"><data value='ProjectUpdateSchema'>ProjectUpdateSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_project_schemas_py.html#t258">core\schemas\project_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_project_schemas_py.html#t258"><data value='ProjectReadSchema'>ProjectReadSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_project_schemas_py.html#t300">core\schemas\project_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_project_schemas_py.html#t300"><data value='ProjectSummarySchema'>ProjectSummarySchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_project_schemas_py.html#t314">core\schemas\project_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_project_schemas_py.html#t314"><data value='ProjectListResponseSchema'>ProjectListResponseSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_project_schemas_py.html">core\schemas\project_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_project_schemas_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>82</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="82 82">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_switchboard_schemas_py.html#t26">core\schemas\switchboard_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_switchboard_schemas_py.html#t26"><data value='SwitchboardBaseSchema'>SwitchboardBaseSchema</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_switchboard_schemas_py.html#t62">core\schemas\switchboard_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_switchboard_schemas_py.html#t62"><data value='SwitchboardCreateSchema'>SwitchboardCreateSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_switchboard_schemas_py.html#t81">core\schemas\switchboard_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_switchboard_schemas_py.html#t81"><data value='SwitchboardUpdateSchema'>SwitchboardUpdateSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_switchboard_schemas_py.html#t107">core\schemas\switchboard_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_switchboard_schemas_py.html#t107"><data value='SwitchboardReadSchema'>SwitchboardReadSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_switchboard_schemas_py.html#t133">core\schemas\switchboard_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_switchboard_schemas_py.html#t133"><data value='SwitchboardSummarySchema'>SwitchboardSummarySchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_switchboard_schemas_py.html#t152">core\schemas\switchboard_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_switchboard_schemas_py.html#t152"><data value='FeederBaseSchema'>FeederBaseSchema</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_switchboard_schemas_py.html#t168">core\schemas\switchboard_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_switchboard_schemas_py.html#t168"><data value='FeederCreateSchema'>FeederCreateSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_switchboard_schemas_py.html#t183">core\schemas\switchboard_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_switchboard_schemas_py.html#t183"><data value='FeederUpdateSchema'>FeederUpdateSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_switchboard_schemas_py.html#t194">core\schemas\switchboard_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_switchboard_schemas_py.html#t194"><data value='FeederReadSchema'>FeederReadSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_switchboard_schemas_py.html#t216">core\schemas\switchboard_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_switchboard_schemas_py.html#t216"><data value='FeederSummarySchema'>FeederSummarySchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_switchboard_schemas_py.html#t231">core\schemas\switchboard_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_switchboard_schemas_py.html#t231"><data value='SwitchboardComponentBaseSchema'>SwitchboardComponentBaseSchema</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_switchboard_schemas_py.html#t248">core\schemas\switchboard_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_switchboard_schemas_py.html#t248"><data value='SwitchboardComponentCreateSchema'>SwitchboardComponentCreateSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_switchboard_schemas_py.html#t266">core\schemas\switchboard_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_switchboard_schemas_py.html#t266"><data value='SwitchboardComponentUpdateSchema'>SwitchboardComponentUpdateSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_switchboard_schemas_py.html#t282">core\schemas\switchboard_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_switchboard_schemas_py.html#t282"><data value='SwitchboardComponentReadSchema'>SwitchboardComponentReadSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_switchboard_schemas_py.html#t303">core\schemas\switchboard_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_switchboard_schemas_py.html#t303"><data value='SwitchboardComponentSummarySchema'>SwitchboardComponentSummarySchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_switchboard_schemas_py.html#t320">core\schemas\switchboard_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_switchboard_schemas_py.html#t320"><data value='FeederComponentBaseSchema'>FeederComponentBaseSchema</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_switchboard_schemas_py.html#t337">core\schemas\switchboard_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_switchboard_schemas_py.html#t337"><data value='FeederComponentCreateSchema'>FeederComponentCreateSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_switchboard_schemas_py.html#t355">core\schemas\switchboard_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_switchboard_schemas_py.html#t355"><data value='FeederComponentUpdateSchema'>FeederComponentUpdateSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_switchboard_schemas_py.html#t371">core\schemas\switchboard_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_switchboard_schemas_py.html#t371"><data value='FeederComponentReadSchema'>FeederComponentReadSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_switchboard_schemas_py.html#t392">core\schemas\switchboard_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_switchboard_schemas_py.html#t392"><data value='FeederComponentSummarySchema'>FeederComponentSummarySchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_switchboard_schemas_py.html#t409">core\schemas\switchboard_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_switchboard_schemas_py.html#t409"><data value='SwitchboardLoadSummarySchema'>SwitchboardLoadSummarySchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_switchboard_schemas_py.html#t426">core\schemas\switchboard_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_switchboard_schemas_py.html#t426"><data value='SwitchboardCapacityAnalysisSchema'>SwitchboardCapacityAnalysisSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_switchboard_schemas_py.html#t449">core\schemas\switchboard_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_switchboard_schemas_py.html#t449"><data value='SwitchboardPaginatedResponseSchema'>SwitchboardPaginatedResponseSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_switchboard_schemas_py.html#t479">core\schemas\switchboard_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_switchboard_schemas_py.html#t479"><data value='FeederPaginatedResponseSchema'>FeederPaginatedResponseSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_switchboard_schemas_py.html#t503">core\schemas\switchboard_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_switchboard_schemas_py.html#t503"><data value='SwitchboardComponentPaginatedResponseSchema'>SwitchboardComponentPaginatedResponseSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_switchboard_schemas_py.html#t531">core\schemas\switchboard_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_switchboard_schemas_py.html#t531"><data value='FeederComponentPaginatedResponseSchema'>FeederComponentPaginatedResponseSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_switchboard_schemas_py.html">core\schemas\switchboard_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_switchboard_schemas_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>141</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="141 141">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_user_schemas_py.html#t31">core\schemas\user_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_user_schemas_py.html#t31"><data value='UserBaseSchema'>UserBaseSchema</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_user_schemas_py.html#t47">core\schemas\user_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_user_schemas_py.html#t47"><data value='UserCreateSchema'>UserCreateSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>16</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_user_schemas_py.html#t86">core\schemas\user_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_user_schemas_py.html#t86"><data value='UserUpdateSchema'>UserUpdateSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_user_schemas_py.html#t99">core\schemas\user_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_user_schemas_py.html#t99"><data value='UserReadSchema'>UserReadSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_user_schemas_py.html#t119">core\schemas\user_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_user_schemas_py.html#t119"><data value='UserSummarySchema'>UserSummarySchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_user_schemas_py.html#t135">core\schemas\user_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_user_schemas_py.html#t135"><data value='UserPreferenceBaseSchema'>UserPreferenceBaseSchema</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_user_schemas_py.html#t190">core\schemas\user_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_user_schemas_py.html#t190"><data value='UserPreferenceCreateSchema'>UserPreferenceCreateSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_user_schemas_py.html#t209">core\schemas\user_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_user_schemas_py.html#t209"><data value='UserPreferenceUpdateSchema'>UserPreferenceUpdateSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_user_schemas_py.html#t247">core\schemas\user_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_user_schemas_py.html#t247"><data value='UserPreferenceReadSchema'>UserPreferenceReadSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_user_schemas_py.html#t278">core\schemas\user_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_user_schemas_py.html#t278"><data value='LoginRequestSchema'>LoginRequestSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_user_schemas_py.html#t294">core\schemas\user_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_user_schemas_py.html#t294"><data value='LoginResponseSchema'>LoginResponseSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_user_schemas_py.html#t319">core\schemas\user_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_user_schemas_py.html#t319"><data value='LogoutResponseSchema'>LogoutResponseSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_user_schemas_py.html#t333">core\schemas\user_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_user_schemas_py.html#t333"><data value='PasswordChangeRequestSchema'>PasswordChangeRequestSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>16</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_user_schemas_py.html#t371">core\schemas\user_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_user_schemas_py.html#t371"><data value='PasswordResetRequestSchema'>PasswordResetRequestSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_user_schemas_py.html#t385">core\schemas\user_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_user_schemas_py.html#t385"><data value='PasswordResetConfirmSchema'>PasswordResetConfirmSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>16</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_user_schemas_py.html#t428">core\schemas\user_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_user_schemas_py.html#t428"><data value='UserSessionSchema'>UserSessionSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_user_schemas_py.html#t456">core\schemas\user_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_user_schemas_py.html#t456"><data value='UserPaginatedResponseSchema'>UserPaginatedResponseSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_user_schemas_py.html">core\schemas\user_schemas.py</a></td>
                <td class="name left"><a href="z_9b29dd34b81637e5_user_schemas_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>80</td>
                <td>0</td>
                <td>22</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="80 80">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_activity_log_service_py.html#t88">core\services\activity_log_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_activity_log_service_py.html#t88"><data value='ActivityLogService'>ActivityLogService</data></a></td>
                <td>219</td>
                <td>219</td>
                <td>26</td>
                <td>60</td>
                <td>0</td>
                <td class="right" data-ratio="0 279">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_activity_log_service_py.html">core\services\activity_log_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_activity_log_service_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>45</td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="39 45">86.67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_component_service_py.html#t50">core\services\component_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_component_service_py.html#t50"><data value='ComponentService'>ComponentService</data></a></td>
                <td>159</td>
                <td>159</td>
                <td>14</td>
                <td>58</td>
                <td>0</td>
                <td class="right" data-ratio="0 217">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_component_service_py.html#t632">core\services\component_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_component_service_py.html#t632"><data value='ComponentCategoryService'>ComponentCategoryService</data></a></td>
                <td>58</td>
                <td>58</td>
                <td>7</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 72">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_component_service_py.html">core\services\component_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_component_service_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>28</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="28 28">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_dependencies_py.html">core\services\dependencies.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_dependencies_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>20</td>
                <td>12</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="8 20">40.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_document_service_py.html#t68">core\services\document_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_document_service_py.html#t68"><data value='DocumentService'>DocumentService</data></a></td>
                <td>290</td>
                <td>290</td>
                <td>33</td>
                <td>82</td>
                <td>0</td>
                <td class="right" data-ratio="0 372">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_document_service_py.html">core\services\document_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_document_service_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>42</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="42 42">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_electrical_service_py.html#t135">core\services\electrical_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_electrical_service_py.html#t135"><data value='ElectricalService'>ElectricalService</data></a></td>
                <td>189</td>
                <td>189</td>
                <td>17</td>
                <td>52</td>
                <td>0</td>
                <td class="right" data-ratio="0 241">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_electrical_service_py.html">core\services\electrical_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_electrical_service_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>39</td>
                <td>12</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="27 39">69.23%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_heat_tracing_service_py.html#t107">core\services\heat_tracing_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_heat_tracing_service_py.html#t107"><data value='HeatTracingService'>HeatTracingService</data></a></td>
                <td>290</td>
                <td>290</td>
                <td>30</td>
                <td>64</td>
                <td>0</td>
                <td class="right" data-ratio="0 354">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_heat_tracing_service_py.html">core\services\heat_tracing_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_heat_tracing_service_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>49</td>
                <td>10</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="39 49">79.59%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_project_service_py.html#t53">core\services\project_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_project_service_py.html#t53"><data value='ProjectService'>ProjectService</data></a></td>
                <td>149</td>
                <td>149</td>
                <td>17</td>
                <td>44</td>
                <td>0</td>
                <td class="right" data-ratio="0 193">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_project_service_py.html">core\services\project_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_project_service_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>20</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="20 20">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_switchboard_service_py.html#t98">core\services\switchboard_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_switchboard_service_py.html#t98"><data value='SwitchboardService'>SwitchboardService</data></a></td>
                <td>181</td>
                <td>181</td>
                <td>22</td>
                <td>46</td>
                <td>0</td>
                <td class="right" data-ratio="0 227">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_switchboard_service_py.html">core\services\switchboard_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_switchboard_service_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>39</td>
                <td>10</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="29 39">74.36%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_user_service_py.html#t101">core\services\user_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_user_service_py.html#t101"><data value='UserService'>UserService</data></a></td>
                <td>182</td>
                <td>182</td>
                <td>121</td>
                <td>46</td>
                <td>0</td>
                <td class="right" data-ratio="0 228">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_user_service_py.html">core\services\user_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_user_service_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>45</td>
                <td>9</td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="36 45">80.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5819abf34aa6dee0___init___py.html">core\standards\__init__.py</a></td>
                <td class="name left"><a href="z_5819abf34aa6dee0___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>9</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="9 9">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6ae03e9e7000b4b1___init___py.html">core\standards\api\__init__.py</a></td>
                <td class="name left"><a href="z_6ae03e9e7000b4b1___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6ae03e9e7000b4b1_api_rp_14f_py.html#t18">core\standards\api\api_rp_14f.py</a></td>
                <td class="name left"><a href="z_6ae03e9e7000b4b1_api_rp_14f_py.html#t18"><data value='APIRP14F'>APIRP14F</data></a></td>
                <td>41</td>
                <td>41</td>
                <td>5</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 47">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6ae03e9e7000b4b1_api_rp_14f_py.html">core\standards\api\api_rp_14f.py</a></td>
                <td class="name left"><a href="z_6ae03e9e7000b4b1_api_rp_14f_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>13</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="13 13">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6ae03e9e7000b4b1_api_rp_14fz_py.html#t18">core\standards\api\api_rp_14fz.py</a></td>
                <td class="name left"><a href="z_6ae03e9e7000b4b1_api_rp_14fz_py.html#t18"><data value='APIRP14FZ'>APIRP14FZ</data></a></td>
                <td>38</td>
                <td>38</td>
                <td>5</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 42">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6ae03e9e7000b4b1_api_rp_14fz_py.html">core\standards\api\api_rp_14fz.py</a></td>
                <td class="name left"><a href="z_6ae03e9e7000b4b1_api_rp_14fz_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>13</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="13 13">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6ae03e9e7000b4b1_api_standards_py.html#t18">core\standards\api\api_standards.py</a></td>
                <td class="name left"><a href="z_6ae03e9e7000b4b1_api_standards_py.html#t18"><data value='APIStandards'>APIStandards</data></a></td>
                <td>30</td>
                <td>30</td>
                <td>2</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 38">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6ae03e9e7000b4b1_api_standards_py.html">core\standards\api\api_standards.py</a></td>
                <td class="name left"><a href="z_6ae03e9e7000b4b1_api_standards_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>10</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="10 10">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_dbac4bf9b4599789___init___py.html">core\standards\general\__init__.py</a></td>
                <td class="name left"><a href="z_dbac4bf9b4599789___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_dbac4bf9b4599789_base_standard_py.html#t20">core\standards\general\base_standard.py</a></td>
                <td class="name left"><a href="z_dbac4bf9b4599789_base_standard_py.html#t20"><data value='StandardType'>StandardType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_dbac4bf9b4599789_base_standard_py.html#t33">core\standards\general\base_standard.py</a></td>
                <td class="name left"><a href="z_dbac4bf9b4599789_base_standard_py.html#t33"><data value='ComplianceLevel'>ComplianceLevel</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_dbac4bf9b4599789_base_standard_py.html#t42">core\standards\general\base_standard.py</a></td>
                <td class="name left"><a href="z_dbac4bf9b4599789_base_standard_py.html#t42"><data value='ValidationResult'>ValidationResult</data></a></td>
                <td>21</td>
                <td>21</td>
                <td>0</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 25">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_dbac4bf9b4599789_base_standard_py.html#t113">core\standards\general\base_standard.py</a></td>
                <td class="name left"><a href="z_dbac4bf9b4599789_base_standard_py.html#t113"><data value='BaseStandard'>BaseStandard</data></a></td>
                <td>45</td>
                <td>45</td>
                <td>46</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="0 65">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_dbac4bf9b4599789_base_standard_py.html">core\standards\general\base_standard.py</a></td>
                <td class="name left"><a href="z_dbac4bf9b4599789_base_standard_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>44</td>
                <td>0</td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="44 44">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_dbac4bf9b4599789_standards_calculator_py.html#t20">core\standards\general\standards_calculator.py</a></td>
                <td class="name left"><a href="z_dbac4bf9b4599789_standards_calculator_py.html#t20"><data value='CalculationResult'>CalculationResult</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_dbac4bf9b4599789_standards_calculator_py.html#t72">core\standards\general\standards_calculator.py</a></td>
                <td class="name left"><a href="z_dbac4bf9b4599789_standards_calculator_py.html#t72"><data value='StandardsCalculator'>StandardsCalculator</data></a></td>
                <td>136</td>
                <td>136</td>
                <td>11</td>
                <td>28</td>
                <td>0</td>
                <td class="right" data-ratio="0 164">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_dbac4bf9b4599789_standards_calculator_py.html">core\standards\general\standards_calculator.py</a></td>
                <td class="name left"><a href="z_dbac4bf9b4599789_standards_calculator_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>25</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="25 25">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_dbac4bf9b4599789_standards_validator_py.html#t19">core\standards\general\standards_validator.py</a></td>
                <td class="name left"><a href="z_dbac4bf9b4599789_standards_validator_py.html#t19"><data value='StandardsValidator'>StandardsValidator</data></a></td>
                <td>126</td>
                <td>126</td>
                <td>12</td>
                <td>42</td>
                <td>0</td>
                <td class="right" data-ratio="0 168">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_dbac4bf9b4599789_standards_validator_py.html">core\standards\general\standards_validator.py</a></td>
                <td class="name left"><a href="z_dbac4bf9b4599789_standards_validator_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>22</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="22 22">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3850ffddfe46d663___init___py.html">core\standards\iec\__init__.py</a></td>
                <td class="name left"><a href="z_3850ffddfe46d663___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3850ffddfe46d663_iec_60079_py.html#t18">core\standards\iec\iec_60079.py</a></td>
                <td class="name left"><a href="z_3850ffddfe46d663_iec_60079_py.html#t18"><data value='IEC60079'>IEC60079</data></a></td>
                <td>40</td>
                <td>40</td>
                <td>5</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 46">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3850ffddfe46d663_iec_60079_py.html">core\standards\iec\iec_60079.py</a></td>
                <td class="name left"><a href="z_3850ffddfe46d663_iec_60079_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>13</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="13 13">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3850ffddfe46d663_iec_62395_py.html#t18">core\standards\iec\iec_62395.py</a></td>
                <td class="name left"><a href="z_3850ffddfe46d663_iec_62395_py.html#t18"><data value='IEC62395'>IEC62395</data></a></td>
                <td>39</td>
                <td>39</td>
                <td>5</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 45">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3850ffddfe46d663_iec_62395_py.html">core\standards\iec\iec_62395.py</a></td>
                <td class="name left"><a href="z_3850ffddfe46d663_iec_62395_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>13</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="13 13">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3850ffddfe46d663_iec_standards_py.html#t18">core\standards\iec\iec_standards.py</a></td>
                <td class="name left"><a href="z_3850ffddfe46d663_iec_standards_py.html#t18"><data value='IECStandards'>IECStandards</data></a></td>
                <td>82</td>
                <td>82</td>
                <td>6</td>
                <td>22</td>
                <td>0</td>
                <td class="right" data-ratio="0 104">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3850ffddfe46d663_iec_standards_py.html">core\standards\iec\iec_standards.py</a></td>
                <td class="name left"><a href="z_3850ffddfe46d663_iec_standards_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>14</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="14 14">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_acb3a9c72f1ecd2d___init___py.html">core\standards\iec_60079_30_1\__init__.py</a></td>
                <td class="name left"><a href="z_acb3a9c72f1ecd2d___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_acb3a9c72f1ecd2d_hazardous_area_compliance_py.html">core\standards\iec_60079_30_1\hazardous_area_compliance.py</a></td>
                <td class="name left"><a href="z_acb3a9c72f1ecd2d_hazardous_area_compliance_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>66</td>
                <td>66</td>
                <td>3</td>
                <td>34</td>
                <td>0</td>
                <td class="right" data-ratio="0 100">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_acb3a9c72f1ecd2d_temperature_class_limits_py.html">core\standards\iec_60079_30_1\temperature_class_limits.py</a></td>
                <td class="name left"><a href="z_acb3a9c72f1ecd2d_temperature_class_limits_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>36</td>
                <td>36</td>
                <td>4</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 44">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_11fc723c286b0310___init___py.html">core\standards\ieee\__init__.py</a></td>
                <td class="name left"><a href="z_11fc723c286b0310___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_11fc723c286b0310_ieee_515_py.html#t19">core\standards\ieee\ieee_515.py</a></td>
                <td class="name left"><a href="z_11fc723c286b0310_ieee_515_py.html#t19"><data value='IEEE515'>IEEE515</data></a></td>
                <td>125</td>
                <td>125</td>
                <td>7</td>
                <td>44</td>
                <td>0</td>
                <td class="right" data-ratio="0 169">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_11fc723c286b0310_ieee_515_py.html">core\standards\ieee\ieee_515.py</a></td>
                <td class="name left"><a href="z_11fc723c286b0310_ieee_515_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>19</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="19 19">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_11fc723c286b0310_ieee_844_py.html#t19">core\standards\ieee\ieee_844.py</a></td>
                <td class="name left"><a href="z_11fc723c286b0310_ieee_844_py.html#t19"><data value='IEEE844'>IEEE844</data></a></td>
                <td>112</td>
                <td>112</td>
                <td>6</td>
                <td>34</td>
                <td>0</td>
                <td class="right" data-ratio="0 146">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_11fc723c286b0310_ieee_844_py.html">core\standards\ieee\ieee_844.py</a></td>
                <td class="name left"><a href="z_11fc723c286b0310_ieee_844_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>20</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="20 20">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_11fc723c286b0310_ieee_standards_py.html#t18">core\standards\ieee\ieee_standards.py</a></td>
                <td class="name left"><a href="z_11fc723c286b0310_ieee_standards_py.html#t18"><data value='IEEEStandards'>IEEEStandards</data></a></td>
                <td>83</td>
                <td>83</td>
                <td>6</td>
                <td>32</td>
                <td>0</td>
                <td class="right" data-ratio="0 115">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_11fc723c286b0310_ieee_standards_py.html">core\standards\ieee\ieee_standards.py</a></td>
                <td class="name left"><a href="z_11fc723c286b0310_ieee_standards_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>14</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="14 14">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8199a77f46f683ea___init___py.html">core\standards\iso\__init__.py</a></td>
                <td class="name left"><a href="z_8199a77f46f683ea___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8199a77f46f683ea_iso_13623_py.html#t15">core\standards\iso\iso_13623.py</a></td>
                <td class="name left"><a href="z_8199a77f46f683ea_iso_13623_py.html#t15"><data value='ISO13623'>ISO13623</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8199a77f46f683ea_iso_13623_py.html">core\standards\iso\iso_13623.py</a></td>
                <td class="name left"><a href="z_8199a77f46f683ea_iso_13623_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>10</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="10 10">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8199a77f46f683ea_iso_14692_py.html#t15">core\standards\iso\iso_14692.py</a></td>
                <td class="name left"><a href="z_8199a77f46f683ea_iso_14692_py.html#t15"><data value='ISO14692'>ISO14692</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8199a77f46f683ea_iso_14692_py.html">core\standards\iso\iso_14692.py</a></td>
                <td class="name left"><a href="z_8199a77f46f683ea_iso_14692_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>10</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="10 10">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8199a77f46f683ea_iso_standards_py.html#t17">core\standards\iso\iso_standards.py</a></td>
                <td class="name left"><a href="z_8199a77f46f683ea_iso_standards_py.html#t17"><data value='ISOStandards'>ISOStandards</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8199a77f46f683ea_iso_standards_py.html">core\standards\iso\iso_standards.py</a></td>
                <td class="name left"><a href="z_8199a77f46f683ea_iso_standards_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="8 8">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7d8fbd68730761fe___init___py.html">core\standards\nfpa\__init__.py</a></td>
                <td class="name left"><a href="z_7d8fbd68730761fe___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7d8fbd68730761fe_nfpa_497_py.html#t15">core\standards\nfpa\nfpa_497.py</a></td>
                <td class="name left"><a href="z_7d8fbd68730761fe_nfpa_497_py.html#t15"><data value='NFPA497'>NFPA497</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7d8fbd68730761fe_nfpa_497_py.html">core\standards\nfpa\nfpa_497.py</a></td>
                <td class="name left"><a href="z_7d8fbd68730761fe_nfpa_497_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>10</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="10 10">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7d8fbd68730761fe_nfpa_70_py.html#t15">core\standards\nfpa\nfpa_70.py</a></td>
                <td class="name left"><a href="z_7d8fbd68730761fe_nfpa_70_py.html#t15"><data value='NFPA70'>NFPA70</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7d8fbd68730761fe_nfpa_70_py.html">core\standards\nfpa\nfpa_70.py</a></td>
                <td class="name left"><a href="z_7d8fbd68730761fe_nfpa_70_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>10</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="10 10">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7d8fbd68730761fe_nfpa_standards_py.html#t17">core\standards\nfpa\nfpa_standards.py</a></td>
                <td class="name left"><a href="z_7d8fbd68730761fe_nfpa_standards_py.html#t17"><data value='NFPAStandards'>NFPAStandards</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7d8fbd68730761fe_nfpa_standards_py.html">core\standards\nfpa\nfpa_standards.py</a></td>
                <td class="name left"><a href="z_7d8fbd68730761fe_nfpa_standards_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="8 8">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5819abf34aa6dee0_standards_manager_py.html#t19">core\standards\standards_manager.py</a></td>
                <td class="name left"><a href="z_5819abf34aa6dee0_standards_manager_py.html#t19"><data value='StandardType'>StandardType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5819abf34aa6dee0_standards_manager_py.html#t28">core\standards\standards_manager.py</a></td>
                <td class="name left"><a href="z_5819abf34aa6dee0_standards_manager_py.html#t28"><data value='ValidationResult'>ValidationResult</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5819abf34aa6dee0_standards_manager_py.html#t40">core\standards\standards_manager.py</a></td>
                <td class="name left"><a href="z_5819abf34aa6dee0_standards_manager_py.html#t40"><data value='StandardsContext'>StandardsContext</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5819abf34aa6dee0_standards_manager_py.html#t51">core\standards\standards_manager.py</a></td>
                <td class="name left"><a href="z_5819abf34aa6dee0_standards_manager_py.html#t51"><data value='StandardsManager'>StandardsManager</data></a></td>
                <td>141</td>
                <td>141</td>
                <td>8</td>
                <td>62</td>
                <td>0</td>
                <td class="right" data-ratio="0 203">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5819abf34aa6dee0_standards_manager_py.html">core\standards\standards_manager.py</a></td>
                <td class="name left"><a href="z_5819abf34aa6dee0_standards_manager_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>40</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="40 40">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5819abf34aa6dee0_standards_service_py.html#t24">core\standards\standards_service.py</a></td>
                <td class="name left"><a href="z_5819abf34aa6dee0_standards_service_py.html#t24"><data value='StandardsService'>StandardsService</data></a></td>
                <td>162</td>
                <td>162</td>
                <td>9</td>
                <td>44</td>
                <td>0</td>
                <td class="right" data-ratio="0 206">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5819abf34aa6dee0_standards_service_py.html">core\standards\standards_service.py</a></td>
                <td class="name left"><a href="z_5819abf34aa6dee0_standards_service_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>24</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="24 24">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e19f0f1c06c261bc___init___py.html">core\standards\tr_50410\__init__.py</a></td>
                <td class="name left"><a href="z_e19f0f1c06c261bc___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e19f0f1c06c261bc_heat_loss_factors_py.html">core\standards\tr_50410\heat_loss_factors.py</a></td>
                <td class="name left"><a href="z_e19f0f1c06c261bc_heat_loss_factors_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>23</td>
                <td>23</td>
                <td>1</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 31">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8e66a5447a3433f3___init___py.html">core\utils\__init__.py</a></td>
                <td class="name left"><a href="z_8e66a5447a3433f3___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>9</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="9 9">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8e66a5447a3433f3_crud_endpoint_factory_py.html#t44">core\utils\crud_endpoint_factory.py</a></td>
                <td class="name left"><a href="z_8e66a5447a3433f3_crud_endpoint_factory_py.html#t44"><data value='CRUDEndpointConfig'>CRUDEndpointConfig</data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8e66a5447a3433f3_crud_endpoint_factory_py.html">core\utils\crud_endpoint_factory.py</a></td>
                <td class="name left"><a href="z_8e66a5447a3433f3_crud_endpoint_factory_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>104</td>
                <td>83</td>
                <td>0</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="21 118">17.80%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8e66a5447a3433f3_datetime_utils_py.html">core\utils\datetime_utils.py</a></td>
                <td class="name left"><a href="z_8e66a5447a3433f3_datetime_utils_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>94</td>
                <td>69</td>
                <td>1</td>
                <td>36</td>
                <td>0</td>
                <td class="right" data-ratio="25 130">19.23%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8e66a5447a3433f3_file_io_utils_py.html#t46">core\utils\file_io_utils.py</a></td>
                <td class="name left"><a href="z_8e66a5447a3433f3_file_io_utils_py.html#t46"><data value='FileIOError'>FileIOError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8e66a5447a3433f3_file_io_utils_py.html">core\utils\file_io_utils.py</a></td>
                <td class="name left"><a href="z_8e66a5447a3433f3_file_io_utils_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>163</td>
                <td>132</td>
                <td>6</td>
                <td>42</td>
                <td>0</td>
                <td class="right" data-ratio="31 205">15.12%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8e66a5447a3433f3_json_validation_py.html#t27">core\utils\json_validation.py</a></td>
                <td class="name left"><a href="z_8e66a5447a3433f3_json_validation_py.html#t27"><data value='JSONValidationError'>JSONValidationError</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8e66a5447a3433f3_json_validation_py.html#t163">core\utils\json_validation.py</a></td>
                <td class="name left"><a href="z_8e66a5447a3433f3_json_validation_py.html#t163"><data value='ValidatedJSON'>ValidatedJSON</data></a></td>
                <td>19</td>
                <td>17</td>
                <td>0</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="2 25">8.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8e66a5447a3433f3_json_validation_py.html#t213">core\utils\json_validation.py</a></td>
                <td class="name left"><a href="z_8e66a5447a3433f3_json_validation_py.html#t213"><data value='FlexibleJSON'>FlexibleJSON</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8e66a5447a3433f3_json_validation_py.html">core\utils\json_validation.py</a></td>
                <td class="name left"><a href="z_8e66a5447a3433f3_json_validation_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>107</td>
                <td>81</td>
                <td>0</td>
                <td>44</td>
                <td>0</td>
                <td class="right" data-ratio="26 151">17.22%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8e66a5447a3433f3_pagination_utils_py.html#t32">core\utils\pagination_utils.py</a></td>
                <td class="name left"><a href="z_8e66a5447a3433f3_pagination_utils_py.html#t32"><data value='PaginationParams'>PaginationParams</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8e66a5447a3433f3_pagination_utils_py.html#t49">core\utils\pagination_utils.py</a></td>
                <td class="name left"><a href="z_8e66a5447a3433f3_pagination_utils_py.html#t49"><data value='SortParams'>SortParams</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8e66a5447a3433f3_pagination_utils_py.html#t61">core\utils\pagination_utils.py</a></td>
                <td class="name left"><a href="z_8e66a5447a3433f3_pagination_utils_py.html#t61"><data value='PaginationResult'>PaginationResult</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8e66a5447a3433f3_pagination_utils_py.html">core\utils\pagination_utils.py</a></td>
                <td class="name left"><a href="z_8e66a5447a3433f3_pagination_utils_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>92</td>
                <td>52</td>
                <td>0</td>
                <td>30</td>
                <td>0</td>
                <td class="right" data-ratio="40 122">32.79%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8e66a5447a3433f3_query_utils_py.html#t31">core\utils\query_utils.py</a></td>
                <td class="name left"><a href="z_8e66a5447a3433f3_query_utils_py.html#t31"><data value='QueryBuilder'>QueryBuilder</data></a></td>
                <td>59</td>
                <td>59</td>
                <td>0</td>
                <td>40</td>
                <td>0</td>
                <td class="right" data-ratio="0 99">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8e66a5447a3433f3_query_utils_py.html">core\utils\query_utils.py</a></td>
                <td class="name left"><a href="z_8e66a5447a3433f3_query_utils_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>108</td>
                <td>85</td>
                <td>0</td>
                <td>62</td>
                <td>0</td>
                <td class="right" data-ratio="23 170">13.53%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8e66a5447a3433f3_string_utils_py.html">core\utils\string_utils.py</a></td>
                <td class="name left"><a href="z_8e66a5447a3433f3_string_utils_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>97</td>
                <td>76</td>
                <td>0</td>
                <td>46</td>
                <td>0</td>
                <td class="right" data-ratio="21 143">14.69%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8e66a5447a3433f3_unit_conversion_utils_py.html#t80">core\utils\unit_conversion_utils.py</a></td>
                <td class="name left"><a href="z_8e66a5447a3433f3_unit_conversion_utils_py.html#t80"><data value='UnitConversionError'>UnitConversionError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8e66a5447a3433f3_unit_conversion_utils_py.html">core\utils\unit_conversion_utils.py</a></td>
                <td class="name left"><a href="z_8e66a5447a3433f3_unit_conversion_utils_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>116</td>
                <td>116</td>
                <td>1</td>
                <td>56</td>
                <td>0</td>
                <td class="right" data-ratio="0 172">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8e66a5447a3433f3_uuid_utils_py.html">core\utils\uuid_utils.py</a></td>
                <td class="name left"><a href="z_8e66a5447a3433f3_uuid_utils_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>45</td>
                <td>29</td>
                <td>0</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="16 57">28.07%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td class="name left">&nbsp;</td>
                <td>15930</td>
                <td>11383</td>
                <td>1563</td>
                <td>3374</td>
                <td>5</td>
                <td class="right" data-ratio="4554 19304">23.59%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.0">coverage.py v7.8.0</a>,
            created at 2025-06-03 23:24 +0300
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href=""></a>
        <a id="nextFileLink" class="nav" href=""></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
