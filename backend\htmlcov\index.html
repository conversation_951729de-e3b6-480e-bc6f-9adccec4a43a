<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Ultimate Electrical Designer Backend Coverage Report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_718ce007.css" type="text/css">
    <script src="coverage_html_cb_497bf287.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Ultimate Electrical Designer Backend Coverage Report:
            <span class="pc_cov">23.59%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>b</kbd>
                        <kbd>p</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button current">Files</a>
                <a class="button" href="function_index.html">Functions</a>
                <a class="button" href="class_index.html">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.0">coverage.py v7.8.0</a>,
            created at 2025-06-03 23:24 +0300
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="branches" aria-sort="none" data-default-sort-order="descending" data-shortcut="b">branches<span class="arrows"></span></th>
                <th id="partial" aria-sort="none" data-default-sort-order="descending" data-shortcut="p">partial<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_10fae538ba4e8521_dependencies_py.html">api\dependencies.py</a></td>
                <td>16</td>
                <td>9</td>
                <td>0</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="7 22">31.82%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_10fae538ba4e8521_main_router_py.html">api\main_router.py</a></td>
                <td>24</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="24 24">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3c8229fafc2171e7_activity_log_routes_py.html">api\v1\activity_log_routes.py</a></td>
                <td>147</td>
                <td>108</td>
                <td>18</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="39 159">24.53%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3c8229fafc2171e7_component_routes_py.html">api\v1\component_routes.py</a></td>
                <td>137</td>
                <td>108</td>
                <td>14</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="29 139">20.86%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3c8229fafc2171e7_document_routes_py.html">api\v1\document_routes.py</a></td>
                <td>149</td>
                <td>102</td>
                <td>16</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="47 161">29.19%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3c8229fafc2171e7_electrical_routes_py.html">api\v1\electrical_routes.py</a></td>
                <td>243</td>
                <td>192</td>
                <td>32</td>
                <td>36</td>
                <td>0</td>
                <td class="right" data-ratio="51 279">18.28%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3c8229fafc2171e7_heat_tracing_routes_py.html">api\v1\heat_tracing_routes.py</a></td>
                <td>186</td>
                <td>147</td>
                <td>22</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="39 188">20.74%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3c8229fafc2171e7_import_export_routes_py.html">api\v1\import_export_routes.py</a></td>
                <td>225</td>
                <td>167</td>
                <td>12</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="58 233">24.89%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3c8229fafc2171e7_project_routes_py.html">api\v1\project_routes.py</a></td>
                <td>70</td>
                <td>48</td>
                <td>1</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="22 72">30.56%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3c8229fafc2171e7_reports_routes_py.html">api\v1\reports_routes.py</a></td>
                <td>168</td>
                <td>97</td>
                <td>10</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="71 174">40.80%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3c8229fafc2171e7_standards_routes_py.html">api\v1\standards_routes.py</a></td>
                <td>148</td>
                <td>97</td>
                <td>11</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="51 150">34.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3c8229fafc2171e7_switchboard_routes_py.html">api\v1\switchboard_routes.py</a></td>
                <td>140</td>
                <td>104</td>
                <td>17</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="36 152">23.68%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3c8229fafc2171e7_user_routes_py.html">api\v1\user_routes.py</a></td>
                <td>112</td>
                <td>79</td>
                <td>97</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="33 128">25.78%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1ea838694151cac8_logging_config_py.html">config\logging_config.py</a></td>
                <td>47</td>
                <td>5</td>
                <td>1</td>
                <td>12</td>
                <td>5</td>
                <td class="right" data-ratio="49 59">83.05%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1ea838694151cac8_settings_py.html">config\settings.py</a></td>
                <td>38</td>
                <td>9</td>
                <td>0</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="29 42">69.05%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d51c265d7ed55fb9___init___py.html">core\calculations\__init__.py</a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d51c265d7ed55fb9_calculation_service_py.html">core\calculations\calculation_service.py</a></td>
                <td>415</td>
                <td>338</td>
                <td>23</td>
                <td>142</td>
                <td>0</td>
                <td class="right" data-ratio="77 557">13.82%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0a7bfb953e6da477___init___py.html">core\calculations\circuit_design\__init__.py</a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0a7bfb953e6da477_circuit_breaker_sizing_py.html">core\calculations\circuit_design\circuit_breaker_sizing.py</a></td>
                <td>104</td>
                <td>79</td>
                <td>6</td>
                <td>34</td>
                <td>0</td>
                <td class="right" data-ratio="25 138">18.12%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0a7bfb953e6da477_control_circuit_logic_py.html">core\calculations\circuit_design\control_circuit_logic.py</a></td>
                <td>153</td>
                <td>117</td>
                <td>5</td>
                <td>44</td>
                <td>0</td>
                <td class="right" data-ratio="36 197">18.27%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bf815d1c1bf6cbfb___init___py.html">core\calculations\common_properties\__init__.py</a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bf815d1c1bf6cbfb_fluid_properties_py.html">core\calculations\common_properties\fluid_properties.py</a></td>
                <td>102</td>
                <td>88</td>
                <td>8</td>
                <td>32</td>
                <td>0</td>
                <td class="right" data-ratio="14 134">10.45%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bf815d1c1bf6cbfb_material_data_py.html">core\calculations\common_properties\material_data.py</a></td>
                <td>68</td>
                <td>55</td>
                <td>13</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="13 82">15.85%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_52a2768ca9a74860___init___py.html">core\calculations\electrical_sizing\__init__.py</a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_52a2768ca9a74860_cable_sizing_py.html">core\calculations\electrical_sizing\cable_sizing.py</a></td>
                <td>77</td>
                <td>66</td>
                <td>5</td>
                <td>26</td>
                <td>0</td>
                <td class="right" data-ratio="11 103">10.68%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_52a2768ca9a74860_voltage_drop_py.html">core\calculations\electrical_sizing\voltage_drop.py</a></td>
                <td>74</td>
                <td>64</td>
                <td>8</td>
                <td>26</td>
                <td>0</td>
                <td class="right" data-ratio="10 100">10.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_df3ce16558279790___init___py.html">core\calculations\heat_loss\__init__.py</a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_df3ce16558279790_insulation_properties_py.html">core\calculations\heat_loss\insulation_properties.py</a></td>
                <td>69</td>
                <td>56</td>
                <td>7</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="13 89">14.61%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_df3ce16558279790_pipe_heat_loss_py.html">core\calculations\heat_loss\pipe_heat_loss.py</a></td>
                <td>96</td>
                <td>81</td>
                <td>3</td>
                <td>34</td>
                <td>0</td>
                <td class="right" data-ratio="15 130">11.54%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_df3ce16558279790_vessel_heat_loss_py.html">core\calculations\heat_loss\vessel_heat_loss.py</a></td>
                <td>62</td>
                <td>53</td>
                <td>2</td>
                <td>22</td>
                <td>0</td>
                <td class="right" data-ratio="9 84">10.71%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e4d2cb2bb52b2279___init___py.html">core\calculations\utils\__init__.py</a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e4d2cb2bb52b2279_input_parser_py.html">core\calculations\utils\input_parser.py</a></td>
                <td>153</td>
                <td>134</td>
                <td>15</td>
                <td>70</td>
                <td>0</td>
                <td class="right" data-ratio="19 223">8.52%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e4d2cb2bb52b2279_math_helpers_py.html">core\calculations\utils\math_helpers.py</a></td>
                <td>162</td>
                <td>145</td>
                <td>17</td>
                <td>58</td>
                <td>0</td>
                <td class="right" data-ratio="17 220">7.73%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e4d2cb2bb52b2279_units_conversion_py.html">core\calculations\utils\units_conversion.py</a></td>
                <td>146</td>
                <td>124</td>
                <td>16</td>
                <td>48</td>
                <td>0</td>
                <td class="right" data-ratio="22 194">11.34%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e4d2cb2bb52b2279_validation_rules_py.html">core\calculations\utils\validation_rules.py</a></td>
                <td>120</td>
                <td>102</td>
                <td>10</td>
                <td>48</td>
                <td>0</td>
                <td class="right" data-ratio="18 168">10.71%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_dd0cc83923a11228___init___py.html">core\data_import\__init__.py</a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_dd0cc83923a11228_global_importer_py.html">core\data_import\global_importer.py</a></td>
                <td>144</td>
                <td>123</td>
                <td>12</td>
                <td>46</td>
                <td>0</td>
                <td class="right" data-ratio="21 190">11.05%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_dd0cc83923a11228_import_service_py.html">core\data_import\import_service.py</a></td>
                <td>142</td>
                <td>121</td>
                <td>12</td>
                <td>36</td>
                <td>0</td>
                <td class="right" data-ratio="21 178">11.80%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f712d5acc85bf35f___init___py.html">core\data_import\mappers\__init__.py</a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f712d5acc85bf35f_catalog_data_mapper_py.html">core\data_import\mappers\catalog_data_mapper.py</a></td>
                <td>179</td>
                <td>159</td>
                <td>9</td>
                <td>72</td>
                <td>0</td>
                <td class="right" data-ratio="20 251">7.97%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f712d5acc85bf35f_project_data_mapper_py.html">core\data_import\mappers\project_data_mapper.py</a></td>
                <td>161</td>
                <td>141</td>
                <td>9</td>
                <td>52</td>
                <td>0</td>
                <td class="right" data-ratio="20 213">9.39%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b263df31e4f5f767___init___py.html">core\data_import\parsers\__init__.py</a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b263df31e4f5f767_csv_parser_py.html">core\data_import\parsers\csv_parser.py</a></td>
                <td>168</td>
                <td>150</td>
                <td>14</td>
                <td>54</td>
                <td>0</td>
                <td class="right" data-ratio="18 222">8.11%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b263df31e4f5f767_json_parser_py.html">core\data_import\parsers\json_parser.py</a></td>
                <td>189</td>
                <td>171</td>
                <td>13</td>
                <td>68</td>
                <td>0</td>
                <td class="right" data-ratio="18 257">7.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b263df31e4f5f767_xlsx_parser_py.html">core\data_import\parsers\xlsx_parser.py</a></td>
                <td>130</td>
                <td>113</td>
                <td>10</td>
                <td>38</td>
                <td>0</td>
                <td class="right" data-ratio="17 168">10.12%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_dd0cc83923a11228_project_importer_py.html">core\data_import\project_importer.py</a></td>
                <td>193</td>
                <td>168</td>
                <td>14</td>
                <td>56</td>
                <td>0</td>
                <td class="right" data-ratio="25 249">10.04%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4343209610adb865___init___py.html">core\data_import\validators\__init__.py</a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4343209610adb865_import_data_validator_py.html">core\data_import\validators\import_data_validator.py</a></td>
                <td>200</td>
                <td>173</td>
                <td>9</td>
                <td>134</td>
                <td>0</td>
                <td class="right" data-ratio="27 334">8.08%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2e48d8fb49f22d07___init___py.html">core\database\__init__.py</a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2e48d8fb49f22d07_dependencies_py.html">core\database\dependencies.py</a></td>
                <td>7</td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 7">71.43%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2e48d8fb49f22d07_engine_py.html">core\database\engine.py</a></td>
                <td>77</td>
                <td>59</td>
                <td>6</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="18 97">18.56%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2e48d8fb49f22d07_initialization_py.html">core\database\initialization.py</a></td>
                <td>94</td>
                <td>73</td>
                <td>12</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="21 100">21.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2e48d8fb49f22d07_session_py.html">core\database\session.py</a></td>
                <td>71</td>
                <td>50</td>
                <td>7</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="21 83">25.30%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5d678fac7219f0d6_error_factory_py.html">core\errors\error_factory.py</a></td>
                <td>12</td>
                <td>6</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="6 14">42.86%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5d678fac7219f0d6_error_registry_py.html">core\errors\error_registry.py</a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5d678fac7219f0d6_error_templates_py.html">core\errors\error_templates.py</a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5d678fac7219f0d6_exceptions_py.html">core\errors\exceptions.py</a></td>
                <td>43</td>
                <td>21</td>
                <td>1</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="22 47">46.81%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f2bf686f1e66ce65___init___py.html">core\models\__init__.py</a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f2bf686f1e66ce65_activity_log_py.html">core\models\activity_log.py</a></td>
                <td>13</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="13 13">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f2bf686f1e66ce65_base_py.html">core\models\base.py</a></td>
                <td>58</td>
                <td>14</td>
                <td>0</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="44 66">66.67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f2bf686f1e66ce65_components_py.html">core\models\components.py</a></td>
                <td>21</td>
                <td>0</td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="21 21">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f2bf686f1e66ce65_documents_py.html">core\models\documents.py</a></td>
                <td>36</td>
                <td>0</td>
                <td>9</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="36 36">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f2bf686f1e66ce65_electrical_py.html">core\models\electrical.py</a></td>
                <td>129</td>
                <td>0</td>
                <td>22</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="129 129">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f2bf686f1e66ce65_enums_py.html">core\models\enums.py</a></td>
                <td>75</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="75 75">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f2bf686f1e66ce65_heat_tracing_py.html">core\models\heat_tracing.py</a></td>
                <td>123</td>
                <td>0</td>
                <td>19</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="123 123">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f2bf686f1e66ce65_project_py.html">core\models\project.py</a></td>
                <td>82</td>
                <td>37</td>
                <td>3</td>
                <td>28</td>
                <td>0</td>
                <td class="right" data-ratio="45 110">40.91%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f2bf686f1e66ce65_switchboard_py.html">core\models\switchboard.py</a></td>
                <td>45</td>
                <td>0</td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="45 45">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f2bf686f1e66ce65_users_py.html">core\models\users.py</a></td>
                <td>24</td>
                <td>0</td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="24 24">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a6617d05fb563bba___init___py.html">core\reports\__init__.py</a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a6617d05fb563bba_data_exporter_py.html">core\reports\data_exporter.py</a></td>
                <td>196</td>
                <td>171</td>
                <td>12</td>
                <td>78</td>
                <td>0</td>
                <td class="right" data-ratio="25 274">9.12%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ded8937c655412b8___init___py.html">core\reports\data_preparation\__init__.py</a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ded8937c655412b8_calculation_data_processor_py.html">core\reports\data_preparation\calculation_data_processor.py</a></td>
                <td>160</td>
                <td>141</td>
                <td>9</td>
                <td>34</td>
                <td>0</td>
                <td class="right" data-ratio="19 194">9.79%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ded8937c655412b8_export_data_formatter_py.html">core\reports\data_preparation\export_data_formatter.py</a></td>
                <td>172</td>
                <td>143</td>
                <td>6</td>
                <td>64</td>
                <td>0</td>
                <td class="right" data-ratio="29 236">12.29%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ded8937c655412b8_report_data_aggregator_py.html">core\reports\data_preparation\report_data_aggregator.py</a></td>
                <td>147</td>
                <td>127</td>
                <td>9</td>
                <td>34</td>
                <td>0</td>
                <td class="right" data-ratio="20 181">11.05%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a6617d05fb563bba_document_generator_py.html">core\reports\document_generator.py</a></td>
                <td>210</td>
                <td>187</td>
                <td>15</td>
                <td>68</td>
                <td>0</td>
                <td class="right" data-ratio="23 278">8.27%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bd29078fd467125e___init___py.html">core\reports\generators\__init__.py</a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bd29078fd467125e_excel_generator_py.html">core\reports\generators\excel_generator.py</a></td>
                <td>186</td>
                <td>161</td>
                <td>6</td>
                <td>60</td>
                <td>0</td>
                <td class="right" data-ratio="25 246">10.16%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bd29078fd467125e_html_generator_py.html">core\reports\generators\html_generator.py</a></td>
                <td>95</td>
                <td>75</td>
                <td>7</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="20 105">19.05%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bd29078fd467125e_pdf_generator_py.html">core\reports\generators\pdf_generator.py</a></td>
                <td>103</td>
                <td>85</td>
                <td>5</td>
                <td>24</td>
                <td>0</td>
                <td class="right" data-ratio="18 127">14.17%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a6617d05fb563bba_report_service_py.html">core\reports\report_service.py</a></td>
                <td>162</td>
                <td>137</td>
                <td>17</td>
                <td>42</td>
                <td>0</td>
                <td class="right" data-ratio="25 204">12.25%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_29755ff45d857bc4___init___py.html">core\reports\templates\__init__.py</a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_29755ff45d857bc4_document_templates_py.html">core\reports\templates\document_templates.py</a></td>
                <td>34</td>
                <td>17</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="17 34">50.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_29755ff45d857bc4_template_manager_py.html">core\reports\templates\template_manager.py</a></td>
                <td>162</td>
                <td>141</td>
                <td>12</td>
                <td>48</td>
                <td>0</td>
                <td class="right" data-ratio="21 210">10.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_29755ff45d857bc4_template_renderer_py.html">core\reports\templates\template_renderer.py</a></td>
                <td>147</td>
                <td>120</td>
                <td>8</td>
                <td>26</td>
                <td>0</td>
                <td class="right" data-ratio="27 173">15.61%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ac369222432f63da_activity_log_repository_py.html">core\repositories\activity_log_repository.py</a></td>
                <td>198</td>
                <td>170</td>
                <td>30</td>
                <td>32</td>
                <td>0</td>
                <td class="right" data-ratio="28 230">12.17%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ac369222432f63da_base_repository_py.html">core\repositories\base_repository.py</a></td>
                <td>103</td>
                <td>82</td>
                <td>0</td>
                <td>32</td>
                <td>0</td>
                <td class="right" data-ratio="21 135">15.56%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ac369222432f63da_component_repository_py.html">core\repositories\component_repository.py</a></td>
                <td>97</td>
                <td>77</td>
                <td>20</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="20 103">19.42%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ac369222432f63da_dependencies_py.html">core\repositories\dependencies.py</a></td>
                <td>27</td>
                <td>16</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="11 27">40.74%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ac369222432f63da_document_repository_py.html">core\repositories\document_repository.py</a></td>
                <td>193</td>
                <td>161</td>
                <td>43</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="32 197">16.24%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ac369222432f63da_electrical_repository_py.html">core\repositories\electrical_repository.py</a></td>
                <td>309</td>
                <td>262</td>
                <td>65</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="47 313">15.02%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ac369222432f63da_heat_tracing_repository_py.html">core\repositories\heat_tracing_repository.py</a></td>
                <td>264</td>
                <td>222</td>
                <td>58</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="42 270">15.56%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ac369222432f63da_project_repository_py.html">core\repositories\project_repository.py</a></td>
                <td>98</td>
                <td>79</td>
                <td>22</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="19 98">19.39%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ac369222432f63da_switchboard_repository_py.html">core\repositories\switchboard_repository.py</a></td>
                <td>153</td>
                <td>123</td>
                <td>32</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="30 153">19.61%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ac369222432f63da_user_repository_py.html">core\repositories\user_repository.py</a></td>
                <td>120</td>
                <td>98</td>
                <td>62</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="22 122">18.03%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_activity_log_schemas_py.html">core\schemas\activity_log_schemas.py</a></td>
                <td>182</td>
                <td>36</td>
                <td>1</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="146 200">73.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_base_py.html">core\schemas\base.py</a></td>
                <td>42</td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="39 42">92.86%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_component_schemas_py.html">core\schemas\component_schemas.py</a></td>
                <td>82</td>
                <td>19</td>
                <td>0</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="63 92">68.48%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_document_schemas_py.html">core\schemas\document_schemas.py</a></td>
                <td>188</td>
                <td>41</td>
                <td>0</td>
                <td>26</td>
                <td>0</td>
                <td class="right" data-ratio="147 214">68.69%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_electrical_schemas_py.html">core\schemas\electrical_schemas.py</a></td>
                <td>409</td>
                <td>30</td>
                <td>0</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="379 427">88.76%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_error_py.html">core\schemas\error.py</a></td>
                <td>18</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="18 18">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_heat_tracing_schemas_py.html">core\schemas\heat_tracing_schemas.py</a></td>
                <td>330</td>
                <td>47</td>
                <td>0</td>
                <td>30</td>
                <td>0</td>
                <td class="right" data-ratio="283 360">78.61%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_project_schemas_py.html">core\schemas\project_schemas.py</a></td>
                <td>116</td>
                <td>34</td>
                <td>1</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="82 134">61.19%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_switchboard_schemas_py.html">core\schemas\switchboard_schemas.py</a></td>
                <td>156</td>
                <td>15</td>
                <td>0</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="141 166">84.94%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b29dd34b81637e5_user_schemas_py.html">core\schemas\user_schemas.py</a></td>
                <td>90</td>
                <td>10</td>
                <td>70</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="80 96">83.33%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_activity_log_service_py.html">core\services\activity_log_service.py</a></td>
                <td>264</td>
                <td>225</td>
                <td>26</td>
                <td>60</td>
                <td>0</td>
                <td class="right" data-ratio="39 324">12.04%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_component_service_py.html">core\services\component_service.py</a></td>
                <td>245</td>
                <td>217</td>
                <td>21</td>
                <td>72</td>
                <td>0</td>
                <td class="right" data-ratio="28 317">8.83%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_dependencies_py.html">core\services\dependencies.py</a></td>
                <td>20</td>
                <td>12</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="8 20">40.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_document_service_py.html">core\services\document_service.py</a></td>
                <td>332</td>
                <td>290</td>
                <td>33</td>
                <td>82</td>
                <td>0</td>
                <td class="right" data-ratio="42 414">10.14%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_electrical_service_py.html">core\services\electrical_service.py</a></td>
                <td>228</td>
                <td>201</td>
                <td>17</td>
                <td>52</td>
                <td>0</td>
                <td class="right" data-ratio="27 280">9.64%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_heat_tracing_service_py.html">core\services\heat_tracing_service.py</a></td>
                <td>339</td>
                <td>300</td>
                <td>30</td>
                <td>64</td>
                <td>0</td>
                <td class="right" data-ratio="39 403">9.68%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_project_service_py.html">core\services\project_service.py</a></td>
                <td>169</td>
                <td>149</td>
                <td>17</td>
                <td>44</td>
                <td>0</td>
                <td class="right" data-ratio="20 213">9.39%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_switchboard_service_py.html">core\services\switchboard_service.py</a></td>
                <td>220</td>
                <td>191</td>
                <td>22</td>
                <td>46</td>
                <td>0</td>
                <td class="right" data-ratio="29 266">10.90%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_user_service_py.html">core\services\user_service.py</a></td>
                <td>227</td>
                <td>191</td>
                <td>129</td>
                <td>46</td>
                <td>0</td>
                <td class="right" data-ratio="36 273">13.19%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5819abf34aa6dee0___init___py.html">core\standards\__init__.py</a></td>
                <td>9</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="9 9">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6ae03e9e7000b4b1___init___py.html">core\standards\api\__init__.py</a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6ae03e9e7000b4b1_api_rp_14f_py.html">core\standards\api\api_rp_14f.py</a></td>
                <td>54</td>
                <td>41</td>
                <td>5</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="13 60">21.67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6ae03e9e7000b4b1_api_rp_14fz_py.html">core\standards\api\api_rp_14fz.py</a></td>
                <td>51</td>
                <td>38</td>
                <td>5</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="13 55">23.64%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6ae03e9e7000b4b1_api_standards_py.html">core\standards\api\api_standards.py</a></td>
                <td>40</td>
                <td>30</td>
                <td>2</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="10 48">20.83%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_dbac4bf9b4599789___init___py.html">core\standards\general\__init__.py</a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_dbac4bf9b4599789_base_standard_py.html">core\standards\general\base_standard.py</a></td>
                <td>110</td>
                <td>66</td>
                <td>53</td>
                <td>24</td>
                <td>0</td>
                <td class="right" data-ratio="44 134">32.84%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_dbac4bf9b4599789_standards_calculator_py.html">core\standards\general\standards_calculator.py</a></td>
                <td>173</td>
                <td>148</td>
                <td>11</td>
                <td>28</td>
                <td>0</td>
                <td class="right" data-ratio="25 201">12.44%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_dbac4bf9b4599789_standards_validator_py.html">core\standards\general\standards_validator.py</a></td>
                <td>148</td>
                <td>126</td>
                <td>12</td>
                <td>42</td>
                <td>0</td>
                <td class="right" data-ratio="22 190">11.58%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3850ffddfe46d663___init___py.html">core\standards\iec\__init__.py</a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3850ffddfe46d663_iec_60079_py.html">core\standards\iec\iec_60079.py</a></td>
                <td>53</td>
                <td>40</td>
                <td>5</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="13 59">22.03%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3850ffddfe46d663_iec_62395_py.html">core\standards\iec\iec_62395.py</a></td>
                <td>52</td>
                <td>39</td>
                <td>5</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="13 58">22.41%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3850ffddfe46d663_iec_standards_py.html">core\standards\iec\iec_standards.py</a></td>
                <td>96</td>
                <td>82</td>
                <td>6</td>
                <td>22</td>
                <td>0</td>
                <td class="right" data-ratio="14 118">11.86%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_acb3a9c72f1ecd2d___init___py.html">core\standards\iec_60079_30_1\__init__.py</a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_acb3a9c72f1ecd2d_hazardous_area_compliance_py.html">core\standards\iec_60079_30_1\hazardous_area_compliance.py</a></td>
                <td>66</td>
                <td>66</td>
                <td>3</td>
                <td>34</td>
                <td>0</td>
                <td class="right" data-ratio="0 100">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_acb3a9c72f1ecd2d_temperature_class_limits_py.html">core\standards\iec_60079_30_1\temperature_class_limits.py</a></td>
                <td>36</td>
                <td>36</td>
                <td>4</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 44">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_11fc723c286b0310___init___py.html">core\standards\ieee\__init__.py</a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_11fc723c286b0310_ieee_515_py.html">core\standards\ieee\ieee_515.py</a></td>
                <td>144</td>
                <td>125</td>
                <td>7</td>
                <td>44</td>
                <td>0</td>
                <td class="right" data-ratio="19 188">10.11%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_11fc723c286b0310_ieee_844_py.html">core\standards\ieee\ieee_844.py</a></td>
                <td>132</td>
                <td>112</td>
                <td>6</td>
                <td>34</td>
                <td>0</td>
                <td class="right" data-ratio="20 166">12.05%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_11fc723c286b0310_ieee_standards_py.html">core\standards\ieee\ieee_standards.py</a></td>
                <td>97</td>
                <td>83</td>
                <td>6</td>
                <td>32</td>
                <td>0</td>
                <td class="right" data-ratio="14 129">10.85%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8199a77f46f683ea___init___py.html">core\standards\iso\__init__.py</a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8199a77f46f683ea_iso_13623_py.html">core\standards\iso\iso_13623.py</a></td>
                <td>15</td>
                <td>5</td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="10 15">66.67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8199a77f46f683ea_iso_14692_py.html">core\standards\iso\iso_14692.py</a></td>
                <td>15</td>
                <td>5</td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="10 15">66.67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8199a77f46f683ea_iso_standards_py.html">core\standards\iso\iso_standards.py</a></td>
                <td>10</td>
                <td>2</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="8 10">80.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7d8fbd68730761fe___init___py.html">core\standards\nfpa\__init__.py</a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7d8fbd68730761fe_nfpa_497_py.html">core\standards\nfpa\nfpa_497.py</a></td>
                <td>15</td>
                <td>5</td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="10 15">66.67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7d8fbd68730761fe_nfpa_70_py.html">core\standards\nfpa\nfpa_70.py</a></td>
                <td>15</td>
                <td>5</td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="10 15">66.67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7d8fbd68730761fe_nfpa_standards_py.html">core\standards\nfpa\nfpa_standards.py</a></td>
                <td>10</td>
                <td>2</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="8 10">80.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5819abf34aa6dee0_standards_manager_py.html">core\standards\standards_manager.py</a></td>
                <td>181</td>
                <td>141</td>
                <td>8</td>
                <td>62</td>
                <td>0</td>
                <td class="right" data-ratio="40 243">16.46%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5819abf34aa6dee0_standards_service_py.html">core\standards\standards_service.py</a></td>
                <td>186</td>
                <td>162</td>
                <td>9</td>
                <td>44</td>
                <td>0</td>
                <td class="right" data-ratio="24 230">10.43%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e19f0f1c06c261bc___init___py.html">core\standards\tr_50410\__init__.py</a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e19f0f1c06c261bc_heat_loss_factors_py.html">core\standards\tr_50410\heat_loss_factors.py</a></td>
                <td>23</td>
                <td>23</td>
                <td>1</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 31">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8e66a5447a3433f3___init___py.html">core\utils\__init__.py</a></td>
                <td>9</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="9 9">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8e66a5447a3433f3_crud_endpoint_factory_py.html">core\utils\crud_endpoint_factory.py</a></td>
                <td>123</td>
                <td>102</td>
                <td>0</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="21 137">15.33%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8e66a5447a3433f3_datetime_utils_py.html">core\utils\datetime_utils.py</a></td>
                <td>94</td>
                <td>69</td>
                <td>1</td>
                <td>36</td>
                <td>0</td>
                <td class="right" data-ratio="25 130">19.23%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8e66a5447a3433f3_file_io_utils_py.html">core\utils\file_io_utils.py</a></td>
                <td>163</td>
                <td>132</td>
                <td>6</td>
                <td>42</td>
                <td>0</td>
                <td class="right" data-ratio="31 205">15.12%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8e66a5447a3433f3_json_validation_py.html">core\utils\json_validation.py</a></td>
                <td>135</td>
                <td>107</td>
                <td>0</td>
                <td>54</td>
                <td>0</td>
                <td class="right" data-ratio="28 189">14.81%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8e66a5447a3433f3_pagination_utils_py.html">core\utils\pagination_utils.py</a></td>
                <td>100</td>
                <td>60</td>
                <td>0</td>
                <td>38</td>
                <td>0</td>
                <td class="right" data-ratio="40 138">28.99%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8e66a5447a3433f3_query_utils_py.html">core\utils\query_utils.py</a></td>
                <td>167</td>
                <td>144</td>
                <td>0</td>
                <td>102</td>
                <td>0</td>
                <td class="right" data-ratio="23 269">8.55%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8e66a5447a3433f3_string_utils_py.html">core\utils\string_utils.py</a></td>
                <td>97</td>
                <td>76</td>
                <td>0</td>
                <td>46</td>
                <td>0</td>
                <td class="right" data-ratio="21 143">14.69%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8e66a5447a3433f3_unit_conversion_utils_py.html">core\utils\unit_conversion_utils.py</a></td>
                <td>116</td>
                <td>116</td>
                <td>1</td>
                <td>56</td>
                <td>0</td>
                <td class="right" data-ratio="0 172">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8e66a5447a3433f3_uuid_utils_py.html">core\utils\uuid_utils.py</a></td>
                <td>45</td>
                <td>29</td>
                <td>0</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="16 57">28.07%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td>15930</td>
                <td>11383</td>
                <td>1563</td>
                <td>3374</td>
                <td>5</td>
                <td class="right" data-ratio="4554 19304">23.59%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.0">coverage.py v7.8.0</a>,
            created at 2025-06-03 23:24 +0300
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href="z_8e66a5447a3433f3_uuid_utils_py.html"></a>
        <a id="nextFileLink" class="nav" href="z_10fae538ba4e8521_dependencies_py.html"></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
