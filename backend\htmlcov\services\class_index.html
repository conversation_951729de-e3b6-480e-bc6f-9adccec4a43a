<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Ultimate Electrical Designer Backend Coverage Report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_718ce007.css" type="text/css">
    <script src="coverage_html_cb_497bf287.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Ultimate Electrical Designer Backend Coverage Report:
            <span class="pc_cov">10.68%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>n</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>b</kbd>
                        <kbd>p</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button" href="index.html">Files</a>
                <a class="button" href="function_index.html">Functions</a>
                <a class="button current">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.0">coverage.py v7.8.0</a>,
            created at 2025-06-04 21:30 +0300
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="region" class="name left" aria-sort="none" data-default-sort-order="ascending" data-shortcut="n">class<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="branches" aria-sort="none" data-default-sort-order="descending" data-shortcut="b">branches<span class="arrows"></span></th>
                <th id="partial" aria-sort="none" data-default-sort-order="descending" data-shortcut="p">partial<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_activity_log_service_py.html#t88">core\services\activity_log_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_activity_log_service_py.html#t88"><data value='ActivityLogService'>ActivityLogService</data></a></td>
                <td>219</td>
                <td>219</td>
                <td>26</td>
                <td>60</td>
                <td>0</td>
                <td class="right" data-ratio="0 279">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_activity_log_service_py.html">core\services\activity_log_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_activity_log_service_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>45</td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="39 45">86.67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_component_service_py.html#t50">core\services\component_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_component_service_py.html#t50"><data value='ComponentService'>ComponentService</data></a></td>
                <td>159</td>
                <td>159</td>
                <td>14</td>
                <td>58</td>
                <td>0</td>
                <td class="right" data-ratio="0 217">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_component_service_py.html#t632">core\services\component_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_component_service_py.html#t632"><data value='ComponentCategoryService'>ComponentCategoryService</data></a></td>
                <td>58</td>
                <td>58</td>
                <td>7</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 72">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_component_service_py.html">core\services\component_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_component_service_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>28</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="28 28">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_dependencies_py.html">core\services\dependencies.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_dependencies_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>20</td>
                <td>12</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="8 20">40.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_document_service_py.html#t68">core\services\document_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_document_service_py.html#t68"><data value='DocumentService'>DocumentService</data></a></td>
                <td>290</td>
                <td>290</td>
                <td>33</td>
                <td>82</td>
                <td>0</td>
                <td class="right" data-ratio="0 372">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_document_service_py.html">core\services\document_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_document_service_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>42</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="42 42">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_electrical_service_py.html#t135">core\services\electrical_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_electrical_service_py.html#t135"><data value='ElectricalService'>ElectricalService</data></a></td>
                <td>189</td>
                <td>189</td>
                <td>17</td>
                <td>52</td>
                <td>0</td>
                <td class="right" data-ratio="0 241">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_electrical_service_py.html">core\services\electrical_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_electrical_service_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>39</td>
                <td>12</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="27 39">69.23%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_heat_tracing_service_py.html#t107">core\services\heat_tracing_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_heat_tracing_service_py.html#t107"><data value='HeatTracingService'>HeatTracingService</data></a></td>
                <td>290</td>
                <td>290</td>
                <td>30</td>
                <td>64</td>
                <td>0</td>
                <td class="right" data-ratio="0 354">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_heat_tracing_service_py.html">core\services\heat_tracing_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_heat_tracing_service_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>49</td>
                <td>10</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="39 49">79.59%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_project_service_py.html#t53">core\services\project_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_project_service_py.html#t53"><data value='ProjectService'>ProjectService</data></a></td>
                <td>149</td>
                <td>149</td>
                <td>17</td>
                <td>44</td>
                <td>0</td>
                <td class="right" data-ratio="0 193">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_project_service_py.html">core\services\project_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_project_service_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>20</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="20 20">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_switchboard_service_py.html#t98">core\services\switchboard_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_switchboard_service_py.html#t98"><data value='SwitchboardService'>SwitchboardService</data></a></td>
                <td>181</td>
                <td>181</td>
                <td>22</td>
                <td>46</td>
                <td>0</td>
                <td class="right" data-ratio="0 227">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_switchboard_service_py.html">core\services\switchboard_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_switchboard_service_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>39</td>
                <td>10</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="29 39">74.36%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_user_service_py.html#t101">core\services\user_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_user_service_py.html#t101"><data value='UserService'>UserService</data></a></td>
                <td>182</td>
                <td>182</td>
                <td>121</td>
                <td>46</td>
                <td>0</td>
                <td class="right" data-ratio="0 228">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_user_service_py.html">core\services\user_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_user_service_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>45</td>
                <td>9</td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="36 45">80.00%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td class="name left">&nbsp;</td>
                <td>2044</td>
                <td>1776</td>
                <td>295</td>
                <td>466</td>
                <td>0</td>
                <td class="right" data-ratio="268 2510">10.68%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.0">coverage.py v7.8.0</a>,
            created at 2025-06-04 21:30 +0300
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href=""></a>
        <a id="nextFileLink" class="nav" href=""></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
