<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Ultimate Electrical Designer Backend Coverage Report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_718ce007.css" type="text/css">
    <script src="coverage_html_cb_497bf287.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Ultimate Electrical Designer Backend Coverage Report:
            <span class="pc_cov">10.68%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>n</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>b</kbd>
                        <kbd>p</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button" href="index.html">Files</a>
                <a class="button current">Functions</a>
                <a class="button" href="class_index.html">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.0">coverage.py v7.8.0</a>,
            created at 2025-06-04 21:30 +0300
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="region" class="name left" aria-sort="none" data-default-sort-order="ascending" data-shortcut="n">function<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="branches" aria-sort="none" data-default-sort-order="descending" data-shortcut="b">branches<span class="arrows"></span></th>
                <th id="partial" aria-sort="none" data-default-sort-order="descending" data-shortcut="p">partial<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_activity_log_service_py.html#t96">core\services\activity_log_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_activity_log_service_py.html#t96"><data value='init__'>ActivityLogService.__init__</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_activity_log_service_py.html#t112">core\services\activity_log_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_activity_log_service_py.html#t112"><data value='log_event'>ActivityLogService.log_event</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>2</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 22">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_activity_log_service_py.html#t180">core\services\activity_log_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_activity_log_service_py.html#t180"><data value='log_user_action'>ActivityLogService.log_user_action</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>1</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 22">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_activity_log_service_py.html#t240">core\services\activity_log_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_activity_log_service_py.html#t240"><data value='log_authentication_event'>ActivityLogService.log_authentication_event</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>1</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_activity_log_service_py.html#t287">core\services\activity_log_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_activity_log_service_py.html#t287"><data value='log_system_event'>ActivityLogService.log_system_event</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>1</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_activity_log_service_py.html#t329">core\services\activity_log_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_activity_log_service_py.html#t329"><data value='get_activity_log'>ActivityLogService.get_activity_log</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>1</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_activity_log_service_py.html#t353">core\services\activity_log_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_activity_log_service_py.html#t353"><data value='get_activity_logs'>ActivityLogService.get_activity_logs</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>1</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_activity_log_service_py.html#t403">core\services\activity_log_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_activity_log_service_py.html#t403"><data value='get_user_activity_logs'>ActivityLogService.get_user_activity_logs</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>1</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_activity_log_service_py.html#t435">core\services\activity_log_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_activity_log_service_py.html#t435"><data value='get_entity_activity_logs'>ActivityLogService.get_entity_activity_logs</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_activity_log_service_py.html#t468">core\services\activity_log_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_activity_log_service_py.html#t468"><data value='get_recent_activity'>ActivityLogService.get_recent_activity</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_activity_log_service_py.html#t483">core\services\activity_log_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_activity_log_service_py.html#t483"><data value='search_activity_logs'>ActivityLogService.search_activity_logs</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_activity_log_service_py.html#t508">core\services\activity_log_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_activity_log_service_py.html#t508"><data value='get_security_events'>ActivityLogService.get_security_events</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>1</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_activity_log_service_py.html#t540">core\services\activity_log_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_activity_log_service_py.html#t540"><data value='log_security_event'>ActivityLogService.log_security_event</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>1</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_activity_log_service_py.html#t601">core\services\activity_log_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_activity_log_service_py.html#t601"><data value='generate_audit_report'>ActivityLogService.generate_audit_report</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_activity_log_service_py.html#t689">core\services\activity_log_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_activity_log_service_py.html#t689"><data value='generate_report_summary'>ActivityLogService._generate_report_summary</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 25">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_activity_log_service_py.html#t745">core\services\activity_log_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_activity_log_service_py.html#t745"><data value='get_user_activity_summary'>ActivityLogService.get_user_activity_summary</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>1</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_activity_log_service_py.html#t786">core\services\activity_log_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_activity_log_service_py.html#t786"><data value='update_activity_log'>ActivityLogService.update_activity_log</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>2</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 22">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_activity_log_service_py.html#t839">core\services\activity_log_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_activity_log_service_py.html#t839"><data value='delete_old_activity_logs'>ActivityLogService.delete_old_activity_logs</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>3</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_activity_log_service_py.html#t881">core\services\activity_log_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_activity_log_service_py.html#t881"><data value='get_activity_logs_paginated'>ActivityLogService.get_activity_logs_paginated</data></a></td>
                <td>35</td>
                <td>35</td>
                <td>2</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 51">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_activity_log_service_py.html#t986">core\services\activity_log_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_activity_log_service_py.html#t986"><data value='log_event_enhanced'>ActivityLogService.log_event_enhanced</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_activity_log_service_py.html">core\services\activity_log_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_activity_log_service_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>45</td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="39 45">86.67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_component_service_py.html#t58">core\services\component_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_component_service_py.html#t58"><data value='init__'>ComponentService.__init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_component_service_py.html#t74">core\services\component_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_component_service_py.html#t74"><data value='create_component'>ComponentService.create_component</data></a></td>
                <td>30</td>
                <td>30</td>
                <td>2</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 40">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_component_service_py.html#t169">core\services\component_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_component_service_py.html#t169"><data value='get_component_details'>ComponentService.get_component_details</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_component_service_py.html#t207">core\services\component_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_component_service_py.html#t207"><data value='update_component'>ComponentService.update_component</data></a></td>
                <td>35</td>
                <td>35</td>
                <td>3</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 49">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_component_service_py.html#t297">core\services\component_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_component_service_py.html#t297"><data value='delete_component'>ComponentService.delete_component</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_component_service_py.html#t346">core\services\component_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_component_service_py.html#t346"><data value='get_components_list'>ComponentService.get_components_list</data></a></td>
                <td>22</td>
                <td>22</td>
                <td>2</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 32">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_component_service_py.html#t442">core\services\component_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_component_service_py.html#t442"><data value='get_components_paginated'>ComponentService.get_components_paginated</data></a></td>
                <td>27</td>
                <td>27</td>
                <td>2</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 37">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_component_service_py.html#t553">core\services\component_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_component_service_py.html#t553"><data value='validate_component_creation'>ComponentService._validate_component_creation</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_component_service_py.html#t585">core\services\component_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_component_service_py.html#t585"><data value='validate_component_update'>ComponentService._validate_component_update</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_component_service_py.html#t639">core\services\component_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_component_service_py.html#t639"><data value='init__'>ComponentCategoryService.__init__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_component_service_py.html#t649">core\services\component_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_component_service_py.html#t649"><data value='create_category'>ComponentCategoryService.create_category</data></a></td>
                <td>19</td>
                <td>19</td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_component_service_py.html#t716">core\services\component_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_component_service_py.html#t716"><data value='get_category_details'>ComponentCategoryService.get_category_details</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_component_service_py.html#t753">core\services\component_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_component_service_py.html#t753"><data value='get_categories_list'>ComponentCategoryService.get_categories_list</data></a></td>
                <td>20</td>
                <td>20</td>
                <td>2</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 28">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_component_service_py.html#t840">core\services\component_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_component_service_py.html#t840"><data value='validate_category_creation'>ComponentCategoryService._validate_category_creation</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_component_service_py.html">core\services\component_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_component_service_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>28</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="28 28">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_dependencies_py.html#t24">core\services\dependencies.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_dependencies_py.html#t24"><data value='get_project_service'>get_project_service</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_dependencies_py.html#t40">core\services\dependencies.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_dependencies_py.html#t40"><data value='get_user_service'>get_user_service</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_dependencies_py.html#t56">core\services\dependencies.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_dependencies_py.html#t56"><data value='get_component_service'>get_component_service</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_dependencies_py.html#t74">core\services\dependencies.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_dependencies_py.html#t74"><data value='get_component_category_service'>get_component_category_service</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_dependencies_py.html#t90">core\services\dependencies.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_dependencies_py.html#t90"><data value='get_heat_tracing_service'>get_heat_tracing_service</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_dependencies_py.html#t106">core\services\dependencies.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_dependencies_py.html#t106"><data value='get_document_service'>get_document_service</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_dependencies_py.html">core\services\dependencies.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_dependencies_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="8 8">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_document_service_py.html#t76">core\services\document_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_document_service_py.html#t76"><data value='init__'>DocumentService.__init__</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_document_service_py.html#t105">core\services\document_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_document_service_py.html#t105"><data value='create_imported_data_revision'>DocumentService.create_imported_data_revision</data></a></td>
                <td>35</td>
                <td>35</td>
                <td>2</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 51">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_document_service_py.html#t213">core\services\document_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_document_service_py.html#t213"><data value='get_imported_data_revision_by_id'>DocumentService.get_imported_data_revision_by_id</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>1</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_document_service_py.html#t252">core\services\document_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_document_service_py.html#t252"><data value='get_imported_data_revisions_by_project'>DocumentService.get_imported_data_revisions_by_project</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_document_service_py.html#t314">core\services\document_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_document_service_py.html#t314"><data value='update_imported_data_revision'>DocumentService.update_imported_data_revision</data></a></td>
                <td>22</td>
                <td>22</td>
                <td>3</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 28">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_document_service_py.html#t399">core\services\document_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_document_service_py.html#t399"><data value='delete_imported_data_revision'>DocumentService.delete_imported_data_revision</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_document_service_py.html#t458">core\services\document_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_document_service_py.html#t458"><data value='create_exported_document'>DocumentService.create_exported_document</data></a></td>
                <td>23</td>
                <td>23</td>
                <td>2</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 31">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_document_service_py.html#t538">core\services\document_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_document_service_py.html#t538"><data value='get_exported_document_by_id'>DocumentService.get_exported_document_by_id</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>1</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_document_service_py.html#t577">core\services\document_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_document_service_py.html#t577"><data value='get_exported_documents_by_project'>DocumentService.get_exported_documents_by_project</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_document_service_py.html#t643">core\services\document_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_document_service_py.html#t643"><data value='validate_imported_data_revision_creation'>DocumentService._validate_imported_data_revision_creation</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_document_service_py.html#t660">core\services\document_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_document_service_py.html#t660"><data value='validate_imported_data_revision_update'>DocumentService._validate_imported_data_revision_update</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_document_service_py.html#t693">core\services\document_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_document_service_py.html#t693"><data value='validate_imported_data_revision_deletion'>DocumentService._validate_imported_data_revision_deletion</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_document_service_py.html#t721">core\services\document_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_document_service_py.html#t721"><data value='validate_exported_document_creation'>DocumentService._validate_exported_document_creation</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_document_service_py.html#t742">core\services\document_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_document_service_py.html#t742"><data value='create_calculation_standard'>DocumentService.create_calculation_standard</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_document_service_py.html#t804">core\services\document_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_document_service_py.html#t804"><data value='get_calculation_standard_by_id'>DocumentService.get_calculation_standard_by_id</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>1</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_document_service_py.html#t843">core\services\document_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_document_service_py.html#t843"><data value='get_calculation_standard_by_code'>DocumentService.get_calculation_standard_by_code</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>1</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_document_service_py.html#t884">core\services\document_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_document_service_py.html#t884"><data value='get_all_calculation_standards'>DocumentService.get_all_calculation_standards</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_document_service_py.html#t926">core\services\document_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_document_service_py.html#t926"><data value='update_calculation_standard'>DocumentService.update_calculation_standard</data></a></td>
                <td>20</td>
                <td>20</td>
                <td>3</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 24">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_document_service_py.html#t997">core\services\document_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_document_service_py.html#t997"><data value='delete_calculation_standard'>DocumentService.delete_calculation_standard</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_document_service_py.html#t1048">core\services\document_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_document_service_py.html#t1048"><data value='validate_calculation_standard_creation'>DocumentService._validate_calculation_standard_creation</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_document_service_py.html#t1080">core\services\document_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_document_service_py.html#t1080"><data value='validate_calculation_standard_update'>DocumentService._validate_calculation_standard_update</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_document_service_py.html#t1112">core\services\document_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_document_service_py.html#t1112"><data value='get_imported_data_revisions_paginated'>DocumentService.get_imported_data_revisions_paginated</data></a></td>
                <td>24</td>
                <td>24</td>
                <td>2</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 32">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_document_service_py.html#t1216">core\services\document_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_document_service_py.html#t1216"><data value='create_imported_data_revision_enhanced'>DocumentService.create_imported_data_revision_enhanced</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>2</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 24">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_document_service_py.html">core\services\document_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_document_service_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>42</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="42 42">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_electrical_service_py.html#t144">core\services\electrical_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_electrical_service_py.html#t144"><data value='init__'>ElectricalService.__init__</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_electrical_service_py.html#t161">core\services\electrical_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_electrical_service_py.html#t161"><data value='perform_cable_sizing_calculation'>ElectricalService.perform_cable_sizing_calculation</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_electrical_service_py.html#t230">core\services\electrical_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_electrical_service_py.html#t230"><data value='perform_voltage_drop_calculation'>ElectricalService.perform_voltage_drop_calculation</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_electrical_service_py.html#t325">core\services\electrical_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_electrical_service_py.html#t325"><data value='validate_electrical_standards'>ElectricalService.validate_electrical_standards</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_electrical_service_py.html#t402">core\services\electrical_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_electrical_service_py.html#t402"><data value='generate_recommendations'>ElectricalService._generate_recommendations</data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 29">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_electrical_service_py.html#t451">core\services\electrical_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_electrical_service_py.html#t451"><data value='calculate_load_for_electrical_node'>ElectricalService.calculate_load_for_electrical_node</data></a></td>
                <td>26</td>
                <td>26</td>
                <td>2</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 32">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_electrical_service_py.html#t573">core\services\electrical_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_electrical_service_py.html#t573"><data value='optimize_cable_route'>ElectricalService.optimize_cable_route</data></a></td>
                <td>26</td>
                <td>26</td>
                <td>2</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 34">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_electrical_service_py.html#t713">core\services\electrical_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_electrical_service_py.html#t713"><data value='perform_electrical_design_workflow'>ElectricalService.perform_electrical_design_workflow</data></a></td>
                <td>28</td>
                <td>28</td>
                <td>2</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 40">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_electrical_service_py.html#t823">core\services\electrical_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_electrical_service_py.html#t823"><data value='create_electrical_node_enhanced'>ElectricalService.create_electrical_node_enhanced</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>2</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 26">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_electrical_service_py.html#t876">core\services\electrical_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_electrical_service_py.html#t876"><data value='get_electrical_nodes_paginated'>ElectricalService.get_electrical_nodes_paginated</data></a></td>
                <td>24</td>
                <td>24</td>
                <td>2</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 32">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_electrical_service_py.html">core\services\electrical_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_electrical_service_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>39</td>
                <td>12</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="27 39">69.23%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_heat_tracing_service_py.html#t116">core\services\heat_tracing_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_heat_tracing_service_py.html#t116"><data value='init__'>HeatTracingService.__init__</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_heat_tracing_service_py.html#t141">core\services\heat_tracing_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_heat_tracing_service_py.html#t141"><data value='create_pipe'>HeatTracingService.create_pipe</data></a></td>
                <td>21</td>
                <td>21</td>
                <td>2</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 27">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_heat_tracing_service_py.html#t207">core\services\heat_tracing_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_heat_tracing_service_py.html#t207"><data value='get_pipe_details'>HeatTracingService.get_pipe_details</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_heat_tracing_service_py.html#t245">core\services\heat_tracing_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_heat_tracing_service_py.html#t245"><data value='update_pipe'>HeatTracingService.update_pipe</data></a></td>
                <td>24</td>
                <td>24</td>
                <td>3</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 28">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_heat_tracing_service_py.html#t312">core\services\heat_tracing_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_heat_tracing_service_py.html#t312"><data value='delete_pipe'>HeatTracingService.delete_pipe</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_heat_tracing_service_py.html#t359">core\services\heat_tracing_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_heat_tracing_service_py.html#t359"><data value='get_pipes_list'>HeatTracingService.get_pipes_list</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_heat_tracing_service_py.html#t417">core\services\heat_tracing_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_heat_tracing_service_py.html#t417"><data value='get_pipes_paginated'>HeatTracingService.get_pipes_paginated</data></a></td>
                <td>25</td>
                <td>25</td>
                <td>2</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 33">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_heat_tracing_service_py.html#t523">core\services\heat_tracing_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_heat_tracing_service_py.html#t523"><data value='create_vessel'>HeatTracingService.create_vessel</data></a></td>
                <td>21</td>
                <td>21</td>
                <td>2</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 27">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_heat_tracing_service_py.html#t590">core\services\heat_tracing_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_heat_tracing_service_py.html#t590"><data value='get_vessel_details'>HeatTracingService.get_vessel_details</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_heat_tracing_service_py.html#t629">core\services\heat_tracing_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_heat_tracing_service_py.html#t629"><data value='get_vessels_list'>HeatTracingService.get_vessels_list</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_heat_tracing_service_py.html#t691">core\services\heat_tracing_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_heat_tracing_service_py.html#t691"><data value='calculate_pipe_heat_loss'>HeatTracingService.calculate_pipe_heat_loss</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>2</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 20">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_heat_tracing_service_py.html#t756">core\services\heat_tracing_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_heat_tracing_service_py.html#t756"><data value='validate_standards_compliance'>HeatTracingService.validate_standards_compliance</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_heat_tracing_service_py.html#t796">core\services\heat_tracing_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_heat_tracing_service_py.html#t796"><data value='execute_heat_tracing_design'>HeatTracingService.execute_heat_tracing_design</data></a></td>
                <td>41</td>
                <td>41</td>
                <td>4</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 49">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_heat_tracing_service_py.html#t903">core\services\heat_tracing_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_heat_tracing_service_py.html#t903"><data value='get_project_heat_tracing_summary'>HeatTracingService.get_project_heat_tracing_summary</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_heat_tracing_service_py.html#t928">core\services\heat_tracing_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_heat_tracing_service_py.html#t928"><data value='get_project_design_readiness'>HeatTracingService.get_project_design_readiness</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_heat_tracing_service_py.html#t957">core\services\heat_tracing_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_heat_tracing_service_py.html#t957"><data value='validate_pipe_creation'>HeatTracingService._validate_pipe_creation</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_heat_tracing_service_py.html#t984">core\services\heat_tracing_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_heat_tracing_service_py.html#t984"><data value='validate_pipe_update'>HeatTracingService._validate_pipe_update</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_heat_tracing_service_py.html#t1018">core\services\heat_tracing_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_heat_tracing_service_py.html#t1018"><data value='validate_vessel_creation'>HeatTracingService._validate_vessel_creation</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_heat_tracing_service_py.html#t1051">core\services\heat_tracing_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_heat_tracing_service_py.html#t1051"><data value='perform_heat_loss_calculation'>HeatTracingService._perform_heat_loss_calculation</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_heat_tracing_service_py.html#t1097">core\services\heat_tracing_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_heat_tracing_service_py.html#t1097"><data value='perform_standards_validation'>HeatTracingService._perform_standards_validation</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_heat_tracing_service_py.html#t1137">core\services\heat_tracing_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_heat_tracing_service_py.html#t1137"><data value='design_pipe_heat_tracing'>HeatTracingService._design_pipe_heat_tracing</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_heat_tracing_service_py.html#t1213">core\services\heat_tracing_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_heat_tracing_service_py.html#t1213"><data value='design_vessel_heat_tracing'>HeatTracingService._design_vessel_heat_tracing</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_heat_tracing_service_py.html#t1294">core\services\heat_tracing_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_heat_tracing_service_py.html#t1294"><data value='generate_design_summary'>HeatTracingService._generate_design_summary</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_heat_tracing_service_py.html">core\services\heat_tracing_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_heat_tracing_service_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>49</td>
                <td>10</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="39 49">79.59%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_project_service_py.html#t61">core\services\project_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_project_service_py.html#t61"><data value='init__'>ProjectService.__init__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_project_service_py.html#t73">core\services\project_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_project_service_py.html#t73"><data value='create_project'>ProjectService.create_project</data></a></td>
                <td>26</td>
                <td>26</td>
                <td>3</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 30">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_project_service_py.html#t173">core\services\project_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_project_service_py.html#t173"><data value='get_project_details'>ProjectService.get_project_details</data></a></td>
                <td>20</td>
                <td>20</td>
                <td>3</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 26">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_project_service_py.html#t226">core\services\project_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_project_service_py.html#t226"><data value='update_project'>ProjectService.update_project</data></a></td>
                <td>29</td>
                <td>29</td>
                <td>3</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 37">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_project_service_py.html#t319">core\services\project_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_project_service_py.html#t319"><data value='delete_project'>ProjectService.delete_project</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_project_service_py.html#t368">core\services\project_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_project_service_py.html#t368"><data value='get_projects_list'>ProjectService.get_projects_list</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>2</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 20">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_project_service_py.html#t437">core\services\project_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_project_service_py.html#t437"><data value='get_projects_paginated'>ProjectService.get_projects_paginated</data></a></td>
                <td>20</td>
                <td>20</td>
                <td>2</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 26">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_project_service_py.html#t522">core\services\project_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_project_service_py.html#t522"><data value='get_project_by_id_or_code'>ProjectService._get_project_by_id_or_code</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>1</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_project_service_py.html#t551">core\services\project_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_project_service_py.html#t551"><data value='validate_project_creation'>ProjectService._validate_project_creation</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_project_service_py.html#t577">core\services\project_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_project_service_py.html#t577"><data value='validate_project_update'>ProjectService._validate_project_update</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_project_service_py.html">core\services\project_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_project_service_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>20</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="20 20">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_switchboard_service_py.html#t107">core\services\switchboard_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_switchboard_service_py.html#t107"><data value='init__'>SwitchboardService.__init__</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_switchboard_service_py.html#t127">core\services\switchboard_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_switchboard_service_py.html#t127"><data value='create_switchboard'>SwitchboardService.create_switchboard</data></a></td>
                <td>21</td>
                <td>21</td>
                <td>2</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 29">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_switchboard_service_py.html#t188">core\services\switchboard_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_switchboard_service_py.html#t188"><data value='get_switchboard'>SwitchboardService.get_switchboard</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>1</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_switchboard_service_py.html#t212">core\services\switchboard_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_switchboard_service_py.html#t212"><data value='update_switchboard'>SwitchboardService.update_switchboard</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>2</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 21">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_switchboard_service_py.html#t272">core\services\switchboard_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_switchboard_service_py.html#t272"><data value='delete_switchboard'>SwitchboardService.delete_switchboard</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_switchboard_service_py.html#t317">core\services\switchboard_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_switchboard_service_py.html#t317"><data value='get_switchboards_by_project'>SwitchboardService.get_switchboards_by_project</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_switchboard_service_py.html#t340">core\services\switchboard_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_switchboard_service_py.html#t340"><data value='create_feeder'>SwitchboardService.create_feeder</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_switchboard_service_py.html#t385">core\services\switchboard_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_switchboard_service_py.html#t385"><data value='get_feeder'>SwitchboardService.get_feeder</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>1</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_switchboard_service_py.html#t409">core\services\switchboard_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_switchboard_service_py.html#t409"><data value='get_feeders_by_switchboard'>SwitchboardService.get_feeders_by_switchboard</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_switchboard_service_py.html#t432">core\services\switchboard_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_switchboard_service_py.html#t432"><data value='add_switchboard_component'>SwitchboardService.add_switchboard_component</data></a></td>
                <td>19</td>
                <td>19</td>
                <td>3</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 21">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_switchboard_service_py.html#t504">core\services\switchboard_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_switchboard_service_py.html#t504"><data value='get_switchboard_load_summary'>SwitchboardService.get_switchboard_load_summary</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>1</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_switchboard_service_py.html#t571">core\services\switchboard_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_switchboard_service_py.html#t571"><data value='get_switchboard_capacity_analysis'>SwitchboardService.get_switchboard_capacity_analysis</data></a></td>
                <td>20</td>
                <td>20</td>
                <td>1</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 28">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_switchboard_service_py.html#t643">core\services\switchboard_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_switchboard_service_py.html#t643"><data value='get_switchboards_paginated'>SwitchboardService.get_switchboards_paginated</data></a></td>
                <td>24</td>
                <td>24</td>
                <td>2</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 32">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_switchboard_service_py.html#t749">core\services\switchboard_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_switchboard_service_py.html#t749"><data value='create_switchboard_enhanced'>SwitchboardService.create_switchboard_enhanced</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>2</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 22">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_switchboard_service_py.html">core\services\switchboard_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_switchboard_service_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>39</td>
                <td>10</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="29 39">74.36%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_user_service_py.html#t109">core\services\user_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_user_service_py.html#t109"><data value='init__'>UserService.__init__</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_user_service_py.html#t125">core\services\user_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_user_service_py.html#t125"><data value='create_user'>UserService.create_user</data></a></td>
                <td>24</td>
                <td>24</td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 32">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_user_service_py.html#t186">core\services\user_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_user_service_py.html#t186"><data value='get_user'>UserService.get_user</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>1</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_user_service_py.html#t210">core\services\user_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_user_service_py.html#t210"><data value='get_user_by_email'>UserService.get_user_by_email</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>1</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_user_service_py.html#t234">core\services\user_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_user_service_py.html#t234"><data value='update_user'>UserService.update_user</data></a></td>
                <td>27</td>
                <td>27</td>
                <td>2</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 41">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_user_service_py.html#t300">core\services\user_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_user_service_py.html#t300"><data value='deactivate_user'>UserService.deactivate_user</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_user_service_py.html#t338">core\services\user_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_user_service_py.html#t338"><data value='get_users'>UserService.get_users</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_user_service_py.html#t354">core\services\user_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_user_service_py.html#t354"><data value='get_users_paginated'>UserService.get_users_paginated</data></a></td>
                <td>25</td>
                <td>25</td>
                <td>2</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 33">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_user_service_py.html#t461">core\services\user_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_user_service_py.html#t461"><data value='authenticate_user'>UserService.authenticate_user</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_user_service_py.html#t502">core\services\user_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_user_service_py.html#t502"><data value='generate_access_token'>UserService.generate_access_token</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_user_service_py.html#t531">core\services\user_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_user_service_py.html#t531"><data value='login'>UserService.login</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_user_service_py.html#t574">core\services\user_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_user_service_py.html#t574"><data value='logout'>UserService.logout</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_user_service_py.html#t610">core\services\user_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_user_service_py.html#t610"><data value='change_password'>UserService.change_password</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>59</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_user_service_py.html#t677">core\services\user_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_user_service_py.html#t677"><data value='get_user_preferences'>UserService.get_user_preferences</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>1</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_user_service_py.html#t705">core\services\user_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_user_service_py.html#t705"><data value='create_or_update_user_preferences'>UserService.create_or_update_user_preferences</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_user_service_py.html#t756">core\services\user_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_user_service_py.html#t756"><data value='delete_user_preferences'>UserService.delete_user_preferences</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>3</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_user_service_py.html#t811">core\services\user_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_user_service_py.html#t811"><data value='search_users'>UserService.search_users</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_user_service_py.html#t830">core\services\user_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_user_service_py.html#t830"><data value='count_active_users'>UserService.count_active_users</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_user_service_py.html#t844">core\services\user_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_user_service_py.html#t844"><data value='hash_password'>UserService.hash_password</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>10</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_user_service_py.html#t856">core\services\user_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_user_service_py.html#t856"><data value='verify_password'>UserService.verify_password</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>11</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_user_service_py.html#t869">core\services\user_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_user_service_py.html#t869"><data value='update_user_last_login'>UserService.update_user_last_login</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_42a9bb67bf120292_user_service_py.html">core\services\user_service.py</a></td>
                <td class="name left"><a href="z_42a9bb67bf120292_user_service_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>45</td>
                <td>9</td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="36 45">80.00%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td class="name left">&nbsp;</td>
                <td>2044</td>
                <td>1776</td>
                <td>295</td>
                <td>466</td>
                <td>0</td>
                <td class="right" data-ratio="268 2510">10.68%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.0">coverage.py v7.8.0</a>,
            created at 2025-06-04 21:30 +0300
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href=""></a>
        <a id="nextFileLink" class="nav" href=""></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
