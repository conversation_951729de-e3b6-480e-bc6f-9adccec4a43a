{"note": "This file is an internal implementation detail to speed up HTML report generation. Its format can change at any time. You might be looking for the JSON report: https://coverage.rtfd.io/cmd.html#cmd-json", "format": 5, "version": "7.8.0", "globals": "79c1b1c061a6170d7f22387d292e9492", "files": {"z_42a9bb67bf120292_activity_log_service_py": {"hash": "20f061729c868d1185579314a3563c72", "index": {"url": "z_42a9bb67bf120292_activity_log_service_py.html", "file": "core\\services\\activity_log_service.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 264, "n_excluded": 26, "n_missing": 225, "n_branches": 60, "n_partial_branches": 0, "n_missing_branches": 60}}}, "z_42a9bb67bf120292_component_service_py": {"hash": "dd06b0b08cad2e6866d5b16dd75a4cac", "index": {"url": "z_42a9bb67bf120292_component_service_py.html", "file": "core\\services\\component_service.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 245, "n_excluded": 21, "n_missing": 217, "n_branches": 72, "n_partial_branches": 0, "n_missing_branches": 72}}}, "z_42a9bb67bf120292_dependencies_py": {"hash": "57b057de20965f24313fdf2e6bf28cd2", "index": {"url": "z_42a9bb67bf120292_dependencies_py.html", "file": "core\\services\\dependencies.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 20, "n_excluded": 0, "n_missing": 12, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_42a9bb67bf120292_document_service_py": {"hash": "2c86ff091c90b2db0750409452732d21", "index": {"url": "z_42a9bb67bf120292_document_service_py.html", "file": "core\\services\\document_service.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 332, "n_excluded": 33, "n_missing": 290, "n_branches": 82, "n_partial_branches": 0, "n_missing_branches": 82}}}, "z_42a9bb67bf120292_electrical_service_py": {"hash": "e36e4a009590e6ea73f90c01cfe197df", "index": {"url": "z_42a9bb67bf120292_electrical_service_py.html", "file": "core\\services\\electrical_service.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 228, "n_excluded": 17, "n_missing": 201, "n_branches": 52, "n_partial_branches": 0, "n_missing_branches": 52}}}, "z_42a9bb67bf120292_heat_tracing_service_py": {"hash": "3b2f2851f5d0f34ee8065b2c51a7350f", "index": {"url": "z_42a9bb67bf120292_heat_tracing_service_py.html", "file": "core\\services\\heat_tracing_service.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 339, "n_excluded": 30, "n_missing": 300, "n_branches": 64, "n_partial_branches": 0, "n_missing_branches": 64}}}, "z_42a9bb67bf120292_project_service_py": {"hash": "6a8171a73e44920af6c70ee1bc39d17f", "index": {"url": "z_42a9bb67bf120292_project_service_py.html", "file": "core\\services\\project_service.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 169, "n_excluded": 17, "n_missing": 149, "n_branches": 44, "n_partial_branches": 0, "n_missing_branches": 44}}}, "z_42a9bb67bf120292_switchboard_service_py": {"hash": "b924dbca9d069497b9a33fcfe4d30c87", "index": {"url": "z_42a9bb67bf120292_switchboard_service_py.html", "file": "core\\services\\switchboard_service.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 220, "n_excluded": 22, "n_missing": 191, "n_branches": 46, "n_partial_branches": 0, "n_missing_branches": 46}}}, "z_42a9bb67bf120292_user_service_py": {"hash": "94f4913fa6d7bb5ef7426f9164959ea4", "index": {"url": "z_42a9bb67bf120292_user_service_py.html", "file": "core\\services\\user_service.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 227, "n_excluded": 129, "n_missing": 191, "n_branches": 46, "n_partial_branches": 0, "n_missing_branches": 46}}}}}