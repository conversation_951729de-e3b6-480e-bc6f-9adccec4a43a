{"note": "This file is an internal implementation detail to speed up HTML report generation. Its format can change at any time. You might be looking for the JSON report: https://coverage.rtfd.io/cmd.html#cmd-json", "format": 5, "version": "7.8.0", "globals": "6f9710698caa3e2d98702f65b1e65e9f", "files": {"z_10fae538ba4e8521_dependencies_py": {"hash": "5845fbfcca927ffcad19661d7d25142a", "index": {"url": "z_10fae538ba4e8521_dependencies_py.html", "file": "api\\dependencies.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 16, "n_excluded": 0, "n_missing": 9, "n_branches": 6, "n_partial_branches": 0, "n_missing_branches": 6}}}, "z_10fae538ba4e8521_main_router_py": {"hash": "ea9bba882a3b9c689d282f01ef12aa55", "index": {"url": "z_10fae538ba4e8521_main_router_py.html", "file": "api\\main_router.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 24, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_3c8229fafc2171e7_activity_log_routes_py": {"hash": "68e4c5ca4795ad63b91c112debe521ac", "index": {"url": "z_3c8229fafc2171e7_activity_log_routes_py.html", "file": "api\\v1\\activity_log_routes.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 147, "n_excluded": 18, "n_missing": 108, "n_branches": 12, "n_partial_branches": 0, "n_missing_branches": 12}}}, "z_3c8229fafc2171e7_component_routes_py": {"hash": "eb134f9a6b1c14bd5abcfbd5fb02a6b8", "index": {"url": "z_3c8229fafc2171e7_component_routes_py.html", "file": "api\\v1\\component_routes.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 137, "n_excluded": 14, "n_missing": 108, "n_branches": 2, "n_partial_branches": 0, "n_missing_branches": 2}}}, "z_3c8229fafc2171e7_document_routes_py": {"hash": "1daf23a2c37b6d741a8d109b52441d74", "index": {"url": "z_3c8229fafc2171e7_document_routes_py.html", "file": "api\\v1\\document_routes.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 149, "n_excluded": 16, "n_missing": 102, "n_branches": 12, "n_partial_branches": 0, "n_missing_branches": 12}}}, "z_3c8229fafc2171e7_electrical_routes_py": {"hash": "7c06bc52032394fdd66f2acf412d9db0", "index": {"url": "z_3c8229fafc2171e7_electrical_routes_py.html", "file": "api\\v1\\electrical_routes.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 243, "n_excluded": 32, "n_missing": 192, "n_branches": 36, "n_partial_branches": 0, "n_missing_branches": 36}}}, "z_3c8229fafc2171e7_heat_tracing_routes_py": {"hash": "fca266f737698dbe46ece1ec03d1149f", "index": {"url": "z_3c8229fafc2171e7_heat_tracing_routes_py.html", "file": "api\\v1\\heat_tracing_routes.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 186, "n_excluded": 22, "n_missing": 147, "n_branches": 2, "n_partial_branches": 0, "n_missing_branches": 2}}}, "z_3c8229fafc2171e7_import_export_routes_py": {"hash": "3deedd01700a9aa042e119ec763706fb", "index": {"url": "z_3c8229fafc2171e7_import_export_routes_py.html", "file": "api\\v1\\import_export_routes.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 225, "n_excluded": 12, "n_missing": 167, "n_branches": 8, "n_partial_branches": 0, "n_missing_branches": 8}}}, "z_3c8229fafc2171e7_project_routes_py": {"hash": "b369703f95da744a22208e3536944a1a", "index": {"url": "z_3c8229fafc2171e7_project_routes_py.html", "file": "api\\v1\\project_routes.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 70, "n_excluded": 1, "n_missing": 48, "n_branches": 2, "n_partial_branches": 0, "n_missing_branches": 2}}}, "z_3c8229fafc2171e7_reports_routes_py": {"hash": "c5ec6e790ce37cd39d669609b369258d", "index": {"url": "z_3c8229fafc2171e7_reports_routes_py.html", "file": "api\\v1\\reports_routes.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 168, "n_excluded": 10, "n_missing": 97, "n_branches": 6, "n_partial_branches": 0, "n_missing_branches": 6}}}, "z_3c8229fafc2171e7_standards_routes_py": {"hash": "e590949b07da7dcf9cb415b75f47eaef", "index": {"url": "z_3c8229fafc2171e7_standards_routes_py.html", "file": "api\\v1\\standards_routes.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 148, "n_excluded": 11, "n_missing": 97, "n_branches": 2, "n_partial_branches": 0, "n_missing_branches": 2}}}, "z_3c8229fafc2171e7_switchboard_routes_py": {"hash": "08fc5759784c776cc71d85534f0ae17b", "index": {"url": "z_3c8229fafc2171e7_switchboard_routes_py.html", "file": "api\\v1\\switchboard_routes.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 140, "n_excluded": 17, "n_missing": 104, "n_branches": 12, "n_partial_branches": 0, "n_missing_branches": 12}}}, "z_3c8229fafc2171e7_user_routes_py": {"hash": "da2d84f56bc09bafa81c5f7af1aa063e", "index": {"url": "z_3c8229fafc2171e7_user_routes_py.html", "file": "api\\v1\\user_routes.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 112, "n_excluded": 97, "n_missing": 79, "n_branches": 16, "n_partial_branches": 0, "n_missing_branches": 16}}}, "z_1ea838694151cac8_logging_config_py": {"hash": "94a01f6afb96040d28953c436f1d1282", "index": {"url": "z_1ea838694151cac8_logging_config_py.html", "file": "config\\logging_config.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 47, "n_excluded": 1, "n_missing": 5, "n_branches": 12, "n_partial_branches": 5, "n_missing_branches": 5}}}, "z_1ea838694151cac8_settings_py": {"hash": "067e2881362cb8b6e457ea3f860d2986", "index": {"url": "z_1ea838694151cac8_settings_py.html", "file": "config\\settings.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 38, "n_excluded": 0, "n_missing": 9, "n_branches": 4, "n_partial_branches": 0, "n_missing_branches": 4}}}, "z_d51c265d7ed55fb9___init___py": {"hash": "93828953aff8302115d54dd16565e4c6", "index": {"url": "z_d51c265d7ed55fb9___init___py.html", "file": "core\\calculations\\__init__.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 5, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_d51c265d7ed55fb9_calculation_service_py": {"hash": "9a6965f14ad1401f30426b4a1ed43512", "index": {"url": "z_d51c265d7ed55fb9_calculation_service_py.html", "file": "core\\calculations\\calculation_service.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 415, "n_excluded": 23, "n_missing": 338, "n_branches": 142, "n_partial_branches": 0, "n_missing_branches": 142}}}, "z_0a7bfb953e6da477___init___py": {"hash": "6a7f36db3d58c8ec4d4be56bbb84db14", "index": {"url": "z_0a7bfb953e6da477___init___py.html", "file": "core\\calculations\\circuit_design\\__init__.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 3, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_0a7bfb953e6da477_circuit_breaker_sizing_py": {"hash": "b84edc00af69ae41c4ff0c08e43b2ec9", "index": {"url": "z_0a7bfb953e6da477_circuit_breaker_sizing_py.html", "file": "core\\calculations\\circuit_design\\circuit_breaker_sizing.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 104, "n_excluded": 6, "n_missing": 79, "n_branches": 34, "n_partial_branches": 0, "n_missing_branches": 34}}}, "z_0a7bfb953e6da477_control_circuit_logic_py": {"hash": "90c8ad20aa72283699d85565e30bf79f", "index": {"url": "z_0a7bfb953e6da477_control_circuit_logic_py.html", "file": "core\\calculations\\circuit_design\\control_circuit_logic.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 153, "n_excluded": 5, "n_missing": 117, "n_branches": 44, "n_partial_branches": 0, "n_missing_branches": 44}}}, "z_bf815d1c1bf6cbfb___init___py": {"hash": "472ad1420a0a1867711e91bfda3c63fe", "index": {"url": "z_bf815d1c1bf6cbfb___init___py.html", "file": "core\\calculations\\common_properties\\__init__.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 3, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_bf815d1c1bf6cbfb_fluid_properties_py": {"hash": "2a635451bda7ceedb81f8d38bd27865a", "index": {"url": "z_bf815d1c1bf6cbfb_fluid_properties_py.html", "file": "core\\calculations\\common_properties\\fluid_properties.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 102, "n_excluded": 8, "n_missing": 88, "n_branches": 32, "n_partial_branches": 0, "n_missing_branches": 32}}}, "z_bf815d1c1bf6cbfb_material_data_py": {"hash": "9cbb26bed3d91e7938bb112eab3e97a8", "index": {"url": "z_bf815d1c1bf6cbfb_material_data_py.html", "file": "core\\calculations\\common_properties\\material_data.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 68, "n_excluded": 13, "n_missing": 55, "n_branches": 14, "n_partial_branches": 0, "n_missing_branches": 14}}}, "z_52a2768ca9a74860___init___py": {"hash": "5ff6437b18c069b20509b3e201f4923b", "index": {"url": "z_52a2768ca9a74860___init___py.html", "file": "core\\calculations\\electrical_sizing\\__init__.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 3, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_52a2768ca9a74860_cable_sizing_py": {"hash": "21204776a25c8aafa772f83e824fde49", "index": {"url": "z_52a2768ca9a74860_cable_sizing_py.html", "file": "core\\calculations\\electrical_sizing\\cable_sizing.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 77, "n_excluded": 5, "n_missing": 66, "n_branches": 26, "n_partial_branches": 0, "n_missing_branches": 26}}}, "z_52a2768ca9a74860_voltage_drop_py": {"hash": "6d3a82f19d67779d0114e3cbb437d8a9", "index": {"url": "z_52a2768ca9a74860_voltage_drop_py.html", "file": "core\\calculations\\electrical_sizing\\voltage_drop.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 74, "n_excluded": 8, "n_missing": 64, "n_branches": 26, "n_partial_branches": 0, "n_missing_branches": 26}}}, "z_df3ce16558279790___init___py": {"hash": "3b2928258016e3430194d9460b0fbd39", "index": {"url": "z_df3ce16558279790___init___py.html", "file": "core\\calculations\\heat_loss\\__init__.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 4, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_df3ce16558279790_insulation_properties_py": {"hash": "4d9b904a4aae4252252895498015a2b6", "index": {"url": "z_df3ce16558279790_insulation_properties_py.html", "file": "core\\calculations\\heat_loss\\insulation_properties.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 69, "n_excluded": 7, "n_missing": 56, "n_branches": 20, "n_partial_branches": 0, "n_missing_branches": 20}}}, "z_df3ce16558279790_pipe_heat_loss_py": {"hash": "ae4cf0fcc0524d665ed121ca80d0191f", "index": {"url": "z_df3ce16558279790_pipe_heat_loss_py.html", "file": "core\\calculations\\heat_loss\\pipe_heat_loss.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 96, "n_excluded": 3, "n_missing": 81, "n_branches": 34, "n_partial_branches": 0, "n_missing_branches": 34}}}, "z_df3ce16558279790_vessel_heat_loss_py": {"hash": "41ce5f273dc27d52839d949d321045a9", "index": {"url": "z_df3ce16558279790_vessel_heat_loss_py.html", "file": "core\\calculations\\heat_loss\\vessel_heat_loss.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 62, "n_excluded": 2, "n_missing": 53, "n_branches": 22, "n_partial_branches": 0, "n_missing_branches": 22}}}, "z_e4d2cb2bb52b2279___init___py": {"hash": "91e602a21b30bf66bdea7affe44a1297", "index": {"url": "z_e4d2cb2bb52b2279___init___py.html", "file": "core\\calculations\\utils\\__init__.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 5, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_e4d2cb2bb52b2279_input_parser_py": {"hash": "f76ced18608d49eec5468f457ef8b4c5", "index": {"url": "z_e4d2cb2bb52b2279_input_parser_py.html", "file": "core\\calculations\\utils\\input_parser.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 153, "n_excluded": 15, "n_missing": 134, "n_branches": 70, "n_partial_branches": 0, "n_missing_branches": 70}}}, "z_e4d2cb2bb52b2279_math_helpers_py": {"hash": "64cd09aa9f90c8c7102e2ef11c80d770", "index": {"url": "z_e4d2cb2bb52b2279_math_helpers_py.html", "file": "core\\calculations\\utils\\math_helpers.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 162, "n_excluded": 17, "n_missing": 145, "n_branches": 58, "n_partial_branches": 0, "n_missing_branches": 58}}}, "z_e4d2cb2bb52b2279_units_conversion_py": {"hash": "ed9baefc26aab1b5e51d1031db6bba9e", "index": {"url": "z_e4d2cb2bb52b2279_units_conversion_py.html", "file": "core\\calculations\\utils\\units_conversion.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 146, "n_excluded": 16, "n_missing": 124, "n_branches": 48, "n_partial_branches": 0, "n_missing_branches": 48}}}, "z_e4d2cb2bb52b2279_validation_rules_py": {"hash": "e1b4f1e5985fd20148aa497acd3bdbf2", "index": {"url": "z_e4d2cb2bb52b2279_validation_rules_py.html", "file": "core\\calculations\\utils\\validation_rules.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 120, "n_excluded": 10, "n_missing": 102, "n_branches": 48, "n_partial_branches": 0, "n_missing_branches": 48}}}, "z_dd0cc83923a11228___init___py": {"hash": "f03d3f2e7211a1c6983c1ca515f9e85d", "index": {"url": "z_dd0cc83923a11228___init___py.html", "file": "core\\data_import\\__init__.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 4, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_dd0cc83923a11228_global_importer_py": {"hash": "354d54cb489a1582abb317c2f1704c49", "index": {"url": "z_dd0cc83923a11228_global_importer_py.html", "file": "core\\data_import\\global_importer.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 144, "n_excluded": 12, "n_missing": 123, "n_branches": 46, "n_partial_branches": 0, "n_missing_branches": 46}}}, "z_dd0cc83923a11228_import_service_py": {"hash": "96c2e2149ad180ace9a90319e1401205", "index": {"url": "z_dd0cc83923a11228_import_service_py.html", "file": "core\\data_import\\import_service.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 142, "n_excluded": 12, "n_missing": 121, "n_branches": 36, "n_partial_branches": 0, "n_missing_branches": 36}}}, "z_f712d5acc85bf35f___init___py": {"hash": "94ba6ba3cce88f6001be3fc3de5efb37", "index": {"url": "z_f712d5acc85bf35f___init___py.html", "file": "core\\data_import\\mappers\\__init__.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 3, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_f712d5acc85bf35f_catalog_data_mapper_py": {"hash": "86aa38229ef0ca0f5a0e561f4d0c954e", "index": {"url": "z_f712d5acc85bf35f_catalog_data_mapper_py.html", "file": "core\\data_import\\mappers\\catalog_data_mapper.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 179, "n_excluded": 9, "n_missing": 159, "n_branches": 72, "n_partial_branches": 0, "n_missing_branches": 72}}}, "z_f712d5acc85bf35f_project_data_mapper_py": {"hash": "a2cc4881defe077c9fdaa4a44c87b9c0", "index": {"url": "z_f712d5acc85bf35f_project_data_mapper_py.html", "file": "core\\data_import\\mappers\\project_data_mapper.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 161, "n_excluded": 9, "n_missing": 141, "n_branches": 52, "n_partial_branches": 0, "n_missing_branches": 52}}}, "z_b263df31e4f5f767___init___py": {"hash": "0226f211837c8b907f8ef784ac333a3c", "index": {"url": "z_b263df31e4f5f767___init___py.html", "file": "core\\data_import\\parsers\\__init__.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 4, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_b263df31e4f5f767_csv_parser_py": {"hash": "5a070a10799170bc0bdc71ab6208301d", "index": {"url": "z_b263df31e4f5f767_csv_parser_py.html", "file": "core\\data_import\\parsers\\csv_parser.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 168, "n_excluded": 14, "n_missing": 150, "n_branches": 54, "n_partial_branches": 0, "n_missing_branches": 54}}}, "z_b263df31e4f5f767_json_parser_py": {"hash": "75b1c55fe4425f7ac9f36c8dd50bb853", "index": {"url": "z_b263df31e4f5f767_json_parser_py.html", "file": "core\\data_import\\parsers\\json_parser.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 189, "n_excluded": 13, "n_missing": 171, "n_branches": 68, "n_partial_branches": 0, "n_missing_branches": 68}}}, "z_b263df31e4f5f767_xlsx_parser_py": {"hash": "254c4536726377cde8f7366e13af803e", "index": {"url": "z_b263df31e4f5f767_xlsx_parser_py.html", "file": "core\\data_import\\parsers\\xlsx_parser.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 130, "n_excluded": 10, "n_missing": 113, "n_branches": 38, "n_partial_branches": 0, "n_missing_branches": 38}}}, "z_dd0cc83923a11228_project_importer_py": {"hash": "1ce8f272a8ccc1ae02fc3db07acd74f6", "index": {"url": "z_dd0cc83923a11228_project_importer_py.html", "file": "core\\data_import\\project_importer.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 193, "n_excluded": 14, "n_missing": 168, "n_branches": 56, "n_partial_branches": 0, "n_missing_branches": 56}}}, "z_4343209610adb865___init___py": {"hash": "ffc0d56a6403f02635856b721c6ca80e", "index": {"url": "z_4343209610adb865___init___py.html", "file": "core\\data_import\\validators\\__init__.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 2, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_4343209610adb865_import_data_validator_py": {"hash": "7a424d51b60d1011e2d187664c8320ca", "index": {"url": "z_4343209610adb865_import_data_validator_py.html", "file": "core\\data_import\\validators\\import_data_validator.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 200, "n_excluded": 9, "n_missing": 173, "n_branches": 134, "n_partial_branches": 0, "n_missing_branches": 134}}}, "z_2e48d8fb49f22d07___init___py": {"hash": "5d9a9698f79b8f1e7305eed4d6bb17ac", "index": {"url": "z_2e48d8fb49f22d07___init___py.html", "file": "core\\database\\__init__.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 4, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_2e48d8fb49f22d07_dependencies_py": {"hash": "4cc66b036816a0f6b8f99e15161603e7", "index": {"url": "z_2e48d8fb49f22d07_dependencies_py.html", "file": "core\\database\\dependencies.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 7, "n_excluded": 0, "n_missing": 2, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_2e48d8fb49f22d07_engine_py": {"hash": "d20770cb7ca63960c0873f49242d6f4a", "index": {"url": "z_2e48d8fb49f22d07_engine_py.html", "file": "core\\database\\engine.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 77, "n_excluded": 6, "n_missing": 59, "n_branches": 20, "n_partial_branches": 0, "n_missing_branches": 20}}}, "z_2e48d8fb49f22d07_initialization_py": {"hash": "fadc308dd35e46d04110b023eb871005", "index": {"url": "z_2e48d8fb49f22d07_initialization_py.html", "file": "core\\database\\initialization.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 94, "n_excluded": 12, "n_missing": 73, "n_branches": 6, "n_partial_branches": 0, "n_missing_branches": 6}}}, "z_2e48d8fb49f22d07_session_py": {"hash": "f6cee45512dd1414900d26ed11ccc745", "index": {"url": "z_2e48d8fb49f22d07_session_py.html", "file": "core\\database\\session.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 71, "n_excluded": 7, "n_missing": 50, "n_branches": 12, "n_partial_branches": 0, "n_missing_branches": 12}}}, "z_5d678fac7219f0d6_error_factory_py": {"hash": "0a73d7f529ec3ead1f7ab6e5a19d0b3b", "index": {"url": "z_5d678fac7219f0d6_error_factory_py.html", "file": "core\\errors\\error_factory.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 12, "n_excluded": 0, "n_missing": 6, "n_branches": 2, "n_partial_branches": 0, "n_missing_branches": 2}}}, "z_5d678fac7219f0d6_error_registry_py": {"hash": "0ebadd18f89962eea761987dfc58c526", "index": {"url": "z_5d678fac7219f0d6_error_registry_py.html", "file": "core\\errors\\error_registry.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 3, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_5d678fac7219f0d6_error_templates_py": {"hash": "22d1a3283901385c555ce469f0400cc2", "index": {"url": "z_5d678fac7219f0d6_error_templates_py.html", "file": "core\\errors\\error_templates.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 1, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_5d678fac7219f0d6_exceptions_py": {"hash": "c87c77c818c22883df500bf8f7a67a4c", "index": {"url": "z_5d678fac7219f0d6_exceptions_py.html", "file": "core\\errors\\exceptions.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 43, "n_excluded": 1, "n_missing": 21, "n_branches": 4, "n_partial_branches": 0, "n_missing_branches": 4}}}, "z_f2bf686f1e66ce65___init___py": {"hash": "70a74bb26ca3a08013605e2766da5037", "index": {"url": "z_f2bf686f1e66ce65___init___py.html", "file": "core\\models\\__init__.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 1, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_f2bf686f1e66ce65_activity_log_py": {"hash": "3a06fe02ff7770fbaef9f1ade7a7413a", "index": {"url": "z_f2bf686f1e66ce65_activity_log_py.html", "file": "core\\models\\activity_log.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 13, "n_excluded": 2, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_f2bf686f1e66ce65_base_py": {"hash": "abddb19d482c84252b3897ac018c33c1", "index": {"url": "z_f2bf686f1e66ce65_base_py.html", "file": "core\\models\\base.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 58, "n_excluded": 0, "n_missing": 14, "n_branches": 8, "n_partial_branches": 0, "n_missing_branches": 8}}}, "z_f2bf686f1e66ce65_components_py": {"hash": "5ef0440573eb76c3f7786f999e57bbfa", "index": {"url": "z_f2bf686f1e66ce65_components_py.html", "file": "core\\models\\components.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 21, "n_excluded": 4, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_f2bf686f1e66ce65_documents_py": {"hash": "1dc391f812a0d118ec162746fd2b8452", "index": {"url": "z_f2bf686f1e66ce65_documents_py.html", "file": "core\\models\\documents.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 36, "n_excluded": 9, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_f2bf686f1e66ce65_electrical_py": {"hash": "388075c70e178c178aae828863e9606a", "index": {"url": "z_f2bf686f1e66ce65_electrical_py.html", "file": "core\\models\\electrical.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 129, "n_excluded": 22, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_f2bf686f1e66ce65_enums_py": {"hash": "5970d5cd9229a080f0bd628265a32033", "index": {"url": "z_f2bf686f1e66ce65_enums_py.html", "file": "core\\models\\enums.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 75, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_f2bf686f1e66ce65_heat_tracing_py": {"hash": "09f8e4d0f8876c7022dc4a869d767e4c", "index": {"url": "z_f2bf686f1e66ce65_heat_tracing_py.html", "file": "core\\models\\heat_tracing.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 123, "n_excluded": 19, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_f2bf686f1e66ce65_project_py": {"hash": "01fb512c91580df1a1ecdff3bed69d59", "index": {"url": "z_f2bf686f1e66ce65_project_py.html", "file": "core\\models\\project.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 82, "n_excluded": 3, "n_missing": 37, "n_branches": 28, "n_partial_branches": 0, "n_missing_branches": 28}}}, "z_f2bf686f1e66ce65_switchboard_py": {"hash": "a9752a578d87902c7c0370cbf80439b7", "index": {"url": "z_f2bf686f1e66ce65_switchboard_py.html", "file": "core\\models\\switchboard.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 45, "n_excluded": 8, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_f2bf686f1e66ce65_users_py": {"hash": "6ef5e02256364ead3228d78f71511a5f", "index": {"url": "z_f2bf686f1e66ce65_users_py.html", "file": "core\\models\\users.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 24, "n_excluded": 7, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a6617d05fb563bba___init___py": {"hash": "fde9e374714605f6a1d7b2c2ddc3c1b4", "index": {"url": "z_a6617d05fb563bba___init___py.html", "file": "core\\reports\\__init__.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 4, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a6617d05fb563bba_data_exporter_py": {"hash": "7269d90221441ef5242867951dc4e6ad", "index": {"url": "z_a6617d05fb563bba_data_exporter_py.html", "file": "core\\reports\\data_exporter.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 196, "n_excluded": 12, "n_missing": 171, "n_branches": 78, "n_partial_branches": 0, "n_missing_branches": 78}}}, "z_ded8937c655412b8___init___py": {"hash": "acb6e775f4bfed5df738aabcaebd2964", "index": {"url": "z_ded8937c655412b8___init___py.html", "file": "core\\reports\\data_preparation\\__init__.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 4, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_ded8937c655412b8_calculation_data_processor_py": {"hash": "19dfa41492f3686e5fa20e9e34dc6b22", "index": {"url": "z_ded8937c655412b8_calculation_data_processor_py.html", "file": "core\\reports\\data_preparation\\calculation_data_processor.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 160, "n_excluded": 9, "n_missing": 141, "n_branches": 34, "n_partial_branches": 0, "n_missing_branches": 34}}}, "z_ded8937c655412b8_export_data_formatter_py": {"hash": "6d6dc77e57143e118d07961151572ddd", "index": {"url": "z_ded8937c655412b8_export_data_formatter_py.html", "file": "core\\reports\\data_preparation\\export_data_formatter.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 172, "n_excluded": 6, "n_missing": 143, "n_branches": 64, "n_partial_branches": 0, "n_missing_branches": 64}}}, "z_ded8937c655412b8_report_data_aggregator_py": {"hash": "4b27f7c1e180d2fd7d66347d8790e8b9", "index": {"url": "z_ded8937c655412b8_report_data_aggregator_py.html", "file": "core\\reports\\data_preparation\\report_data_aggregator.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 147, "n_excluded": 9, "n_missing": 127, "n_branches": 34, "n_partial_branches": 0, "n_missing_branches": 34}}}, "z_a6617d05fb563bba_document_generator_py": {"hash": "8d80eafb05b243030aa87debd6622c67", "index": {"url": "z_a6617d05fb563bba_document_generator_py.html", "file": "core\\reports\\document_generator.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 210, "n_excluded": 15, "n_missing": 187, "n_branches": 68, "n_partial_branches": 0, "n_missing_branches": 68}}}, "z_bd29078fd467125e___init___py": {"hash": "4fdc27d6097ce6f4a916d11ff1d3905c", "index": {"url": "z_bd29078fd467125e___init___py.html", "file": "core\\reports\\generators\\__init__.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 4, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_bd29078fd467125e_excel_generator_py": {"hash": "14542e21e9a1eba1b8369657a3d3014a", "index": {"url": "z_bd29078fd467125e_excel_generator_py.html", "file": "core\\reports\\generators\\excel_generator.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 186, "n_excluded": 6, "n_missing": 161, "n_branches": 60, "n_partial_branches": 0, "n_missing_branches": 60}}}, "z_bd29078fd467125e_html_generator_py": {"hash": "11ef7e0cf441f2bbcd2c7aa54b699c36", "index": {"url": "z_bd29078fd467125e_html_generator_py.html", "file": "core\\reports\\generators\\html_generator.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 95, "n_excluded": 7, "n_missing": 75, "n_branches": 10, "n_partial_branches": 0, "n_missing_branches": 10}}}, "z_bd29078fd467125e_pdf_generator_py": {"hash": "2575bf19b54c8401109599d2b64df34f", "index": {"url": "z_bd29078fd467125e_pdf_generator_py.html", "file": "core\\reports\\generators\\pdf_generator.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 103, "n_excluded": 5, "n_missing": 85, "n_branches": 24, "n_partial_branches": 0, "n_missing_branches": 24}}}, "z_a6617d05fb563bba_report_service_py": {"hash": "7d7c7da20d88720c8c5b7d6d804d78be", "index": {"url": "z_a6617d05fb563bba_report_service_py.html", "file": "core\\reports\\report_service.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 162, "n_excluded": 17, "n_missing": 137, "n_branches": 42, "n_partial_branches": 0, "n_missing_branches": 42}}}, "z_29755ff45d857bc4___init___py": {"hash": "faee28f7c9ba58f20d1e8ed93e30571b", "index": {"url": "z_29755ff45d857bc4___init___py.html", "file": "core\\reports\\templates\\__init__.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 4, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_29755ff45d857bc4_document_templates_py": {"hash": "03cbfee48e53dd73defccc4d0ecd42dc", "index": {"url": "z_29755ff45d857bc4_document_templates_py.html", "file": "core\\reports\\templates\\document_templates.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 34, "n_excluded": 1, "n_missing": 17, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_29755ff45d857bc4_template_manager_py": {"hash": "308ceb03fbc159ff9e2f4d313eba10e9", "index": {"url": "z_29755ff45d857bc4_template_manager_py.html", "file": "core\\reports\\templates\\template_manager.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 162, "n_excluded": 12, "n_missing": 141, "n_branches": 48, "n_partial_branches": 0, "n_missing_branches": 48}}}, "z_29755ff45d857bc4_template_renderer_py": {"hash": "c4b29394f080880cf466262712c9a216", "index": {"url": "z_29755ff45d857bc4_template_renderer_py.html", "file": "core\\reports\\templates\\template_renderer.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 147, "n_excluded": 8, "n_missing": 120, "n_branches": 26, "n_partial_branches": 0, "n_missing_branches": 26}}}, "z_ac369222432f63da_activity_log_repository_py": {"hash": "59576875b97a3a35f78b3ba137d9c80d", "index": {"url": "z_ac369222432f63da_activity_log_repository_py.html", "file": "core\\repositories\\activity_log_repository.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 198, "n_excluded": 30, "n_missing": 170, "n_branches": 32, "n_partial_branches": 0, "n_missing_branches": 32}}}, "z_ac369222432f63da_base_repository_py": {"hash": "6410552cdaa7fd223b0e304f12662492", "index": {"url": "z_ac369222432f63da_base_repository_py.html", "file": "core\\repositories\\base_repository.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 103, "n_excluded": 0, "n_missing": 82, "n_branches": 32, "n_partial_branches": 0, "n_missing_branches": 32}}}, "z_ac369222432f63da_component_repository_py": {"hash": "06a243e88b49e1b222bad6b85480111b", "index": {"url": "z_ac369222432f63da_component_repository_py.html", "file": "core\\repositories\\component_repository.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 97, "n_excluded": 20, "n_missing": 77, "n_branches": 6, "n_partial_branches": 0, "n_missing_branches": 6}}}, "z_ac369222432f63da_dependencies_py": {"hash": "437721cc4a70e0d61a1f5dc049d34d12", "index": {"url": "z_ac369222432f63da_dependencies_py.html", "file": "core\\repositories\\dependencies.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 27, "n_excluded": 0, "n_missing": 16, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_ac369222432f63da_document_repository_py": {"hash": "028c507f2984d3e3d1977eb6f9da85bf", "index": {"url": "z_ac369222432f63da_document_repository_py.html", "file": "core\\repositories\\document_repository.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 193, "n_excluded": 43, "n_missing": 161, "n_branches": 4, "n_partial_branches": 0, "n_missing_branches": 4}}}, "z_ac369222432f63da_electrical_repository_py": {"hash": "decc7bc40a23c93c7851e3d66d045c7b", "index": {"url": "z_ac369222432f63da_electrical_repository_py.html", "file": "core\\repositories\\electrical_repository.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 309, "n_excluded": 65, "n_missing": 262, "n_branches": 4, "n_partial_branches": 0, "n_missing_branches": 4}}}, "z_ac369222432f63da_heat_tracing_repository_py": {"hash": "1e9690525b5eff37ec1a0de0253e2b0a", "index": {"url": "z_ac369222432f63da_heat_tracing_repository_py.html", "file": "core\\repositories\\heat_tracing_repository.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 264, "n_excluded": 58, "n_missing": 222, "n_branches": 6, "n_partial_branches": 0, "n_missing_branches": 6}}}, "z_ac369222432f63da_project_repository_py": {"hash": "ff3d1765c8269d290b5a413b003354dc", "index": {"url": "z_ac369222432f63da_project_repository_py.html", "file": "core\\repositories\\project_repository.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 98, "n_excluded": 22, "n_missing": 79, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_ac369222432f63da_switchboard_repository_py": {"hash": "b59cd85d30a17a5acdf0adf2e6b5abee", "index": {"url": "z_ac369222432f63da_switchboard_repository_py.html", "file": "core\\repositories\\switchboard_repository.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 153, "n_excluded": 32, "n_missing": 123, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_ac369222432f63da_user_repository_py": {"hash": "6b37dbcc578de102946bc26cb4b14c09", "index": {"url": "z_ac369222432f63da_user_repository_py.html", "file": "core\\repositories\\user_repository.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 120, "n_excluded": 62, "n_missing": 98, "n_branches": 2, "n_partial_branches": 0, "n_missing_branches": 2}}}, "z_9b29dd34b81637e5_activity_log_schemas_py": {"hash": "35f17bd7d69708d1a06c25318f4e76ff", "index": {"url": "z_9b29dd34b81637e5_activity_log_schemas_py.html", "file": "core\\schemas\\activity_log_schemas.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 182, "n_excluded": 1, "n_missing": 36, "n_branches": 18, "n_partial_branches": 0, "n_missing_branches": 18}}}, "z_9b29dd34b81637e5_base_py": {"hash": "afbaabee84d96d07750d9db70d95f2e6", "index": {"url": "z_9b29dd34b81637e5_base_py.html", "file": "core\\schemas\\base.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 42, "n_excluded": 0, "n_missing": 3, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_9b29dd34b81637e5_component_schemas_py": {"hash": "03fe72bcf128dd24c8057b95045e3c7e", "index": {"url": "z_9b29dd34b81637e5_component_schemas_py.html", "file": "core\\schemas\\component_schemas.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 82, "n_excluded": 0, "n_missing": 19, "n_branches": 10, "n_partial_branches": 0, "n_missing_branches": 10}}}, "z_9b29dd34b81637e5_document_schemas_py": {"hash": "7a7fbf39195c69cc76b5db0a8bdb4b6d", "index": {"url": "z_9b29dd34b81637e5_document_schemas_py.html", "file": "core\\schemas\\document_schemas.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 188, "n_excluded": 0, "n_missing": 41, "n_branches": 26, "n_partial_branches": 0, "n_missing_branches": 26}}}, "z_9b29dd34b81637e5_electrical_schemas_py": {"hash": "9b77e718112d38700ce8a5c73128b76a", "index": {"url": "z_9b29dd34b81637e5_electrical_schemas_py.html", "file": "core\\schemas\\electrical_schemas.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 409, "n_excluded": 0, "n_missing": 30, "n_branches": 18, "n_partial_branches": 0, "n_missing_branches": 18}}}, "z_9b29dd34b81637e5_error_py": {"hash": "9e650b26bccba88025c050328ed3ca7c", "index": {"url": "z_9b29dd34b81637e5_error_py.html", "file": "core\\schemas\\error.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 18, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_9b29dd34b81637e5_heat_tracing_schemas_py": {"hash": "ed61bbd9e97a3dd10f3919b1be284ffb", "index": {"url": "z_9b29dd34b81637e5_heat_tracing_schemas_py.html", "file": "core\\schemas\\heat_tracing_schemas.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 330, "n_excluded": 0, "n_missing": 47, "n_branches": 30, "n_partial_branches": 0, "n_missing_branches": 30}}}, "z_9b29dd34b81637e5_project_schemas_py": {"hash": "4b5e268cacad7f3bd9919fc6022d30f0", "index": {"url": "z_9b29dd34b81637e5_project_schemas_py.html", "file": "core\\schemas\\project_schemas.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 116, "n_excluded": 1, "n_missing": 34, "n_branches": 18, "n_partial_branches": 0, "n_missing_branches": 18}}}, "z_9b29dd34b81637e5_switchboard_schemas_py": {"hash": "4e12806ac8dac9e1028949a252af3a55", "index": {"url": "z_9b29dd34b81637e5_switchboard_schemas_py.html", "file": "core\\schemas\\switchboard_schemas.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 156, "n_excluded": 0, "n_missing": 15, "n_branches": 10, "n_partial_branches": 0, "n_missing_branches": 10}}}, "z_9b29dd34b81637e5_user_schemas_py": {"hash": "f76caf31f6cfc0b769bb3a6853e9e5d3", "index": {"url": "z_9b29dd34b81637e5_user_schemas_py.html", "file": "core\\schemas\\user_schemas.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 90, "n_excluded": 70, "n_missing": 10, "n_branches": 6, "n_partial_branches": 0, "n_missing_branches": 6}}}, "z_42a9bb67bf120292_activity_log_service_py": {"hash": "20f061729c868d1185579314a3563c72", "index": {"url": "z_42a9bb67bf120292_activity_log_service_py.html", "file": "core\\services\\activity_log_service.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 264, "n_excluded": 26, "n_missing": 225, "n_branches": 60, "n_partial_branches": 0, "n_missing_branches": 60}}}, "z_42a9bb67bf120292_component_service_py": {"hash": "dd06b0b08cad2e6866d5b16dd75a4cac", "index": {"url": "z_42a9bb67bf120292_component_service_py.html", "file": "core\\services\\component_service.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 245, "n_excluded": 21, "n_missing": 217, "n_branches": 72, "n_partial_branches": 0, "n_missing_branches": 72}}}, "z_42a9bb67bf120292_dependencies_py": {"hash": "57b057de20965f24313fdf2e6bf28cd2", "index": {"url": "z_42a9bb67bf120292_dependencies_py.html", "file": "core\\services\\dependencies.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 20, "n_excluded": 0, "n_missing": 12, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_42a9bb67bf120292_document_service_py": {"hash": "2c86ff091c90b2db0750409452732d21", "index": {"url": "z_42a9bb67bf120292_document_service_py.html", "file": "core\\services\\document_service.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 332, "n_excluded": 33, "n_missing": 290, "n_branches": 82, "n_partial_branches": 0, "n_missing_branches": 82}}}, "z_42a9bb67bf120292_electrical_service_py": {"hash": "e36e4a009590e6ea73f90c01cfe197df", "index": {"url": "z_42a9bb67bf120292_electrical_service_py.html", "file": "core\\services\\electrical_service.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 228, "n_excluded": 17, "n_missing": 201, "n_branches": 52, "n_partial_branches": 0, "n_missing_branches": 52}}}, "z_42a9bb67bf120292_heat_tracing_service_py": {"hash": "3b2f2851f5d0f34ee8065b2c51a7350f", "index": {"url": "z_42a9bb67bf120292_heat_tracing_service_py.html", "file": "core\\services\\heat_tracing_service.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 339, "n_excluded": 30, "n_missing": 300, "n_branches": 64, "n_partial_branches": 0, "n_missing_branches": 64}}}, "z_42a9bb67bf120292_project_service_py": {"hash": "6a8171a73e44920af6c70ee1bc39d17f", "index": {"url": "z_42a9bb67bf120292_project_service_py.html", "file": "core\\services\\project_service.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 169, "n_excluded": 17, "n_missing": 149, "n_branches": 44, "n_partial_branches": 0, "n_missing_branches": 44}}}, "z_42a9bb67bf120292_switchboard_service_py": {"hash": "b924dbca9d069497b9a33fcfe4d30c87", "index": {"url": "z_42a9bb67bf120292_switchboard_service_py.html", "file": "core\\services\\switchboard_service.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 220, "n_excluded": 22, "n_missing": 191, "n_branches": 46, "n_partial_branches": 0, "n_missing_branches": 46}}}, "z_42a9bb67bf120292_user_service_py": {"hash": "94f4913fa6d7bb5ef7426f9164959ea4", "index": {"url": "z_42a9bb67bf120292_user_service_py.html", "file": "core\\services\\user_service.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 227, "n_excluded": 129, "n_missing": 191, "n_branches": 46, "n_partial_branches": 0, "n_missing_branches": 46}}}, "z_5819abf34aa6dee0___init___py": {"hash": "ac1a85f660b7f569f984b0f25d87da23", "index": {"url": "z_5819abf34aa6dee0___init___py.html", "file": "core\\standards\\__init__.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 9, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_6ae03e9e7000b4b1___init___py": {"hash": "d0015fd8e3ca8b360ce3e255964c96df", "index": {"url": "z_6ae03e9e7000b4b1___init___py.html", "file": "core\\standards\\api\\__init__.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 4, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_6ae03e9e7000b4b1_api_rp_14f_py": {"hash": "4f15390c9452d417efb17be28270f093", "index": {"url": "z_6ae03e9e7000b4b1_api_rp_14f_py.html", "file": "core\\standards\\api\\api_rp_14f.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 54, "n_excluded": 5, "n_missing": 41, "n_branches": 6, "n_partial_branches": 0, "n_missing_branches": 6}}}, "z_6ae03e9e7000b4b1_api_rp_14fz_py": {"hash": "216c0ea2dac0939a1fcc8d2a764d81a6", "index": {"url": "z_6ae03e9e7000b4b1_api_rp_14fz_py.html", "file": "core\\standards\\api\\api_rp_14fz.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 51, "n_excluded": 5, "n_missing": 38, "n_branches": 4, "n_partial_branches": 0, "n_missing_branches": 4}}}, "z_6ae03e9e7000b4b1_api_standards_py": {"hash": "f3b85fc5cfae632f2c586e066d6d584c", "index": {"url": "z_6ae03e9e7000b4b1_api_standards_py.html", "file": "core\\standards\\api\\api_standards.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 40, "n_excluded": 2, "n_missing": 30, "n_branches": 8, "n_partial_branches": 0, "n_missing_branches": 8}}}, "z_dbac4bf9b4599789___init___py": {"hash": "408209f1bfa0555264c6ec2789b90842", "index": {"url": "z_dbac4bf9b4599789___init___py.html", "file": "core\\standards\\general\\__init__.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 4, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_dbac4bf9b4599789_base_standard_py": {"hash": "e5393d55ae903a54ca0274b5ebcfbe00", "index": {"url": "z_dbac4bf9b4599789_base_standard_py.html", "file": "core\\standards\\general\\base_standard.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 110, "n_excluded": 53, "n_missing": 66, "n_branches": 24, "n_partial_branches": 0, "n_missing_branches": 24}}}, "z_dbac4bf9b4599789_standards_calculator_py": {"hash": "06d60b42b390dc7022ff8d2bfc68d531", "index": {"url": "z_dbac4bf9b4599789_standards_calculator_py.html", "file": "core\\standards\\general\\standards_calculator.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 173, "n_excluded": 11, "n_missing": 148, "n_branches": 28, "n_partial_branches": 0, "n_missing_branches": 28}}}, "z_dbac4bf9b4599789_standards_validator_py": {"hash": "85a195baf5be956962f794ad30d73389", "index": {"url": "z_dbac4bf9b4599789_standards_validator_py.html", "file": "core\\standards\\general\\standards_validator.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 148, "n_excluded": 12, "n_missing": 126, "n_branches": 42, "n_partial_branches": 0, "n_missing_branches": 42}}}, "z_3850ffddfe46d663___init___py": {"hash": "59dad666ce15003c31832c13860b06db", "index": {"url": "z_3850ffddfe46d663___init___py.html", "file": "core\\standards\\iec\\__init__.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 4, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_3850ffddfe46d663_iec_60079_py": {"hash": "7e3a87eab127ef22dff4593453395eeb", "index": {"url": "z_3850ffddfe46d663_iec_60079_py.html", "file": "core\\standards\\iec\\iec_60079.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 53, "n_excluded": 5, "n_missing": 40, "n_branches": 6, "n_partial_branches": 0, "n_missing_branches": 6}}}, "z_3850ffddfe46d663_iec_62395_py": {"hash": "376bc837a06808c16fd0a202ea2b0865", "index": {"url": "z_3850ffddfe46d663_iec_62395_py.html", "file": "core\\standards\\iec\\iec_62395.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 52, "n_excluded": 5, "n_missing": 39, "n_branches": 6, "n_partial_branches": 0, "n_missing_branches": 6}}}, "z_3850ffddfe46d663_iec_standards_py": {"hash": "6dc7c56c8d46b6ab1add0efaba8464bd", "index": {"url": "z_3850ffddfe46d663_iec_standards_py.html", "file": "core\\standards\\iec\\iec_standards.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 96, "n_excluded": 6, "n_missing": 82, "n_branches": 22, "n_partial_branches": 0, "n_missing_branches": 22}}}, "z_acb3a9c72f1ecd2d___init___py": {"hash": "77671b05c8cd4132cc17d2e787a6d551", "index": {"url": "z_acb3a9c72f1ecd2d___init___py.html", "file": "core\\standards\\iec_60079_30_1\\__init__.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 3, "n_excluded": 0, "n_missing": 3, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_acb3a9c72f1ecd2d_hazardous_area_compliance_py": {"hash": "9ae905cc37230381210f9e6a334f6d2b", "index": {"url": "z_acb3a9c72f1ecd2d_hazardous_area_compliance_py.html", "file": "core\\standards\\iec_60079_30_1\\hazardous_area_compliance.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 66, "n_excluded": 3, "n_missing": 66, "n_branches": 34, "n_partial_branches": 0, "n_missing_branches": 34}}}, "z_acb3a9c72f1ecd2d_temperature_class_limits_py": {"hash": "a818077db9f4446e7aa5abe4921b1aed", "index": {"url": "z_acb3a9c72f1ecd2d_temperature_class_limits_py.html", "file": "core\\standards\\iec_60079_30_1\\temperature_class_limits.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 36, "n_excluded": 4, "n_missing": 36, "n_branches": 8, "n_partial_branches": 0, "n_missing_branches": 8}}}, "z_11fc723c286b0310___init___py": {"hash": "6ca2b2641eb9644f4d8f203067576237", "index": {"url": "z_11fc723c286b0310___init___py.html", "file": "core\\standards\\ieee\\__init__.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 4, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_11fc723c286b0310_ieee_515_py": {"hash": "e9bf73e83b8ed9c6e4fb177a524bdc84", "index": {"url": "z_11fc723c286b0310_ieee_515_py.html", "file": "core\\standards\\ieee\\ieee_515.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 144, "n_excluded": 7, "n_missing": 125, "n_branches": 44, "n_partial_branches": 0, "n_missing_branches": 44}}}, "z_11fc723c286b0310_ieee_844_py": {"hash": "aaf21071bde00c6c63e8b8d48ae7ae18", "index": {"url": "z_11fc723c286b0310_ieee_844_py.html", "file": "core\\standards\\ieee\\ieee_844.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 132, "n_excluded": 6, "n_missing": 112, "n_branches": 34, "n_partial_branches": 0, "n_missing_branches": 34}}}, "z_11fc723c286b0310_ieee_standards_py": {"hash": "d82bd9b2a9d026bc780ec4598155159f", "index": {"url": "z_11fc723c286b0310_ieee_standards_py.html", "file": "core\\standards\\ieee\\ieee_standards.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 97, "n_excluded": 6, "n_missing": 83, "n_branches": 32, "n_partial_branches": 0, "n_missing_branches": 32}}}, "z_8199a77f46f683ea___init___py": {"hash": "f863ff87dfa5d91e6a2e7537ae7e1feb", "index": {"url": "z_8199a77f46f683ea___init___py.html", "file": "core\\standards\\iso\\__init__.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 4, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_8199a77f46f683ea_iso_13623_py": {"hash": "43e00a77a777a48c9e6502a2df0cf73c", "index": {"url": "z_8199a77f46f683ea_iso_13623_py.html", "file": "core\\standards\\iso\\iso_13623.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 15, "n_excluded": 2, "n_missing": 5, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_8199a77f46f683ea_iso_14692_py": {"hash": "6609b9d07833dbc1d13ad5db88d72b0b", "index": {"url": "z_8199a77f46f683ea_iso_14692_py.html", "file": "core\\standards\\iso\\iso_14692.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 15, "n_excluded": 2, "n_missing": 5, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_8199a77f46f683ea_iso_standards_py": {"hash": "34de87e51476e26fd5106ac57cfbcdb4", "index": {"url": "z_8199a77f46f683ea_iso_standards_py.html", "file": "core\\standards\\iso\\iso_standards.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 10, "n_excluded": 1, "n_missing": 2, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_7d8fbd68730761fe___init___py": {"hash": "76638cfadaa7d97d3727405212df5e18", "index": {"url": "z_7d8fbd68730761fe___init___py.html", "file": "core\\standards\\nfpa\\__init__.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 4, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_7d8fbd68730761fe_nfpa_497_py": {"hash": "2753f641c2baa459f3020db2e52f5c08", "index": {"url": "z_7d8fbd68730761fe_nfpa_497_py.html", "file": "core\\standards\\nfpa\\nfpa_497.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 15, "n_excluded": 2, "n_missing": 5, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_7d8fbd68730761fe_nfpa_70_py": {"hash": "ea98281fb83b4d63ed86073668656178", "index": {"url": "z_7d8fbd68730761fe_nfpa_70_py.html", "file": "core\\standards\\nfpa\\nfpa_70.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 15, "n_excluded": 2, "n_missing": 5, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_7d8fbd68730761fe_nfpa_standards_py": {"hash": "ede608493f8da77f5e7f030c086b698d", "index": {"url": "z_7d8fbd68730761fe_nfpa_standards_py.html", "file": "core\\standards\\nfpa\\nfpa_standards.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 10, "n_excluded": 1, "n_missing": 2, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_5819abf34aa6dee0_standards_manager_py": {"hash": "80eb6b235f63f1fca4644d29778bd6e5", "index": {"url": "z_5819abf34aa6dee0_standards_manager_py.html", "file": "core\\standards\\standards_manager.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 181, "n_excluded": 8, "n_missing": 141, "n_branches": 62, "n_partial_branches": 0, "n_missing_branches": 62}}}, "z_5819abf34aa6dee0_standards_service_py": {"hash": "113f1dc54df51b50b60212bcdab260ed", "index": {"url": "z_5819abf34aa6dee0_standards_service_py.html", "file": "core\\standards\\standards_service.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 186, "n_excluded": 9, "n_missing": 162, "n_branches": 44, "n_partial_branches": 0, "n_missing_branches": 44}}}, "z_e19f0f1c06c261bc___init___py": {"hash": "691c4b3aec0b89632b86259ad27cc2dc", "index": {"url": "z_e19f0f1c06c261bc___init___py.html", "file": "core\\standards\\tr_50410\\__init__.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 2, "n_excluded": 0, "n_missing": 2, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_e19f0f1c06c261bc_heat_loss_factors_py": {"hash": "8ac0f67c1c4521c14a7f10a88e4f633e", "index": {"url": "z_e19f0f1c06c261bc_heat_loss_factors_py.html", "file": "core\\standards\\tr_50410\\heat_loss_factors.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 23, "n_excluded": 1, "n_missing": 23, "n_branches": 8, "n_partial_branches": 0, "n_missing_branches": 8}}}, "z_8e66a5447a3433f3___init___py": {"hash": "7d8c72295fa7874b6ef05d804c48558e", "index": {"url": "z_8e66a5447a3433f3___init___py.html", "file": "core\\utils\\__init__.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 9, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_8e66a5447a3433f3_crud_endpoint_factory_py": {"hash": "661f2a63e993d1e4c1d660a6fc5f130c", "index": {"url": "z_8e66a5447a3433f3_crud_endpoint_factory_py.html", "file": "core\\utils\\crud_endpoint_factory.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 123, "n_excluded": 0, "n_missing": 102, "n_branches": 14, "n_partial_branches": 0, "n_missing_branches": 14}}}, "z_8e66a5447a3433f3_datetime_utils_py": {"hash": "3d7f8479cf49161fa96f1ad884dd89ea", "index": {"url": "z_8e66a5447a3433f3_datetime_utils_py.html", "file": "core\\utils\\datetime_utils.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 94, "n_excluded": 1, "n_missing": 69, "n_branches": 36, "n_partial_branches": 0, "n_missing_branches": 36}}}, "z_8e66a5447a3433f3_file_io_utils_py": {"hash": "6dda0eee9eb83405efd309727d76a5c1", "index": {"url": "z_8e66a5447a3433f3_file_io_utils_py.html", "file": "core\\utils\\file_io_utils.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 163, "n_excluded": 6, "n_missing": 132, "n_branches": 42, "n_partial_branches": 0, "n_missing_branches": 42}}}, "z_8e66a5447a3433f3_json_validation_py": {"hash": "f03eec86f438103c13655c9c41fcc33d", "index": {"url": "z_8e66a5447a3433f3_json_validation_py.html", "file": "core\\utils\\json_validation.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 135, "n_excluded": 0, "n_missing": 107, "n_branches": 54, "n_partial_branches": 0, "n_missing_branches": 54}}}, "z_8e66a5447a3433f3_pagination_utils_py": {"hash": "c5826db5caa9b0bb141fb9898099a183", "index": {"url": "z_8e66a5447a3433f3_pagination_utils_py.html", "file": "core\\utils\\pagination_utils.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 100, "n_excluded": 0, "n_missing": 60, "n_branches": 38, "n_partial_branches": 0, "n_missing_branches": 38}}}, "z_8e66a5447a3433f3_query_utils_py": {"hash": "71bcba733fddd2c2e4753f37bea2df72", "index": {"url": "z_8e66a5447a3433f3_query_utils_py.html", "file": "core\\utils\\query_utils.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 167, "n_excluded": 0, "n_missing": 144, "n_branches": 102, "n_partial_branches": 0, "n_missing_branches": 102}}}, "z_8e66a5447a3433f3_string_utils_py": {"hash": "fcfe26aaa72cc5ab27848ece2cf7929d", "index": {"url": "z_8e66a5447a3433f3_string_utils_py.html", "file": "core\\utils\\string_utils.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 97, "n_excluded": 0, "n_missing": 76, "n_branches": 46, "n_partial_branches": 0, "n_missing_branches": 46}}}, "z_8e66a5447a3433f3_unit_conversion_utils_py": {"hash": "6536b4de16964b6e31a68d65652b4aaf", "index": {"url": "z_8e66a5447a3433f3_unit_conversion_utils_py.html", "file": "core\\utils\\unit_conversion_utils.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 116, "n_excluded": 1, "n_missing": 116, "n_branches": 56, "n_partial_branches": 0, "n_missing_branches": 56}}}, "z_8e66a5447a3433f3_uuid_utils_py": {"hash": "83f44aeab583ca958536759788f27cc5", "index": {"url": "z_8e66a5447a3433f3_uuid_utils_py.html", "file": "core\\utils\\uuid_utils.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 45, "n_excluded": 0, "n_missing": 29, "n_branches": 12, "n_partial_branches": 0, "n_missing_branches": 12}}}}}