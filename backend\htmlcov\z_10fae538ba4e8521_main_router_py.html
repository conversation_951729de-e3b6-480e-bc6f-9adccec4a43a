<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage for api\main_router.py: 100.00%</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_718ce007.css" type="text/css">
    <script type="text/javascript">
        contexts = {
  "a": "(empty)"
}
    </script>
    <script src="coverage_html_cb_497bf287.js" defer></script>
</head>
<body class="pyfile">
<header>
    <div class="content">
        <h1>
            <span class="text">Coverage for </span><b>api\main_router.py</b>:
            <span class="pc_cov">100.00%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>r</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>p</kbd>
                        &nbsp; toggle line displays
                    </p>
                    <p>
                        <kbd>j</kbd>
                        <kbd>k</kbd>
                        &nbsp; next/prev highlighted chunk
                    </p>
                    <p>
                        <kbd>0</kbd> &nbsp; (zero) top of page
                    </p>
                    <p>
                        <kbd>1</kbd> &nbsp; (one) first highlighted chunk
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>u</kbd> &nbsp; up to the index
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <h2>
            <span class="text">24 statements &nbsp;</span>
            <button type="button" class="run button_toggle_run" value="run" data-shortcut="r" title="Toggle lines run">24<span class="text"> run</span></button>
            <button type="button" class="mis show_mis button_toggle_mis" value="mis" data-shortcut="m" title="Toggle lines missing">0<span class="text"> missing</span></button>
            <button type="button" class="exc show_exc button_toggle_exc" value="exc" data-shortcut="x" title="Toggle lines excluded">0<span class="text"> excluded</span></button>
            <button type="button" class="par run show_par button_toggle_par" value="par" data-shortcut="p" title="Toggle lines partially run">0<span class="text"> partial</span></button>
        </h2>
        <p class="text">
            <a id="prevFileLink" class="nav" href="z_10fae538ba4e8521_dependencies_py.html">&#xab; prev</a> &nbsp; &nbsp;
            <a id="indexLink" class="nav" href="index.html">&Hat; index</a> &nbsp; &nbsp;
            <a id="nextFileLink" class="nav" href="z_3c8229fafc2171e7_activity_log_routes_py.html">&#xbb; next</a>
            &nbsp; &nbsp; &nbsp;
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.0">coverage.py v7.8.0</a>,
            created at 2025-06-03 23:24 +0300
        </p>
        <aside class="hidden">
            <button type="button" class="button_next_chunk" data-shortcut="j"></button>
            <button type="button" class="button_prev_chunk" data-shortcut="k"></button>
            <button type="button" class="button_top_of_page" data-shortcut="0"></button>
            <button type="button" class="button_first_chunk" data-shortcut="1"></button>
            <button type="button" class="button_prev_file" data-shortcut="["></button>
            <button type="button" class="button_next_file" data-shortcut="]"></button>
            <button type="button" class="button_to_index" data-shortcut="u"></button>
            <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
        </aside>
    </div>
</header>
<main id="source">
    <p class="pln"><span class="n"><a id="t1" href="#t1">1</a></span><span class="t"><span class="com"># src/api/main_router.py</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t2" href="#t2">2</a></span><span class="t"><span class="key">from</span> <span class="nam">fastapi</span> <span class="key">import</span> <span class="nam">APIRouter</span>&nbsp;</span><span class="r"><label for="ctxs2" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t3" href="#t3">3</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t4" href="#t4">4</a></span><span class="t"><span class="com"># Import versioned routers</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t5" href="#t5">5</a></span><span class="t"><span class="key">from</span> <span class="op">.</span><span class="nam">v1</span><span class="op">.</span><span class="nam">project_routes</span> <span class="key">import</span> <span class="nam">router</span> <span class="key">as</span> <span class="nam">v1_project_router</span>&nbsp;</span><span class="r"><label for="ctxs5" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t6" href="#t6">6</a></span><span class="t"><span class="key">from</span> <span class="op">.</span><span class="nam">v1</span><span class="op">.</span><span class="nam">component_routes</span> <span class="key">import</span> <span class="nam">router</span> <span class="key">as</span> <span class="nam">v1_component_router</span>&nbsp;</span><span class="r"><label for="ctxs6" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t7" href="#t7">7</a></span><span class="t"><span class="key">from</span> <span class="op">.</span><span class="nam">v1</span><span class="op">.</span><span class="nam">heat_tracing_routes</span> <span class="key">import</span> <span class="nam">router</span> <span class="key">as</span> <span class="nam">v1_heat_tracing_router</span>&nbsp;</span><span class="r"><label for="ctxs7" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t8" href="#t8">8</a></span><span class="t"><span class="key">from</span> <span class="op">.</span><span class="nam">v1</span><span class="op">.</span><span class="nam">electrical_routes</span> <span class="key">import</span> <span class="nam">router</span> <span class="key">as</span> <span class="nam">v1_electrical_router</span>&nbsp;</span><span class="r"><label for="ctxs8" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t9" href="#t9">9</a></span><span class="t"><span class="key">from</span> <span class="op">.</span><span class="nam">v1</span><span class="op">.</span><span class="nam">switchboard_routes</span> <span class="key">import</span> <span class="nam">router</span> <span class="key">as</span> <span class="nam">v1_switchboard_router</span>&nbsp;</span><span class="r"><label for="ctxs9" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t10" href="#t10">10</a></span><span class="t"><span class="key">from</span> <span class="op">.</span><span class="nam">v1</span><span class="op">.</span><span class="nam">user_routes</span> <span class="key">import</span> <span class="nam">router</span> <span class="key">as</span> <span class="nam">v1_user_router</span>&nbsp;</span><span class="r"><label for="ctxs10" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t11" href="#t11">11</a></span><span class="t"><span class="key">from</span> <span class="op">.</span><span class="nam">v1</span><span class="op">.</span><span class="nam">document_routes</span> <span class="key">import</span> <span class="nam">router</span> <span class="key">as</span> <span class="nam">v1_document_router</span>&nbsp;</span><span class="r"><label for="ctxs11" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t12" href="#t12">12</a></span><span class="t"><span class="key">from</span> <span class="op">.</span><span class="nam">v1</span><span class="op">.</span><span class="nam">activity_log_routes</span> <span class="key">import</span> <span class="nam">router</span> <span class="key">as</span> <span class="nam">v1_activity_log_router</span>&nbsp;</span><span class="r"><label for="ctxs12" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t13" href="#t13">13</a></span><span class="t"><span class="key">from</span> <span class="op">.</span><span class="nam">v1</span><span class="op">.</span><span class="nam">import_export_routes</span> <span class="key">import</span> <span class="nam">router</span> <span class="key">as</span> <span class="nam">v1_import_export_router</span>&nbsp;</span><span class="r"><label for="ctxs13" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t14" href="#t14">14</a></span><span class="t"><span class="key">from</span> <span class="op">.</span><span class="nam">v1</span><span class="op">.</span><span class="nam">reports_routes</span> <span class="key">import</span> <span class="nam">router</span> <span class="key">as</span> <span class="nam">v1_reports_router</span>&nbsp;</span><span class="r"><label for="ctxs14" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t15" href="#t15">15</a></span><span class="t"><span class="key">from</span> <span class="op">.</span><span class="nam">v1</span><span class="op">.</span><span class="nam">standards_routes</span> <span class="key">import</span> <span class="nam">router</span> <span class="key">as</span> <span class="nam">v1_standards_router</span>&nbsp;</span><span class="r"><label for="ctxs15" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t16" href="#t16">16</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t17" href="#t17">17</a></span><span class="t"><span class="nam">api_router</span> <span class="op">=</span> <span class="nam">APIRouter</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs17" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t18" href="#t18">18</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t19" href="#t19">19</a></span><span class="t"><span class="com"># Include versioned routers, typically with a version prefix</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t20" href="#t20">20</a></span><span class="t"><span class="nam">api_router</span><span class="op">.</span><span class="nam">include_router</span><span class="op">(</span><span class="nam">v1_project_router</span><span class="op">,</span> <span class="nam">prefix</span><span class="op">=</span><span class="str">"/v1/projects"</span><span class="op">,</span> <span class="nam">tags</span><span class="op">=</span><span class="op">[</span><span class="str">"Projects"</span><span class="op">]</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs20" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t21" href="#t21">21</a></span><span class="t"><span class="nam">api_router</span><span class="op">.</span><span class="nam">include_router</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs21" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t22" href="#t22">22</a></span><span class="t">    <span class="nam">v1_component_router</span><span class="op">,</span> <span class="nam">prefix</span><span class="op">=</span><span class="str">"/v1/components"</span><span class="op">,</span> <span class="nam">tags</span><span class="op">=</span><span class="op">[</span><span class="str">"Components"</span><span class="op">]</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t23" href="#t23">23</a></span><span class="t"><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t24" href="#t24">24</a></span><span class="t"><span class="nam">api_router</span><span class="op">.</span><span class="nam">include_router</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs24" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t25" href="#t25">25</a></span><span class="t">    <span class="nam">v1_heat_tracing_router</span><span class="op">,</span> <span class="nam">prefix</span><span class="op">=</span><span class="str">"/v1/heat-tracing"</span><span class="op">,</span> <span class="nam">tags</span><span class="op">=</span><span class="op">[</span><span class="str">"Heat Tracing"</span><span class="op">]</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t26" href="#t26">26</a></span><span class="t"><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t27" href="#t27">27</a></span><span class="t"><span class="nam">api_router</span><span class="op">.</span><span class="nam">include_router</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs27" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t28" href="#t28">28</a></span><span class="t">    <span class="nam">v1_electrical_router</span><span class="op">,</span> <span class="nam">prefix</span><span class="op">=</span><span class="str">"/v1/electrical"</span><span class="op">,</span> <span class="nam">tags</span><span class="op">=</span><span class="op">[</span><span class="str">"Electrical"</span><span class="op">]</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t29" href="#t29">29</a></span><span class="t"><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t30" href="#t30">30</a></span><span class="t"><span class="nam">api_router</span><span class="op">.</span><span class="nam">include_router</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs30" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t31" href="#t31">31</a></span><span class="t">    <span class="nam">v1_switchboard_router</span><span class="op">,</span> <span class="nam">prefix</span><span class="op">=</span><span class="str">"/v1/switchboards"</span><span class="op">,</span> <span class="nam">tags</span><span class="op">=</span><span class="op">[</span><span class="str">"Switchboards"</span><span class="op">]</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t32" href="#t32">32</a></span><span class="t"><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t33" href="#t33">33</a></span><span class="t"><span class="nam">api_router</span><span class="op">.</span><span class="nam">include_router</span><span class="op">(</span><span class="nam">v1_user_router</span><span class="op">,</span> <span class="nam">prefix</span><span class="op">=</span><span class="str">"/v1/users"</span><span class="op">,</span> <span class="nam">tags</span><span class="op">=</span><span class="op">[</span><span class="str">"Users"</span><span class="op">]</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs33" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t34" href="#t34">34</a></span><span class="t"><span class="nam">api_router</span><span class="op">.</span><span class="nam">include_router</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs34" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t35" href="#t35">35</a></span><span class="t">    <span class="nam">v1_document_router</span><span class="op">,</span> <span class="nam">prefix</span><span class="op">=</span><span class="str">"/v1/documents"</span><span class="op">,</span> <span class="nam">tags</span><span class="op">=</span><span class="op">[</span><span class="str">"Documents"</span><span class="op">]</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t36" href="#t36">36</a></span><span class="t"><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t37" href="#t37">37</a></span><span class="t"><span class="nam">api_router</span><span class="op">.</span><span class="nam">include_router</span><span class="op">(</span><span class="nam">v1_activity_log_router</span><span class="op">,</span> <span class="nam">prefix</span><span class="op">=</span><span class="str">"/v1"</span><span class="op">,</span> <span class="nam">tags</span><span class="op">=</span><span class="op">[</span><span class="str">"Activity Logs"</span><span class="op">]</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs37" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t38" href="#t38">38</a></span><span class="t"><span class="nam">api_router</span><span class="op">.</span><span class="nam">include_router</span><span class="op">(</span><span class="nam">v1_import_export_router</span><span class="op">,</span> <span class="nam">prefix</span><span class="op">=</span><span class="str">"/v1"</span><span class="op">,</span> <span class="nam">tags</span><span class="op">=</span><span class="op">[</span><span class="str">"Import/Export"</span><span class="op">]</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs38" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t39" href="#t39">39</a></span><span class="t"><span class="nam">api_router</span><span class="op">.</span><span class="nam">include_router</span><span class="op">(</span><span class="nam">v1_reports_router</span><span class="op">,</span> <span class="nam">prefix</span><span class="op">=</span><span class="str">"/v1"</span><span class="op">,</span> <span class="nam">tags</span><span class="op">=</span><span class="op">[</span><span class="str">"Reports"</span><span class="op">]</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs39" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t40" href="#t40">40</a></span><span class="t"><span class="nam">api_router</span><span class="op">.</span><span class="nam">include_router</span><span class="op">(</span><span class="nam">v1_standards_router</span><span class="op">,</span> <span class="nam">prefix</span><span class="op">=</span><span class="str">"/v1"</span><span class="op">,</span> <span class="nam">tags</span><span class="op">=</span><span class="op">[</span><span class="str">"Standards"</span><span class="op">]</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs40" class="ctx">(empty)</label></span></p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="z_10fae538ba4e8521_dependencies_py.html">&#xab; prev</a> &nbsp; &nbsp;
            <a class="nav" href="index.html">&Hat; index</a> &nbsp; &nbsp;
            <a class="nav" href="z_3c8229fafc2171e7_activity_log_routes_py.html">&#xbb; next</a>
            &nbsp; &nbsp; &nbsp;
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.0">coverage.py v7.8.0</a>,
            created at 2025-06-03 23:24 +0300
        </p>
    </div>
</footer>
</body>
</html>
