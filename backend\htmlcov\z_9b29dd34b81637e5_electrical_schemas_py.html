<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage for core\schemas\electrical_schemas.py: 88.76%</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_718ce007.css" type="text/css">
    <script type="text/javascript">
        contexts = {
  "a": "(empty)"
}
    </script>
    <script src="coverage_html_cb_497bf287.js" defer></script>
</head>
<body class="pyfile">
<header>
    <div class="content">
        <h1>
            <span class="text">Coverage for </span><b>core\schemas\electrical_schemas.py</b>:
            <span class="pc_cov">88.76%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>r</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>p</kbd>
                        &nbsp; toggle line displays
                    </p>
                    <p>
                        <kbd>j</kbd>
                        <kbd>k</kbd>
                        &nbsp; next/prev highlighted chunk
                    </p>
                    <p>
                        <kbd>0</kbd> &nbsp; (zero) top of page
                    </p>
                    <p>
                        <kbd>1</kbd> &nbsp; (one) first highlighted chunk
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>u</kbd> &nbsp; up to the index
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <h2>
            <span class="text">409 statements &nbsp;</span>
            <button type="button" class="run button_toggle_run" value="run" data-shortcut="r" title="Toggle lines run">379<span class="text"> run</span></button>
            <button type="button" class="mis show_mis button_toggle_mis" value="mis" data-shortcut="m" title="Toggle lines missing">30<span class="text"> missing</span></button>
            <button type="button" class="exc show_exc button_toggle_exc" value="exc" data-shortcut="x" title="Toggle lines excluded">0<span class="text"> excluded</span></button>
            <button type="button" class="par run show_par button_toggle_par" value="par" data-shortcut="p" title="Toggle lines partially run">0<span class="text"> partial</span></button>
        </h2>
        <p class="text">
            <a id="prevFileLink" class="nav" href="z_9b29dd34b81637e5_document_schemas_py.html">&#xab; prev</a> &nbsp; &nbsp;
            <a id="indexLink" class="nav" href="index.html">&Hat; index</a> &nbsp; &nbsp;
            <a id="nextFileLink" class="nav" href="z_9b29dd34b81637e5_error_py.html">&#xbb; next</a>
            &nbsp; &nbsp; &nbsp;
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.0">coverage.py v7.8.0</a>,
            created at 2025-06-03 23:24 +0300
        </p>
        <aside class="hidden">
            <button type="button" class="button_next_chunk" data-shortcut="j"></button>
            <button type="button" class="button_prev_chunk" data-shortcut="k"></button>
            <button type="button" class="button_top_of_page" data-shortcut="0"></button>
            <button type="button" class="button_first_chunk" data-shortcut="1"></button>
            <button type="button" class="button_prev_file" data-shortcut="["></button>
            <button type="button" class="button_next_file" data-shortcut="]"></button>
            <button type="button" class="button_to_index" data-shortcut="u"></button>
            <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
        </aside>
    </div>
</header>
<main id="source">
    <p class="pln"><span class="n"><a id="t1" href="#t1">1</a></span><span class="t"><span class="com"># backend/core/schemas/electrical_schemas.py</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t2" href="#t2">2</a></span><span class="t"><span class="str">"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t3" href="#t3">3</a></span><span class="t"><span class="str">Pydantic schemas for Electrical entities validation, serialization, and deserialization.</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t4" href="#t4">4</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t5" href="#t5">5</a></span><span class="t"><span class="str">This module defines the data contracts for Electrical-related operations including:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t6" href="#t6">6</a></span><span class="t"><span class="str">- ElectricalNode schemas: For electrical connection points and nodes</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t7" href="#t7">7</a></span><span class="t"><span class="str">- CableRoute schemas: For cable routing between electrical nodes</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t8" href="#t8">8</a></span><span class="t"><span class="str">- CableSegment schemas: For individual cable segments with specifications</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t9" href="#t9">9</a></span><span class="t"><span class="str">- LoadCalculation schemas: For electrical load calculations and power requirements</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t10" href="#t10">10</a></span><span class="t"><span class="str">- VoltageDropCalculation schemas: For voltage drop calculations and cable sizing</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t11" href="#t11">11</a></span><span class="t"><span class="str">- Calculation schemas: For integration with calculations layer</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t12" href="#t12">12</a></span><span class="t"><span class="str">- Validation schemas: For electrical engineering constraints and business logic</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t13" href="#t13">13</a></span><span class="t"><span class="str">"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t14" href="#t14">14</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t15" href="#t15">15</a></span><span class="t"><span class="key">import</span> <span class="nam">json</span>&nbsp;</span><span class="r"><label for="ctxs15" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t16" href="#t16">16</a></span><span class="t"><span class="key">from</span> <span class="nam">typing</span> <span class="key">import</span> <span class="nam">Any</span><span class="op">,</span> <span class="nam">Dict</span><span class="op">,</span> <span class="nam">List</span><span class="op">,</span> <span class="nam">Optional</span>&nbsp;</span><span class="r"><label for="ctxs16" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t17" href="#t17">17</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t18" href="#t18">18</a></span><span class="t"><span class="key">from</span> <span class="nam">pydantic</span> <span class="key">import</span> <span class="nam">BaseModel</span><span class="op">,</span> <span class="nam">ConfigDict</span><span class="op">,</span> <span class="nam">Field</span><span class="op">,</span> <span class="nam">field_validator</span><span class="op">,</span> <span class="nam">model_validator</span>&nbsp;</span><span class="r"><label for="ctxs18" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t19" href="#t19">19</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t20" href="#t20">20</a></span><span class="t"><span class="key">from</span> <span class="op">.</span><span class="op">.</span><span class="nam">models</span><span class="op">.</span><span class="nam">enums</span> <span class="key">import</span> <span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs20" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t21" href="#t21">21</a></span><span class="t">    <span class="nam">CableInstallationMethod</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t22" href="#t22">22</a></span><span class="t">    <span class="nam">ElectricalNodeType</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t23" href="#t23">23</a></span><span class="t"><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t24" href="#t24">24</a></span><span class="t"><span class="key">from</span> <span class="op">.</span><span class="nam">base</span> <span class="key">import</span> <span class="nam">BaseSoftDeleteSchema</span><span class="op">,</span> <span class="nam">PaginatedResponseSchema</span>&nbsp;</span><span class="r"><label for="ctxs24" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t25" href="#t25">25</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t26" href="#t26">26</a></span><span class="t"><span class="com"># ============================================================================</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t27" href="#t27">27</a></span><span class="t"><span class="com"># CALCULATION INTEGRATION SCHEMAS</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t28" href="#t28">28</a></span><span class="t"><span class="com"># ============================================================================</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t29" href="#t29">29</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t30" href="#t30">30</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t31" href="#t31">31</a></span><span class="t"><span class="key">class</span> <span class="nam">CableSizingCalculationInputSchema</span><span class="op">(</span><span class="nam">BaseModel</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"><label for="ctxs31" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t32" href="#t32">32</a></span><span class="t">    <span class="str">"""Schema for cable sizing calculation inputs."""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t33" href="#t33">33</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t34" href="#t34">34</a></span><span class="t">    <span class="nam">required_power_kw</span><span class="op">:</span> <span class="nam">float</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="op">...</span><span class="op">,</span> <span class="nam">gt</span><span class="op">=</span><span class="num">0</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Required power in kW"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs34" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t35" href="#t35">35</a></span><span class="t">    <span class="nam">cable_length_m</span><span class="op">:</span> <span class="nam">float</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="op">...</span><span class="op">,</span> <span class="nam">gt</span><span class="op">=</span><span class="num">0</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Cable length in meters"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs35" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t36" href="#t36">36</a></span><span class="t">    <span class="nam">supply_voltage_v</span><span class="op">:</span> <span class="nam">float</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="op">...</span><span class="op">,</span> <span class="nam">gt</span><span class="op">=</span><span class="num">0</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Supply voltage in volts"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs36" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t37" href="#t37">37</a></span><span class="t">    <span class="nam">ambient_temperature_c</span><span class="op">:</span> <span class="nam">float</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs37" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t38" href="#t38">38</a></span><span class="t">        <span class="num">25.0</span><span class="op">,</span> <span class="nam">ge</span><span class="op">=</span><span class="op">-</span><span class="num">50</span><span class="op">,</span> <span class="nam">le</span><span class="op">=</span><span class="num">80</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Ambient temperature in Celsius"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t39" href="#t39">39</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t40" href="#t40">40</a></span><span class="t">    <span class="nam">installation_method</span><span class="op">:</span> <span class="nam">CableInstallationMethod</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs40" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t41" href="#t41">41</a></span><span class="t">        <span class="op">...</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Cable installation method"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t42" href="#t42">42</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t43" href="#t43">43</a></span><span class="t">    <span class="nam">cable_type</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">str</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs43" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t44" href="#t44">44</a></span><span class="t">        <span class="key">None</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Preferred cable type (optional)"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t45" href="#t45">45</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t46" href="#t46">46</a></span><span class="t">    <span class="nam">max_voltage_drop_percent</span><span class="op">:</span> <span class="nam">float</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs46" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t47" href="#t47">47</a></span><span class="t">        <span class="num">5.0</span><span class="op">,</span> <span class="nam">gt</span><span class="op">=</span><span class="num">0</span><span class="op">,</span> <span class="nam">le</span><span class="op">=</span><span class="num">50</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Maximum allowed voltage drop percentage"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t48" href="#t48">48</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t49" href="#t49">49</a></span><span class="t">    <span class="nam">power_factor</span><span class="op">:</span> <span class="nam">float</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs49" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t50" href="#t50">50</a></span><span class="t">        <span class="num">1.0</span><span class="op">,</span> <span class="nam">gt</span><span class="op">=</span><span class="num">0</span><span class="op">,</span> <span class="nam">le</span><span class="op">=</span><span class="num">1</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Power factor (default 1.0 for resistive loads)"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t51" href="#t51">51</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t52" href="#t52">52</a></span><span class="t">    <span class="nam">safety_factor</span><span class="op">:</span> <span class="nam">float</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs52" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t53" href="#t53">53</a></span><span class="t">        <span class="num">1.2</span><span class="op">,</span> <span class="nam">ge</span><span class="op">=</span><span class="num">1</span><span class="op">,</span> <span class="nam">le</span><span class="op">=</span><span class="num">3</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Safety factor for cable sizing"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t54" href="#t54">54</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t55" href="#t55">55</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t56" href="#t56">56</a></span><span class="t">    <span class="nam">model_config</span> <span class="op">=</span> <span class="nam">ConfigDict</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs56" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t57" href="#t57">57</a></span><span class="t">        <span class="nam">json_schema_extra</span><span class="op">=</span><span class="op">{</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t58" href="#t58">58</a></span><span class="t">            <span class="str">"example"</span><span class="op">:</span> <span class="op">{</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t59" href="#t59">59</a></span><span class="t">                <span class="str">"required_power_kw"</span><span class="op">:</span> <span class="num">2.5</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t60" href="#t60">60</a></span><span class="t">                <span class="str">"cable_length_m"</span><span class="op">:</span> <span class="num">100.0</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t61" href="#t61">61</a></span><span class="t">                <span class="str">"supply_voltage_v"</span><span class="op">:</span> <span class="num">240.0</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t62" href="#t62">62</a></span><span class="t">                <span class="str">"ambient_temperature_c"</span><span class="op">:</span> <span class="num">25.0</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t63" href="#t63">63</a></span><span class="t">                <span class="str">"installation_method"</span><span class="op">:</span> <span class="str">"CABLE_TRAY"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t64" href="#t64">64</a></span><span class="t">                <span class="str">"cable_type"</span><span class="op">:</span> <span class="str">"self-regulating"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t65" href="#t65">65</a></span><span class="t">                <span class="str">"max_voltage_drop_percent"</span><span class="op">:</span> <span class="num">5.0</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t66" href="#t66">66</a></span><span class="t">                <span class="str">"power_factor"</span><span class="op">:</span> <span class="num">1.0</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t67" href="#t67">67</a></span><span class="t">                <span class="str">"safety_factor"</span><span class="op">:</span> <span class="num">1.2</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t68" href="#t68">68</a></span><span class="t">            <span class="op">}</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t69" href="#t69">69</a></span><span class="t">        <span class="op">}</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t70" href="#t70">70</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t71" href="#t71">71</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t72" href="#t72">72</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t73" href="#t73">73</a></span><span class="t"><span class="key">class</span> <span class="nam">CableSizingCalculationResultSchema</span><span class="op">(</span><span class="nam">BaseModel</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"><label for="ctxs73" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t74" href="#t74">74</a></span><span class="t">    <span class="str">"""Schema for cable sizing calculation results."""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t75" href="#t75">75</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t76" href="#t76">76</a></span><span class="t">    <span class="nam">recommended_cable_type</span><span class="op">:</span> <span class="nam">str</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="op">...</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Recommended cable type"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs76" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t77" href="#t77">77</a></span><span class="t">    <span class="nam">cable_power_per_meter</span><span class="op">:</span> <span class="nam">float</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs77" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t78" href="#t78">78</a></span><span class="t">        <span class="op">...</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Cable power per meter in W/m"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t79" href="#t79">79</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t80" href="#t80">80</a></span><span class="t">    <span class="nam">total_cable_length</span><span class="op">:</span> <span class="nam">float</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="op">...</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Total cable length in meters"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs80" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t81" href="#t81">81</a></span><span class="t">    <span class="nam">current_draw</span><span class="op">:</span> <span class="nam">float</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="op">...</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Current draw in amperes"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs81" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t82" href="#t82">82</a></span><span class="t">    <span class="nam">voltage_drop</span><span class="op">:</span> <span class="nam">float</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="op">...</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Voltage drop in volts"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs82" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t83" href="#t83">83</a></span><span class="t">    <span class="nam">voltage_drop_percent</span><span class="op">:</span> <span class="nam">float</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="op">...</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Voltage drop percentage"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs83" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t84" href="#t84">84</a></span><span class="t">    <span class="nam">power_density</span><span class="op">:</span> <span class="nam">float</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="op">...</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Power density in W/m&#178;"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs84" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t85" href="#t85">85</a></span><span class="t">    <span class="nam">is_compliant</span><span class="op">:</span> <span class="nam">bool</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="op">...</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Meets voltage drop requirements"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs85" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t86" href="#t86">86</a></span><span class="t">    <span class="nam">safety_margin_percent</span><span class="op">:</span> <span class="nam">float</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="op">...</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Safety margin percentage"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs86" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t87" href="#t87">87</a></span><span class="t">    <span class="nam">calculation_metadata</span><span class="op">:</span> <span class="nam">Dict</span><span class="op">[</span><span class="nam">str</span><span class="op">,</span> <span class="nam">Any</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs87" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t88" href="#t88">88</a></span><span class="t">        <span class="nam">default_factory</span><span class="op">=</span><span class="nam">dict</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Calculation metadata"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t89" href="#t89">89</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t90" href="#t90">90</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t91" href="#t91">91</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t92" href="#t92">92</a></span><span class="t"><span class="key">class</span> <span class="nam">VoltageDropCalculationInputSchema</span><span class="op">(</span><span class="nam">BaseModel</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"><label for="ctxs92" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t93" href="#t93">93</a></span><span class="t">    <span class="str">"""Schema for voltage drop calculation inputs."""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t94" href="#t94">94</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t95" href="#t95">95</a></span><span class="t">    <span class="nam">supply_voltage_v</span><span class="op">:</span> <span class="nam">float</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="op">...</span><span class="op">,</span> <span class="nam">gt</span><span class="op">=</span><span class="num">0</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Supply voltage in volts"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs95" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t96" href="#t96">96</a></span><span class="t">    <span class="nam">load_current_a</span><span class="op">:</span> <span class="nam">float</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="op">...</span><span class="op">,</span> <span class="nam">gt</span><span class="op">=</span><span class="num">0</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Load current in amperes"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs96" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t97" href="#t97">97</a></span><span class="t">    <span class="nam">cable_length_m</span><span class="op">:</span> <span class="nam">float</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="op">...</span><span class="op">,</span> <span class="nam">gt</span><span class="op">=</span><span class="num">0</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Cable length in meters"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs97" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t98" href="#t98">98</a></span><span class="t">    <span class="nam">cable_resistance_ohm_per_m</span><span class="op">:</span> <span class="nam">float</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs98" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t99" href="#t99">99</a></span><span class="t">        <span class="op">...</span><span class="op">,</span> <span class="nam">ge</span><span class="op">=</span><span class="num">0</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Cable resistance per meter in ohm/m"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t100" href="#t100">100</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t101" href="#t101">101</a></span><span class="t">    <span class="nam">cable_reactance_ohm_per_m</span><span class="op">:</span> <span class="nam">float</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs101" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t102" href="#t102">102</a></span><span class="t">        <span class="num">0.0</span><span class="op">,</span> <span class="nam">ge</span><span class="op">=</span><span class="num">0</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Cable reactance per meter in ohm/m"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t103" href="#t103">103</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t104" href="#t104">104</a></span><span class="t">    <span class="nam">power_factor</span><span class="op">:</span> <span class="nam">float</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs104" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t105" href="#t105">105</a></span><span class="t">        <span class="num">1.0</span><span class="op">,</span> <span class="nam">gt</span><span class="op">=</span><span class="num">0</span><span class="op">,</span> <span class="nam">le</span><span class="op">=</span><span class="num">1</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Power factor (default 1.0)"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t106" href="#t106">106</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t107" href="#t107">107</a></span><span class="t">    <span class="nam">ambient_temperature_c</span><span class="op">:</span> <span class="nam">float</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs107" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t108" href="#t108">108</a></span><span class="t">        <span class="num">25.0</span><span class="op">,</span> <span class="nam">ge</span><span class="op">=</span><span class="op">-</span><span class="num">50</span><span class="op">,</span> <span class="nam">le</span><span class="op">=</span><span class="num">80</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Ambient temperature in Celsius"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t109" href="#t109">109</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t110" href="#t110">110</a></span><span class="t">    <span class="nam">derating_factor</span><span class="op">:</span> <span class="nam">float</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs110" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t111" href="#t111">111</a></span><span class="t">        <span class="num">1.0</span><span class="op">,</span> <span class="nam">gt</span><span class="op">=</span><span class="num">0</span><span class="op">,</span> <span class="nam">le</span><span class="op">=</span><span class="num">1</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Derating factor for temperature/grouping"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t112" href="#t112">112</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t113" href="#t113">113</a></span><span class="t">    <span class="nam">max_voltage_drop_percent</span><span class="op">:</span> <span class="nam">float</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs113" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t114" href="#t114">114</a></span><span class="t">        <span class="num">5.0</span><span class="op">,</span> <span class="nam">gt</span><span class="op">=</span><span class="num">0</span><span class="op">,</span> <span class="nam">le</span><span class="op">=</span><span class="num">50</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Maximum allowed voltage drop percentage"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t115" href="#t115">115</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t116" href="#t116">116</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t117" href="#t117">117</a></span><span class="t">    <span class="nam">model_config</span> <span class="op">=</span> <span class="nam">ConfigDict</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs117" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t118" href="#t118">118</a></span><span class="t">        <span class="nam">json_schema_extra</span><span class="op">=</span><span class="op">{</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t119" href="#t119">119</a></span><span class="t">            <span class="str">"example"</span><span class="op">:</span> <span class="op">{</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t120" href="#t120">120</a></span><span class="t">                <span class="str">"supply_voltage_v"</span><span class="op">:</span> <span class="num">240.0</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t121" href="#t121">121</a></span><span class="t">                <span class="str">"load_current_a"</span><span class="op">:</span> <span class="num">10.4</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t122" href="#t122">122</a></span><span class="t">                <span class="str">"cable_length_m"</span><span class="op">:</span> <span class="num">100.0</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t123" href="#t123">123</a></span><span class="t">                <span class="str">"cable_resistance_ohm_per_m"</span><span class="op">:</span> <span class="num">0.0184</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t124" href="#t124">124</a></span><span class="t">                <span class="str">"cable_reactance_ohm_per_m"</span><span class="op">:</span> <span class="num">0.0</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t125" href="#t125">125</a></span><span class="t">                <span class="str">"power_factor"</span><span class="op">:</span> <span class="num">1.0</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t126" href="#t126">126</a></span><span class="t">                <span class="str">"ambient_temperature_c"</span><span class="op">:</span> <span class="num">25.0</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t127" href="#t127">127</a></span><span class="t">                <span class="str">"derating_factor"</span><span class="op">:</span> <span class="num">1.0</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t128" href="#t128">128</a></span><span class="t">                <span class="str">"max_voltage_drop_percent"</span><span class="op">:</span> <span class="num">5.0</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t129" href="#t129">129</a></span><span class="t">            <span class="op">}</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t130" href="#t130">130</a></span><span class="t">        <span class="op">}</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t131" href="#t131">131</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t132" href="#t132">132</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t133" href="#t133">133</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t134" href="#t134">134</a></span><span class="t"><span class="key">class</span> <span class="nam">VoltageDropCalculationResultSchema</span><span class="op">(</span><span class="nam">BaseModel</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"><label for="ctxs134" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t135" href="#t135">135</a></span><span class="t">    <span class="str">"""Schema for voltage drop calculation results."""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t136" href="#t136">136</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t137" href="#t137">137</a></span><span class="t">    <span class="nam">calculated_voltage_drop_v</span><span class="op">:</span> <span class="nam">float</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="op">...</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Voltage drop in volts"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs137" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t138" href="#t138">138</a></span><span class="t">    <span class="nam">calculated_voltage_drop_percent</span><span class="op">:</span> <span class="nam">float</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs138" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t139" href="#t139">139</a></span><span class="t">        <span class="op">...</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Voltage drop percentage"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t140" href="#t140">140</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t141" href="#t141">141</a></span><span class="t">    <span class="nam">calculated_power_loss_w</span><span class="op">:</span> <span class="nam">float</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="op">...</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Power loss in watts"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs141" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t142" href="#t142">142</a></span><span class="t">    <span class="nam">calculated_efficiency_percent</span><span class="op">:</span> <span class="nam">float</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs142" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t143" href="#t143">143</a></span><span class="t">        <span class="op">...</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Cable efficiency percentage"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t144" href="#t144">144</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t145" href="#t145">145</a></span><span class="t">    <span class="nam">is_compliant</span><span class="op">:</span> <span class="nam">bool</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="op">...</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Meets voltage drop requirements"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs145" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t146" href="#t146">146</a></span><span class="t">    <span class="nam">compliance_margin_percent</span><span class="op">:</span> <span class="nam">float</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs146" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t147" href="#t147">147</a></span><span class="t">        <span class="op">...</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Compliance margin percentage"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t148" href="#t148">148</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t149" href="#t149">149</a></span><span class="t">    <span class="nam">calculation_method</span><span class="op">:</span> <span class="nam">str</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="op">...</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Calculation method used"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs149" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t150" href="#t150">150</a></span><span class="t">    <span class="nam">calculation_metadata</span><span class="op">:</span> <span class="nam">Dict</span><span class="op">[</span><span class="nam">str</span><span class="op">,</span> <span class="nam">Any</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs150" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t151" href="#t151">151</a></span><span class="t">        <span class="nam">default_factory</span><span class="op">=</span><span class="nam">dict</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Calculation metadata"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t152" href="#t152">152</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t153" href="#t153">153</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t154" href="#t154">154</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t155" href="#t155">155</a></span><span class="t"><span class="key">class</span> <span class="nam">ElectricalStandardsValidationInputSchema</span><span class="op">(</span><span class="nam">BaseModel</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"><label for="ctxs155" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t156" href="#t156">156</a></span><span class="t">    <span class="str">"""Schema for electrical standards validation inputs."""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t157" href="#t157">157</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t158" href="#t158">158</a></span><span class="t">    <span class="nam">cable_sizing_result</span><span class="op">:</span> <span class="nam">CableSizingCalculationResultSchema</span>&nbsp;</span><span class="r"><label for="ctxs158" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t159" href="#t159">159</a></span><span class="t">    <span class="nam">voltage_drop_result</span><span class="op">:</span> <span class="nam">VoltageDropCalculationResultSchema</span>&nbsp;</span><span class="r"><label for="ctxs159" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t160" href="#t160">160</a></span><span class="t">    <span class="nam">design_parameters</span><span class="op">:</span> <span class="nam">Dict</span><span class="op">[</span><span class="nam">str</span><span class="op">,</span> <span class="nam">Any</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="nam">default_factory</span><span class="op">=</span><span class="nam">dict</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs160" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t161" href="#t161">161</a></span><span class="t">    <span class="nam">project_standards</span><span class="op">:</span> <span class="nam">List</span><span class="op">[</span><span class="nam">str</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="nam">default_factory</span><span class="op">=</span><span class="nam">list</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs161" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t162" href="#t162">162</a></span><span class="t">    <span class="nam">hazardous_area_zone</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">str</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="key">None</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Hazardous area zone"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs162" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t163" href="#t163">163</a></span><span class="t">    <span class="nam">gas_group</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">str</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="key">None</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Gas group classification"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs163" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t164" href="#t164">164</a></span><span class="t">    <span class="nam">temperature_class</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">str</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="key">None</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Temperature class"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs164" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t165" href="#t165">165</a></span><span class="t">    <span class="nam">installation_environment</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">str</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs165" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t166" href="#t166">166</a></span><span class="t">        <span class="key">None</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Installation environment"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t167" href="#t167">167</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t168" href="#t168">168</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t169" href="#t169">169</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t170" href="#t170">170</a></span><span class="t"><span class="key">class</span> <span class="nam">ElectricalStandardsValidationResultSchema</span><span class="op">(</span><span class="nam">BaseModel</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"><label for="ctxs170" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t171" href="#t171">171</a></span><span class="t">    <span class="str">"""Schema for electrical standards validation results."""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t172" href="#t172">172</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t173" href="#t173">173</a></span><span class="t">    <span class="nam">is_compliant</span><span class="op">:</span> <span class="nam">bool</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="op">...</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Overall compliance status"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs173" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t174" href="#t174">174</a></span><span class="t">    <span class="nam">standard</span><span class="op">:</span> <span class="nam">str</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="op">...</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Applied standard"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs174" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t175" href="#t175">175</a></span><span class="t">    <span class="nam">violations</span><span class="op">:</span> <span class="nam">List</span><span class="op">[</span><span class="nam">str</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs175" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t176" href="#t176">176</a></span><span class="t">        <span class="nam">default_factory</span><span class="op">=</span><span class="nam">list</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Compliance violations"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t177" href="#t177">177</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t178" href="#t178">178</a></span><span class="t">    <span class="nam">warnings</span><span class="op">:</span> <span class="nam">List</span><span class="op">[</span><span class="nam">str</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="nam">default_factory</span><span class="op">=</span><span class="nam">list</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Compliance warnings"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs178" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t179" href="#t179">179</a></span><span class="t">    <span class="nam">applied_factors</span><span class="op">:</span> <span class="nam">Dict</span><span class="op">[</span><span class="nam">str</span><span class="op">,</span> <span class="nam">float</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs179" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t180" href="#t180">180</a></span><span class="t">        <span class="nam">default_factory</span><span class="op">=</span><span class="nam">dict</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Applied safety factors"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t181" href="#t181">181</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t182" href="#t182">182</a></span><span class="t">    <span class="nam">recommendations</span><span class="op">:</span> <span class="nam">List</span><span class="op">[</span><span class="nam">str</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs182" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t183" href="#t183">183</a></span><span class="t">        <span class="nam">default_factory</span><span class="op">=</span><span class="nam">list</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Design recommendations"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t184" href="#t184">184</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t185" href="#t185">185</a></span><span class="t">    <span class="nam">metadata</span><span class="op">:</span> <span class="nam">Dict</span><span class="op">[</span><span class="nam">str</span><span class="op">,</span> <span class="nam">Any</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs185" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t186" href="#t186">186</a></span><span class="t">        <span class="nam">default_factory</span><span class="op">=</span><span class="nam">dict</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Validation metadata"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t187" href="#t187">187</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t188" href="#t188">188</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t189" href="#t189">189</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t190" href="#t190">190</a></span><span class="t"><span class="com"># ============================================================================</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t191" href="#t191">191</a></span><span class="t"><span class="com"># ELECTRICAL NODE SCHEMAS</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t192" href="#t192">192</a></span><span class="t"><span class="com"># ============================================================================</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t193" href="#t193">193</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t194" href="#t194">194</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t195" href="#t195">195</a></span><span class="t"><span class="key">class</span> <span class="nam">ElectricalNodeBaseSchema</span><span class="op">(</span><span class="nam">BaseModel</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"><label for="ctxs195" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t196" href="#t196">196</a></span><span class="t">    <span class="str">"""Base schema for ElectricalNode with common fields."""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t197" href="#t197">197</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t198" href="#t198">198</a></span><span class="t">    <span class="nam">name</span><span class="op">:</span> <span class="nam">str</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs198" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t199" href="#t199">199</a></span><span class="t">        <span class="op">...</span><span class="op">,</span> <span class="nam">min_length</span><span class="op">=</span><span class="num">3</span><span class="op">,</span> <span class="nam">max_length</span><span class="op">=</span><span class="num">100</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Electrical node name/identifier"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t200" href="#t200">200</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t201" href="#t201">201</a></span><span class="t">    <span class="nam">node_type</span><span class="op">:</span> <span class="nam">ElectricalNodeType</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="op">...</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Electrical node type"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs201" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t202" href="#t202">202</a></span><span class="t">    <span class="nam">location_description</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">str</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs202" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t203" href="#t203">203</a></span><span class="t">        <span class="key">None</span><span class="op">,</span> <span class="nam">max_length</span><span class="op">=</span><span class="num">200</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Location description"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t204" href="#t204">204</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t205" href="#t205">205</a></span><span class="t">    <span class="nam">voltage_v</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">float</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="key">None</span><span class="op">,</span> <span class="nam">gt</span><span class="op">=</span><span class="num">0</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Voltage in volts"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs205" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t206" href="#t206">206</a></span><span class="t">    <span class="nam">power_capacity_kva</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">float</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs206" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t207" href="#t207">207</a></span><span class="t">        <span class="key">None</span><span class="op">,</span> <span class="nam">gt</span><span class="op">=</span><span class="num">0</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Power capacity in kVA"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t208" href="#t208">208</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t209" href="#t209">209</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t210" href="#t210">210</a></span><span class="t">    <span class="op">@</span><span class="nam">field_validator</span><span class="op">(</span><span class="str">"name"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs210" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t211" href="#t211">211</a></span><span class="t">    <span class="op">@</span><span class="nam">classmethod</span>&nbsp;</span><span class="r"><label for="ctxs211" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t212" href="#t212">212</a></span><span class="t">    <span class="key">def</span> <span class="nam">validate_name</span><span class="op">(</span><span class="nam">cls</span><span class="op">,</span> <span class="nam">v</span><span class="op">:</span> <span class="nam">str</span><span class="op">)</span> <span class="op">-></span> <span class="nam">str</span><span class="op">:</span>&nbsp;</span><span class="r"><label for="ctxs212" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t213" href="#t213">213</a></span><span class="t">        <span class="str">"""Validate electrical node name."""</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t214" href="#t214">214</a></span><span class="t">        <span class="key">if</span> <span class="key">not</span> <span class="nam">v</span> <span class="key">or</span> <span class="key">not</span> <span class="nam">v</span><span class="op">.</span><span class="nam">strip</span><span class="op">(</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t215" href="#t215">215</a></span><span class="t">            <span class="key">raise</span> <span class="nam">ValueError</span><span class="op">(</span><span class="str">"Electrical node name cannot be empty"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t216" href="#t216">216</a></span><span class="t">        <span class="key">return</span> <span class="nam">v</span><span class="op">.</span><span class="nam">strip</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t217" href="#t217">217</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t218" href="#t218">218</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t219" href="#t219">219</a></span><span class="t"><span class="key">class</span> <span class="nam">ElectricalNodeCreateSchema</span><span class="op">(</span><span class="nam">ElectricalNodeBaseSchema</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"><label for="ctxs219" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t220" href="#t220">220</a></span><span class="t">    <span class="str">"""Schema for creating a new electrical node."""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t221" href="#t221">221</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t222" href="#t222">222</a></span><span class="t">    <span class="nam">project_id</span><span class="op">:</span> <span class="nam">int</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="op">...</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Project ID"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs222" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t223" href="#t223">223</a></span><span class="t">    <span class="nam">related_switchboard_id</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">int</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs223" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t224" href="#t224">224</a></span><span class="t">        <span class="key">None</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Related switchboard ID"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t225" href="#t225">225</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t226" href="#t226">226</a></span><span class="t">    <span class="nam">related_feeder_id</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">int</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="key">None</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Related feeder ID"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs226" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t227" href="#t227">227</a></span><span class="t">    <span class="nam">related_control_circuit_id</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">int</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs227" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t228" href="#t228">228</a></span><span class="t">        <span class="key">None</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Related control circuit ID"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t229" href="#t229">229</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t230" href="#t230">230</a></span><span class="t">    <span class="nam">related_pipe_id</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">int</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="key">None</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Related pipe ID"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs230" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t231" href="#t231">231</a></span><span class="t">    <span class="nam">related_vessel_id</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">int</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="key">None</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Related vessel ID"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs231" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t232" href="#t232">232</a></span><span class="t">    <span class="nam">related_component_id</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">int</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs232" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t233" href="#t233">233</a></span><span class="t">        <span class="key">None</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Related component ID"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t234" href="#t234">234</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t235" href="#t235">235</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t236" href="#t236">236</a></span><span class="t">    <span class="nam">model_config</span> <span class="op">=</span> <span class="nam">ConfigDict</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs236" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t237" href="#t237">237</a></span><span class="t">        <span class="nam">json_schema_extra</span><span class="op">=</span><span class="op">{</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t238" href="#t238">238</a></span><span class="t">            <span class="str">"example"</span><span class="op">:</span> <span class="op">{</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t239" href="#t239">239</a></span><span class="t">                <span class="str">"name"</span><span class="op">:</span> <span class="str">"Main Switchboard Incoming"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t240" href="#t240">240</a></span><span class="t">                <span class="str">"project_id"</span><span class="op">:</span> <span class="num">1</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t241" href="#t241">241</a></span><span class="t">                <span class="str">"node_type"</span><span class="op">:</span> <span class="str">"SWITCHBOARD_INCOMING"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t242" href="#t242">242</a></span><span class="t">                <span class="str">"location_description"</span><span class="op">:</span> <span class="str">"Main electrical room"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t243" href="#t243">243</a></span><span class="t">                <span class="str">"voltage_v"</span><span class="op">:</span> <span class="num">415.0</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t244" href="#t244">244</a></span><span class="t">                <span class="str">"power_capacity_kva"</span><span class="op">:</span> <span class="num">100.0</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t245" href="#t245">245</a></span><span class="t">                <span class="str">"related_switchboard_id"</span><span class="op">:</span> <span class="num">1</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t246" href="#t246">246</a></span><span class="t">            <span class="op">}</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t247" href="#t247">247</a></span><span class="t">        <span class="op">}</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t248" href="#t248">248</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t249" href="#t249">249</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t250" href="#t250">250</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t251" href="#t251">251</a></span><span class="t"><span class="key">class</span> <span class="nam">ElectricalNodeUpdateSchema</span><span class="op">(</span><span class="nam">BaseModel</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"><label for="ctxs251" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t252" href="#t252">252</a></span><span class="t">    <span class="str">"""Schema for updating an existing electrical node."""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t253" href="#t253">253</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t254" href="#t254">254</a></span><span class="t">    <span class="nam">name</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">str</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs254" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t255" href="#t255">255</a></span><span class="t">        <span class="key">None</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t256" href="#t256">256</a></span><span class="t">        <span class="nam">min_length</span><span class="op">=</span><span class="num">3</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t257" href="#t257">257</a></span><span class="t">        <span class="nam">max_length</span><span class="op">=</span><span class="num">100</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t258" href="#t258">258</a></span><span class="t">        <span class="nam">description</span><span class="op">=</span><span class="str">"Electrical node name/identifier"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t259" href="#t259">259</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t260" href="#t260">260</a></span><span class="t">    <span class="nam">node_type</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">ElectricalNodeType</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs260" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t261" href="#t261">261</a></span><span class="t">        <span class="key">None</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Electrical node type"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t262" href="#t262">262</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t263" href="#t263">263</a></span><span class="t">    <span class="nam">location_description</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">str</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs263" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t264" href="#t264">264</a></span><span class="t">        <span class="key">None</span><span class="op">,</span> <span class="nam">max_length</span><span class="op">=</span><span class="num">200</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Location description"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t265" href="#t265">265</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t266" href="#t266">266</a></span><span class="t">    <span class="nam">voltage_v</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">float</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="key">None</span><span class="op">,</span> <span class="nam">gt</span><span class="op">=</span><span class="num">0</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Voltage in volts"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs266" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t267" href="#t267">267</a></span><span class="t">    <span class="nam">power_capacity_kva</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">float</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs267" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t268" href="#t268">268</a></span><span class="t">        <span class="key">None</span><span class="op">,</span> <span class="nam">gt</span><span class="op">=</span><span class="num">0</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Power capacity in kVA"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t269" href="#t269">269</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t270" href="#t270">270</a></span><span class="t">    <span class="nam">related_switchboard_id</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">int</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs270" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t271" href="#t271">271</a></span><span class="t">        <span class="key">None</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Related switchboard ID"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t272" href="#t272">272</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t273" href="#t273">273</a></span><span class="t">    <span class="nam">related_feeder_id</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">int</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="key">None</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Related feeder ID"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs273" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t274" href="#t274">274</a></span><span class="t">    <span class="nam">related_control_circuit_id</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">int</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs274" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t275" href="#t275">275</a></span><span class="t">        <span class="key">None</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Related control circuit ID"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t276" href="#t276">276</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t277" href="#t277">277</a></span><span class="t">    <span class="nam">related_pipe_id</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">int</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="key">None</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Related pipe ID"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs277" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t278" href="#t278">278</a></span><span class="t">    <span class="nam">related_vessel_id</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">int</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="key">None</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Related vessel ID"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs278" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t279" href="#t279">279</a></span><span class="t">    <span class="nam">related_component_id</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">int</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs279" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t280" href="#t280">280</a></span><span class="t">        <span class="key">None</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Related component ID"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t281" href="#t281">281</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t282" href="#t282">282</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t283" href="#t283">283</a></span><span class="t">    <span class="com"># Apply the same validators as create schema</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t284" href="#t284">284</a></span><span class="t">    <span class="nam">_validate_name</span> <span class="op">=</span> <span class="nam">field_validator</span><span class="op">(</span><span class="str">"name"</span><span class="op">)</span><span class="op">(</span><span class="nam">ElectricalNodeBaseSchema</span><span class="op">.</span><span class="nam">validate_name</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs284" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t285" href="#t285">285</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t286" href="#t286">286</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t287" href="#t287">287</a></span><span class="t"><span class="key">class</span> <span class="nam">ElectricalNodeReadSchema</span><span class="op">(</span><span class="nam">ElectricalNodeBaseSchema</span><span class="op">,</span> <span class="nam">BaseSoftDeleteSchema</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"><label for="ctxs287" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t288" href="#t288">288</a></span><span class="t">    <span class="str">"""Schema for reading/displaying electrical node data."""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t289" href="#t289">289</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t290" href="#t290">290</a></span><span class="t">    <span class="nam">project_id</span><span class="op">:</span> <span class="nam">int</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="op">...</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Project ID"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs290" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t291" href="#t291">291</a></span><span class="t">    <span class="nam">related_switchboard_id</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">int</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs291" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t292" href="#t292">292</a></span><span class="t">        <span class="key">None</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Related switchboard ID"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t293" href="#t293">293</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t294" href="#t294">294</a></span><span class="t">    <span class="nam">related_feeder_id</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">int</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="key">None</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Related feeder ID"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs294" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t295" href="#t295">295</a></span><span class="t">    <span class="nam">related_control_circuit_id</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">int</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs295" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t296" href="#t296">296</a></span><span class="t">        <span class="key">None</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Related control circuit ID"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t297" href="#t297">297</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t298" href="#t298">298</a></span><span class="t">    <span class="nam">related_pipe_id</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">int</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="key">None</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Related pipe ID"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs298" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t299" href="#t299">299</a></span><span class="t">    <span class="nam">related_vessel_id</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">int</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="key">None</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Related vessel ID"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs299" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t300" href="#t300">300</a></span><span class="t">    <span class="nam">related_component_id</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">int</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs300" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t301" href="#t301">301</a></span><span class="t">        <span class="key">None</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Related component ID"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t302" href="#t302">302</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t303" href="#t303">303</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t304" href="#t304">304</a></span><span class="t">    <span class="nam">model_config</span> <span class="op">=</span> <span class="nam">ConfigDict</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs304" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t305" href="#t305">305</a></span><span class="t">        <span class="nam">from_attributes</span><span class="op">=</span><span class="key">True</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t306" href="#t306">306</a></span><span class="t">        <span class="nam">json_schema_extra</span><span class="op">=</span><span class="op">{</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t307" href="#t307">307</a></span><span class="t">            <span class="str">"example"</span><span class="op">:</span> <span class="op">{</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t308" href="#t308">308</a></span><span class="t">                <span class="str">"id"</span><span class="op">:</span> <span class="num">1</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t309" href="#t309">309</a></span><span class="t">                <span class="str">"name"</span><span class="op">:</span> <span class="str">"Main Switchboard Incoming"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t310" href="#t310">310</a></span><span class="t">                <span class="str">"project_id"</span><span class="op">:</span> <span class="num">1</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t311" href="#t311">311</a></span><span class="t">                <span class="str">"node_type"</span><span class="op">:</span> <span class="str">"SWITCHBOARD_INCOMING"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t312" href="#t312">312</a></span><span class="t">                <span class="str">"location_description"</span><span class="op">:</span> <span class="str">"Main electrical room"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t313" href="#t313">313</a></span><span class="t">                <span class="str">"voltage_v"</span><span class="op">:</span> <span class="num">415.0</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t314" href="#t314">314</a></span><span class="t">                <span class="str">"power_capacity_kva"</span><span class="op">:</span> <span class="num">100.0</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t315" href="#t315">315</a></span><span class="t">                <span class="str">"related_switchboard_id"</span><span class="op">:</span> <span class="num">1</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t316" href="#t316">316</a></span><span class="t">                <span class="str">"created_at"</span><span class="op">:</span> <span class="str">"2024-01-15T10:30:00Z"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t317" href="#t317">317</a></span><span class="t">                <span class="str">"updated_at"</span><span class="op">:</span> <span class="str">"2024-01-15T10:30:00Z"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t318" href="#t318">318</a></span><span class="t">                <span class="str">"is_deleted"</span><span class="op">:</span> <span class="key">False</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t319" href="#t319">319</a></span><span class="t">                <span class="str">"deleted_at"</span><span class="op">:</span> <span class="key">None</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t320" href="#t320">320</a></span><span class="t">                <span class="str">"deleted_by_user_id"</span><span class="op">:</span> <span class="key">None</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t321" href="#t321">321</a></span><span class="t">            <span class="op">}</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t322" href="#t322">322</a></span><span class="t">        <span class="op">}</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t323" href="#t323">323</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t324" href="#t324">324</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t325" href="#t325">325</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t326" href="#t326">326</a></span><span class="t"><span class="key">class</span> <span class="nam">ElectricalNodeSummarySchema</span><span class="op">(</span><span class="nam">BaseModel</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"><label for="ctxs326" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t327" href="#t327">327</a></span><span class="t">    <span class="str">"""Lightweight schema for electrical node listings."""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t328" href="#t328">328</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t329" href="#t329">329</a></span><span class="t">    <span class="nam">id</span><span class="op">:</span> <span class="nam">int</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="op">...</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Electrical node ID"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs329" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t330" href="#t330">330</a></span><span class="t">    <span class="nam">name</span><span class="op">:</span> <span class="nam">str</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="op">...</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Electrical node name"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs330" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t331" href="#t331">331</a></span><span class="t">    <span class="nam">project_id</span><span class="op">:</span> <span class="nam">int</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="op">...</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Project ID"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs331" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t332" href="#t332">332</a></span><span class="t">    <span class="nam">node_type</span><span class="op">:</span> <span class="nam">ElectricalNodeType</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="op">...</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Electrical node type"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs332" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t333" href="#t333">333</a></span><span class="t">    <span class="nam">voltage_v</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">float</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="key">None</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Voltage in volts"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs333" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t334" href="#t334">334</a></span><span class="t">    <span class="nam">power_capacity_kva</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">float</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs334" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t335" href="#t335">335</a></span><span class="t">        <span class="key">None</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Power capacity in kVA"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t336" href="#t336">336</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t337" href="#t337">337</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t338" href="#t338">338</a></span><span class="t">    <span class="nam">model_config</span> <span class="op">=</span> <span class="nam">ConfigDict</span><span class="op">(</span><span class="nam">from_attributes</span><span class="op">=</span><span class="key">True</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs338" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t339" href="#t339">339</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t340" href="#t340">340</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t341" href="#t341">341</a></span><span class="t"><span class="com"># ============================================================================</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t342" href="#t342">342</a></span><span class="t"><span class="com"># CABLE ROUTE SCHEMAS</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t343" href="#t343">343</a></span><span class="t"><span class="com"># ============================================================================</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t344" href="#t344">344</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t345" href="#t345">345</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t346" href="#t346">346</a></span><span class="t"><span class="key">class</span> <span class="nam">CableRouteBaseSchema</span><span class="op">(</span><span class="nam">BaseModel</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"><label for="ctxs346" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t347" href="#t347">347</a></span><span class="t">    <span class="str">"""Base schema for CableRoute with common fields."""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t348" href="#t348">348</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t349" href="#t349">349</a></span><span class="t">    <span class="nam">name</span><span class="op">:</span> <span class="nam">str</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs349" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t350" href="#t350">350</a></span><span class="t">        <span class="op">...</span><span class="op">,</span> <span class="nam">min_length</span><span class="op">=</span><span class="num">3</span><span class="op">,</span> <span class="nam">max_length</span><span class="op">=</span><span class="num">100</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Cable route name/identifier"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t351" href="#t351">351</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t352" href="#t352">352</a></span><span class="t">    <span class="nam">length_m</span><span class="op">:</span> <span class="nam">float</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="op">...</span><span class="op">,</span> <span class="nam">gt</span><span class="op">=</span><span class="num">0</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Cable route length in meters"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs352" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t353" href="#t353">353</a></span><span class="t">    <span class="nam">number_of_runs</span><span class="op">:</span> <span class="nam">int</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="num">1</span><span class="op">,</span> <span class="nam">ge</span><span class="op">=</span><span class="num">1</span><span class="op">,</span> <span class="nam">le</span><span class="op">=</span><span class="num">10</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Number of cable runs"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs353" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t354" href="#t354">354</a></span><span class="t">    <span class="nam">installation_method</span><span class="op">:</span> <span class="nam">CableInstallationMethod</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs354" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t355" href="#t355">355</a></span><span class="t">        <span class="op">...</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Cable installation method"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t356" href="#t356">356</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t357" href="#t357">357</a></span><span class="t">    <span class="nam">max_ambient_temp_c</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">float</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs357" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t358" href="#t358">358</a></span><span class="t">        <span class="key">None</span><span class="op">,</span> <span class="nam">ge</span><span class="op">=</span><span class="op">-</span><span class="num">50</span><span class="op">,</span> <span class="nam">le</span><span class="op">=</span><span class="num">80</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Maximum ambient temperature in Celsius"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t359" href="#t359">359</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t360" href="#t360">360</a></span><span class="t">    <span class="nam">min_ambient_temp_c</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">float</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs360" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t361" href="#t361">361</a></span><span class="t">        <span class="key">None</span><span class="op">,</span> <span class="nam">ge</span><span class="op">=</span><span class="op">-</span><span class="num">50</span><span class="op">,</span> <span class="nam">le</span><span class="op">=</span><span class="num">80</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Minimum ambient temperature in Celsius"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t362" href="#t362">362</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t363" href="#t363">363</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t364" href="#t364">364</a></span><span class="t">    <span class="op">@</span><span class="nam">field_validator</span><span class="op">(</span><span class="str">"name"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs364" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t365" href="#t365">365</a></span><span class="t">    <span class="op">@</span><span class="nam">classmethod</span>&nbsp;</span><span class="r"><label for="ctxs365" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t366" href="#t366">366</a></span><span class="t">    <span class="key">def</span> <span class="nam">validate_name</span><span class="op">(</span><span class="nam">cls</span><span class="op">,</span> <span class="nam">v</span><span class="op">:</span> <span class="nam">str</span><span class="op">)</span> <span class="op">-></span> <span class="nam">str</span><span class="op">:</span>&nbsp;</span><span class="r"><label for="ctxs366" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t367" href="#t367">367</a></span><span class="t">        <span class="str">"""Validate cable route name."""</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t368" href="#t368">368</a></span><span class="t">        <span class="key">if</span> <span class="key">not</span> <span class="nam">v</span> <span class="key">or</span> <span class="key">not</span> <span class="nam">v</span><span class="op">.</span><span class="nam">strip</span><span class="op">(</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t369" href="#t369">369</a></span><span class="t">            <span class="key">raise</span> <span class="nam">ValueError</span><span class="op">(</span><span class="str">"Cable route name cannot be empty"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t370" href="#t370">370</a></span><span class="t">        <span class="key">return</span> <span class="nam">v</span><span class="op">.</span><span class="nam">strip</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t371" href="#t371">371</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t372" href="#t372">372</a></span><span class="t">    <span class="op">@</span><span class="nam">model_validator</span><span class="op">(</span><span class="nam">mode</span><span class="op">=</span><span class="str">"after"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs372" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t373" href="#t373">373</a></span><span class="t">    <span class="key">def</span> <span class="nam">validate_temperature_range</span><span class="op">(</span><span class="nam">self</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"><label for="ctxs373" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t374" href="#t374">374</a></span><span class="t">        <span class="str">"""Validate temperature range."""</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t375" href="#t375">375</a></span><span class="t">        <span class="key">if</span> <span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t376" href="#t376">376</a></span><span class="t">            <span class="nam">self</span><span class="op">.</span><span class="nam">max_ambient_temp_c</span> <span class="key">is</span> <span class="key">not</span> <span class="key">None</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t377" href="#t377">377</a></span><span class="t">            <span class="key">and</span> <span class="nam">self</span><span class="op">.</span><span class="nam">min_ambient_temp_c</span> <span class="key">is</span> <span class="key">not</span> <span class="key">None</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t378" href="#t378">378</a></span><span class="t">            <span class="key">and</span> <span class="nam">self</span><span class="op">.</span><span class="nam">max_ambient_temp_c</span> <span class="op">&lt;=</span> <span class="nam">self</span><span class="op">.</span><span class="nam">min_ambient_temp_c</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t379" href="#t379">379</a></span><span class="t">        <span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t380" href="#t380">380</a></span><span class="t">            <span class="key">raise</span> <span class="nam">ValueError</span><span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t381" href="#t381">381</a></span><span class="t">                <span class="str">"Maximum ambient temperature must be higher than minimum ambient temperature"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t382" href="#t382">382</a></span><span class="t">            <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t383" href="#t383">383</a></span><span class="t">        <span class="key">return</span> <span class="nam">self</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t384" href="#t384">384</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t385" href="#t385">385</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t386" href="#t386">386</a></span><span class="t"><span class="key">class</span> <span class="nam">CableRouteCreateSchema</span><span class="op">(</span><span class="nam">CableRouteBaseSchema</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"><label for="ctxs386" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t387" href="#t387">387</a></span><span class="t">    <span class="str">"""Schema for creating a new cable route."""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t388" href="#t388">388</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t389" href="#t389">389</a></span><span class="t">    <span class="nam">project_id</span><span class="op">:</span> <span class="nam">int</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="op">...</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Project ID"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs389" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t390" href="#t390">390</a></span><span class="t">    <span class="nam">from_node_id</span><span class="op">:</span> <span class="nam">int</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="op">...</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"From electrical node ID"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs390" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t391" href="#t391">391</a></span><span class="t">    <span class="nam">to_node_id</span><span class="op">:</span> <span class="nam">int</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="op">...</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"To electrical node ID"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs391" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t392" href="#t392">392</a></span><span class="t">    <span class="nam">cable_component_id</span><span class="op">:</span> <span class="nam">int</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="op">...</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Cable component ID"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs392" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t393" href="#t393">393</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t394" href="#t394">394</a></span><span class="t">    <span class="op">@</span><span class="nam">model_validator</span><span class="op">(</span><span class="nam">mode</span><span class="op">=</span><span class="str">"after"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs394" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t395" href="#t395">395</a></span><span class="t">    <span class="key">def</span> <span class="nam">validate_different_nodes</span><span class="op">(</span><span class="nam">self</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"><label for="ctxs395" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t396" href="#t396">396</a></span><span class="t">        <span class="str">"""Validate that from and to nodes are different."""</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t397" href="#t397">397</a></span><span class="t">        <span class="key">if</span> <span class="nam">self</span><span class="op">.</span><span class="nam">from_node_id</span> <span class="op">==</span> <span class="nam">self</span><span class="op">.</span><span class="nam">to_node_id</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t398" href="#t398">398</a></span><span class="t">            <span class="key">raise</span> <span class="nam">ValueError</span><span class="op">(</span><span class="str">"From node and to node must be different"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t399" href="#t399">399</a></span><span class="t">        <span class="key">return</span> <span class="nam">self</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t400" href="#t400">400</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t401" href="#t401">401</a></span><span class="t">    <span class="nam">model_config</span> <span class="op">=</span> <span class="nam">ConfigDict</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs401" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t402" href="#t402">402</a></span><span class="t">        <span class="nam">json_schema_extra</span><span class="op">=</span><span class="op">{</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t403" href="#t403">403</a></span><span class="t">            <span class="str">"example"</span><span class="op">:</span> <span class="op">{</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t404" href="#t404">404</a></span><span class="t">                <span class="str">"name"</span><span class="op">:</span> <span class="str">"Main Feed to HT Panel"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t405" href="#t405">405</a></span><span class="t">                <span class="str">"project_id"</span><span class="op">:</span> <span class="num">1</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t406" href="#t406">406</a></span><span class="t">                <span class="str">"from_node_id"</span><span class="op">:</span> <span class="num">1</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t407" href="#t407">407</a></span><span class="t">                <span class="str">"to_node_id"</span><span class="op">:</span> <span class="num">2</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t408" href="#t408">408</a></span><span class="t">                <span class="str">"cable_component_id"</span><span class="op">:</span> <span class="num">25</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t409" href="#t409">409</a></span><span class="t">                <span class="str">"length_m"</span><span class="op">:</span> <span class="num">150.0</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t410" href="#t410">410</a></span><span class="t">                <span class="str">"number_of_runs"</span><span class="op">:</span> <span class="num">1</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t411" href="#t411">411</a></span><span class="t">                <span class="str">"installation_method"</span><span class="op">:</span> <span class="str">"CABLE_TRAY"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t412" href="#t412">412</a></span><span class="t">                <span class="str">"max_ambient_temp_c"</span><span class="op">:</span> <span class="num">40.0</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t413" href="#t413">413</a></span><span class="t">                <span class="str">"min_ambient_temp_c"</span><span class="op">:</span> <span class="op">-</span><span class="num">10.0</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t414" href="#t414">414</a></span><span class="t">            <span class="op">}</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t415" href="#t415">415</a></span><span class="t">        <span class="op">}</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t416" href="#t416">416</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t417" href="#t417">417</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t418" href="#t418">418</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t419" href="#t419">419</a></span><span class="t"><span class="key">class</span> <span class="nam">CableRouteUpdateSchema</span><span class="op">(</span><span class="nam">BaseModel</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"><label for="ctxs419" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t420" href="#t420">420</a></span><span class="t">    <span class="str">"""Schema for updating an existing cable route."""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t421" href="#t421">421</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t422" href="#t422">422</a></span><span class="t">    <span class="nam">name</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">str</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs422" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t423" href="#t423">423</a></span><span class="t">        <span class="key">None</span><span class="op">,</span> <span class="nam">min_length</span><span class="op">=</span><span class="num">3</span><span class="op">,</span> <span class="nam">max_length</span><span class="op">=</span><span class="num">100</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Cable route name/identifier"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t424" href="#t424">424</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t425" href="#t425">425</a></span><span class="t">    <span class="nam">from_node_id</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">int</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="key">None</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"From electrical node ID"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs425" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t426" href="#t426">426</a></span><span class="t">    <span class="nam">to_node_id</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">int</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="key">None</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"To electrical node ID"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs426" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t427" href="#t427">427</a></span><span class="t">    <span class="nam">cable_component_id</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">int</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="key">None</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Cable component ID"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs427" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t428" href="#t428">428</a></span><span class="t">    <span class="nam">length_m</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">float</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs428" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t429" href="#t429">429</a></span><span class="t">        <span class="key">None</span><span class="op">,</span> <span class="nam">gt</span><span class="op">=</span><span class="num">0</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Cable route length in meters"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t430" href="#t430">430</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t431" href="#t431">431</a></span><span class="t">    <span class="nam">number_of_runs</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">int</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs431" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t432" href="#t432">432</a></span><span class="t">        <span class="key">None</span><span class="op">,</span> <span class="nam">ge</span><span class="op">=</span><span class="num">1</span><span class="op">,</span> <span class="nam">le</span><span class="op">=</span><span class="num">10</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Number of cable runs"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t433" href="#t433">433</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t434" href="#t434">434</a></span><span class="t">    <span class="nam">installation_method</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">CableInstallationMethod</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs434" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t435" href="#t435">435</a></span><span class="t">        <span class="key">None</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Cable installation method"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t436" href="#t436">436</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t437" href="#t437">437</a></span><span class="t">    <span class="nam">max_ambient_temp_c</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">float</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs437" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t438" href="#t438">438</a></span><span class="t">        <span class="key">None</span><span class="op">,</span> <span class="nam">ge</span><span class="op">=</span><span class="op">-</span><span class="num">50</span><span class="op">,</span> <span class="nam">le</span><span class="op">=</span><span class="num">80</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Maximum ambient temperature in Celsius"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t439" href="#t439">439</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t440" href="#t440">440</a></span><span class="t">    <span class="nam">min_ambient_temp_c</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">float</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs440" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t441" href="#t441">441</a></span><span class="t">        <span class="key">None</span><span class="op">,</span> <span class="nam">ge</span><span class="op">=</span><span class="op">-</span><span class="num">50</span><span class="op">,</span> <span class="nam">le</span><span class="op">=</span><span class="num">80</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Minimum ambient temperature in Celsius"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t442" href="#t442">442</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t443" href="#t443">443</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t444" href="#t444">444</a></span><span class="t">    <span class="com"># Apply the same validators as create schema</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t445" href="#t445">445</a></span><span class="t">    <span class="nam">_validate_name</span> <span class="op">=</span> <span class="nam">field_validator</span><span class="op">(</span><span class="str">"name"</span><span class="op">)</span><span class="op">(</span><span class="nam">CableRouteBaseSchema</span><span class="op">.</span><span class="nam">validate_name</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs445" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t446" href="#t446">446</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t447" href="#t447">447</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t448" href="#t448">448</a></span><span class="t"><span class="key">class</span> <span class="nam">CableRouteReadSchema</span><span class="op">(</span><span class="nam">CableRouteBaseSchema</span><span class="op">,</span> <span class="nam">BaseSoftDeleteSchema</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"><label for="ctxs448" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t449" href="#t449">449</a></span><span class="t">    <span class="str">"""Schema for reading/displaying cable route data."""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t450" href="#t450">450</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t451" href="#t451">451</a></span><span class="t">    <span class="nam">project_id</span><span class="op">:</span> <span class="nam">int</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="op">...</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Project ID"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs451" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t452" href="#t452">452</a></span><span class="t">    <span class="nam">from_node_id</span><span class="op">:</span> <span class="nam">int</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="op">...</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"From electrical node ID"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs452" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t453" href="#t453">453</a></span><span class="t">    <span class="nam">to_node_id</span><span class="op">:</span> <span class="nam">int</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="op">...</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"To electrical node ID"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs453" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t454" href="#t454">454</a></span><span class="t">    <span class="nam">cable_component_id</span><span class="op">:</span> <span class="nam">int</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="op">...</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Cable component ID"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs454" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t455" href="#t455">455</a></span><span class="t">    <span class="nam">calculated_voltage_drop_v</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">float</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs455" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t456" href="#t456">456</a></span><span class="t">        <span class="key">None</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Calculated voltage drop in volts"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t457" href="#t457">457</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t458" href="#t458">458</a></span><span class="t">    <span class="nam">calculated_current_capacity_a</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">float</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs458" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t459" href="#t459">459</a></span><span class="t">        <span class="key">None</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Calculated current capacity in amperes"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t460" href="#t460">460</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t461" href="#t461">461</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t462" href="#t462">462</a></span><span class="t">    <span class="nam">model_config</span> <span class="op">=</span> <span class="nam">ConfigDict</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs462" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t463" href="#t463">463</a></span><span class="t">        <span class="nam">from_attributes</span><span class="op">=</span><span class="key">True</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t464" href="#t464">464</a></span><span class="t">        <span class="nam">json_schema_extra</span><span class="op">=</span><span class="op">{</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t465" href="#t465">465</a></span><span class="t">            <span class="str">"example"</span><span class="op">:</span> <span class="op">{</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t466" href="#t466">466</a></span><span class="t">                <span class="str">"id"</span><span class="op">:</span> <span class="num">1</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t467" href="#t467">467</a></span><span class="t">                <span class="str">"name"</span><span class="op">:</span> <span class="str">"Main Feed to HT Panel"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t468" href="#t468">468</a></span><span class="t">                <span class="str">"project_id"</span><span class="op">:</span> <span class="num">1</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t469" href="#t469">469</a></span><span class="t">                <span class="str">"from_node_id"</span><span class="op">:</span> <span class="num">1</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t470" href="#t470">470</a></span><span class="t">                <span class="str">"to_node_id"</span><span class="op">:</span> <span class="num">2</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t471" href="#t471">471</a></span><span class="t">                <span class="str">"cable_component_id"</span><span class="op">:</span> <span class="num">25</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t472" href="#t472">472</a></span><span class="t">                <span class="str">"length_m"</span><span class="op">:</span> <span class="num">150.0</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t473" href="#t473">473</a></span><span class="t">                <span class="str">"number_of_runs"</span><span class="op">:</span> <span class="num">1</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t474" href="#t474">474</a></span><span class="t">                <span class="str">"installation_method"</span><span class="op">:</span> <span class="str">"CABLE_TRAY"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t475" href="#t475">475</a></span><span class="t">                <span class="str">"calculated_voltage_drop_v"</span><span class="op">:</span> <span class="num">8.5</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t476" href="#t476">476</a></span><span class="t">                <span class="str">"calculated_current_capacity_a"</span><span class="op">:</span> <span class="num">32.0</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t477" href="#t477">477</a></span><span class="t">                <span class="str">"created_at"</span><span class="op">:</span> <span class="str">"2024-01-15T10:30:00Z"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t478" href="#t478">478</a></span><span class="t">                <span class="str">"updated_at"</span><span class="op">:</span> <span class="str">"2024-01-15T10:30:00Z"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t479" href="#t479">479</a></span><span class="t">                <span class="str">"is_deleted"</span><span class="op">:</span> <span class="key">False</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t480" href="#t480">480</a></span><span class="t">                <span class="str">"deleted_at"</span><span class="op">:</span> <span class="key">None</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t481" href="#t481">481</a></span><span class="t">                <span class="str">"deleted_by_user_id"</span><span class="op">:</span> <span class="key">None</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t482" href="#t482">482</a></span><span class="t">            <span class="op">}</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t483" href="#t483">483</a></span><span class="t">        <span class="op">}</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t484" href="#t484">484</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t485" href="#t485">485</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t486" href="#t486">486</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t487" href="#t487">487</a></span><span class="t"><span class="key">class</span> <span class="nam">CableRouteSummarySchema</span><span class="op">(</span><span class="nam">BaseModel</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"><label for="ctxs487" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t488" href="#t488">488</a></span><span class="t">    <span class="str">"""Lightweight schema for cable route listings."""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t489" href="#t489">489</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t490" href="#t490">490</a></span><span class="t">    <span class="nam">id</span><span class="op">:</span> <span class="nam">int</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="op">...</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Cable route ID"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs490" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t491" href="#t491">491</a></span><span class="t">    <span class="nam">name</span><span class="op">:</span> <span class="nam">str</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="op">...</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Cable route name"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs491" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t492" href="#t492">492</a></span><span class="t">    <span class="nam">project_id</span><span class="op">:</span> <span class="nam">int</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="op">...</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Project ID"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs492" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t493" href="#t493">493</a></span><span class="t">    <span class="nam">from_node_id</span><span class="op">:</span> <span class="nam">int</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="op">...</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"From electrical node ID"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs493" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t494" href="#t494">494</a></span><span class="t">    <span class="nam">to_node_id</span><span class="op">:</span> <span class="nam">int</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="op">...</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"To electrical node ID"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs494" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t495" href="#t495">495</a></span><span class="t">    <span class="nam">length_m</span><span class="op">:</span> <span class="nam">float</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="op">...</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Cable route length in meters"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs495" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t496" href="#t496">496</a></span><span class="t">    <span class="nam">calculated_voltage_drop_v</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">float</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs496" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t497" href="#t497">497</a></span><span class="t">        <span class="key">None</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Calculated voltage drop in volts"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t498" href="#t498">498</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t499" href="#t499">499</a></span><span class="t">    <span class="nam">installation_method</span><span class="op">:</span> <span class="nam">CableInstallationMethod</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs499" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t500" href="#t500">500</a></span><span class="t">        <span class="op">...</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Cable installation method"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t501" href="#t501">501</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t502" href="#t502">502</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t503" href="#t503">503</a></span><span class="t">    <span class="nam">model_config</span> <span class="op">=</span> <span class="nam">ConfigDict</span><span class="op">(</span><span class="nam">from_attributes</span><span class="op">=</span><span class="key">True</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs503" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t504" href="#t504">504</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t505" href="#t505">505</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t506" href="#t506">506</a></span><span class="t"><span class="com"># ============================================================================</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t507" href="#t507">507</a></span><span class="t"><span class="com"># CABLE SEGMENT SCHEMAS</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t508" href="#t508">508</a></span><span class="t"><span class="com"># ============================================================================</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t509" href="#t509">509</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t510" href="#t510">510</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t511" href="#t511">511</a></span><span class="t"><span class="key">class</span> <span class="nam">CableSegmentBaseSchema</span><span class="op">(</span><span class="nam">BaseModel</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"><label for="ctxs511" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t512" href="#t512">512</a></span><span class="t">    <span class="str">"""Base schema for CableSegment with common fields."""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t513" href="#t513">513</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t514" href="#t514">514</a></span><span class="t">    <span class="nam">name</span><span class="op">:</span> <span class="nam">str</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs514" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t515" href="#t515">515</a></span><span class="t">        <span class="op">...</span><span class="op">,</span> <span class="nam">min_length</span><span class="op">=</span><span class="num">3</span><span class="op">,</span> <span class="nam">max_length</span><span class="op">=</span><span class="num">100</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Cable segment name/identifier"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t516" href="#t516">516</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t517" href="#t517">517</a></span><span class="t">    <span class="nam">segment_order</span><span class="op">:</span> <span class="nam">int</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="num">1</span><span class="op">,</span> <span class="nam">ge</span><span class="op">=</span><span class="num">1</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Segment order in route"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs517" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t518" href="#t518">518</a></span><span class="t">    <span class="nam">length_m</span><span class="op">:</span> <span class="nam">float</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="op">...</span><span class="op">,</span> <span class="nam">gt</span><span class="op">=</span><span class="num">0</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Segment length in meters"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs518" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t519" href="#t519">519</a></span><span class="t">    <span class="nam">installation_method</span><span class="op">:</span> <span class="nam">CableInstallationMethod</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs519" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t520" href="#t520">520</a></span><span class="t">        <span class="op">...</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Cable installation method"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t521" href="#t521">521</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t522" href="#t522">522</a></span><span class="t">    <span class="nam">ambient_temperature_c</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">float</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs522" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t523" href="#t523">523</a></span><span class="t">        <span class="key">None</span><span class="op">,</span> <span class="nam">ge</span><span class="op">=</span><span class="op">-</span><span class="num">50</span><span class="op">,</span> <span class="nam">le</span><span class="op">=</span><span class="num">80</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Ambient temperature in Celsius"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t524" href="#t524">524</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t525" href="#t525">525</a></span><span class="t">    <span class="nam">ground_temperature_c</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">float</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs525" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t526" href="#t526">526</a></span><span class="t">        <span class="key">None</span><span class="op">,</span> <span class="nam">ge</span><span class="op">=</span><span class="op">-</span><span class="num">50</span><span class="op">,</span> <span class="nam">le</span><span class="op">=</span><span class="num">80</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Ground temperature in Celsius"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t527" href="#t527">527</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t528" href="#t528">528</a></span><span class="t">    <span class="nam">burial_depth_m</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">float</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs528" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t529" href="#t529">529</a></span><span class="t">        <span class="key">None</span><span class="op">,</span> <span class="nam">ge</span><span class="op">=</span><span class="num">0</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Burial depth in meters"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t530" href="#t530">530</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t531" href="#t531">531</a></span><span class="t">    <span class="nam">conductor_size_mm2</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">float</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs531" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t532" href="#t532">532</a></span><span class="t">        <span class="key">None</span><span class="op">,</span> <span class="nam">gt</span><span class="op">=</span><span class="num">0</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Conductor size in mm&#178;"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t533" href="#t533">533</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t534" href="#t534">534</a></span><span class="t">    <span class="nam">insulation_type</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">str</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs534" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t535" href="#t535">535</a></span><span class="t">        <span class="key">None</span><span class="op">,</span> <span class="nam">max_length</span><span class="op">=</span><span class="num">50</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Insulation type"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t536" href="#t536">536</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t537" href="#t537">537</a></span><span class="t">    <span class="nam">sheath_type</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">str</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="key">None</span><span class="op">,</span> <span class="nam">max_length</span><span class="op">=</span><span class="num">50</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Sheath type"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs537" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t538" href="#t538">538</a></span><span class="t">    <span class="nam">armour_type</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">str</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="key">None</span><span class="op">,</span> <span class="nam">max_length</span><span class="op">=</span><span class="num">50</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Armour type"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs538" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t539" href="#t539">539</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t540" href="#t540">540</a></span><span class="t">    <span class="op">@</span><span class="nam">field_validator</span><span class="op">(</span><span class="str">"name"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs540" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t541" href="#t541">541</a></span><span class="t">    <span class="op">@</span><span class="nam">classmethod</span>&nbsp;</span><span class="r"><label for="ctxs541" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t542" href="#t542">542</a></span><span class="t">    <span class="key">def</span> <span class="nam">validate_name</span><span class="op">(</span><span class="nam">cls</span><span class="op">,</span> <span class="nam">v</span><span class="op">:</span> <span class="nam">str</span><span class="op">)</span> <span class="op">-></span> <span class="nam">str</span><span class="op">:</span>&nbsp;</span><span class="r"><label for="ctxs542" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t543" href="#t543">543</a></span><span class="t">        <span class="str">"""Validate cable segment name."""</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t544" href="#t544">544</a></span><span class="t">        <span class="key">if</span> <span class="key">not</span> <span class="nam">v</span> <span class="key">or</span> <span class="key">not</span> <span class="nam">v</span><span class="op">.</span><span class="nam">strip</span><span class="op">(</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t545" href="#t545">545</a></span><span class="t">            <span class="key">raise</span> <span class="nam">ValueError</span><span class="op">(</span><span class="str">"Cable segment name cannot be empty"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t546" href="#t546">546</a></span><span class="t">        <span class="key">return</span> <span class="nam">v</span><span class="op">.</span><span class="nam">strip</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t547" href="#t547">547</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t548" href="#t548">548</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t549" href="#t549">549</a></span><span class="t"><span class="key">class</span> <span class="nam">CableSegmentCreateSchema</span><span class="op">(</span><span class="nam">CableSegmentBaseSchema</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"><label for="ctxs549" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t550" href="#t550">550</a></span><span class="t">    <span class="str">"""Schema for creating a new cable segment."""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t551" href="#t551">551</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t552" href="#t552">552</a></span><span class="t">    <span class="nam">project_id</span><span class="op">:</span> <span class="nam">int</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="op">...</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Project ID"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs552" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t553" href="#t553">553</a></span><span class="t">    <span class="nam">cable_route_id</span><span class="op">:</span> <span class="nam">int</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="op">...</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Cable route ID"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs553" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t554" href="#t554">554</a></span><span class="t">    <span class="nam">cable_component_id</span><span class="op">:</span> <span class="nam">int</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="op">...</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Cable component ID"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs554" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t555" href="#t555">555</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t556" href="#t556">556</a></span><span class="t">    <span class="nam">model_config</span> <span class="op">=</span> <span class="nam">ConfigDict</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs556" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t557" href="#t557">557</a></span><span class="t">        <span class="nam">json_schema_extra</span><span class="op">=</span><span class="op">{</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t558" href="#t558">558</a></span><span class="t">            <span class="str">"example"</span><span class="op">:</span> <span class="op">{</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t559" href="#t559">559</a></span><span class="t">                <span class="str">"name"</span><span class="op">:</span> <span class="str">"Segment 1 - Underground"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t560" href="#t560">560</a></span><span class="t">                <span class="str">"project_id"</span><span class="op">:</span> <span class="num">1</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t561" href="#t561">561</a></span><span class="t">                <span class="str">"cable_route_id"</span><span class="op">:</span> <span class="num">1</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t562" href="#t562">562</a></span><span class="t">                <span class="str">"cable_component_id"</span><span class="op">:</span> <span class="num">25</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t563" href="#t563">563</a></span><span class="t">                <span class="str">"segment_order"</span><span class="op">:</span> <span class="num">1</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t564" href="#t564">564</a></span><span class="t">                <span class="str">"length_m"</span><span class="op">:</span> <span class="num">50.0</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t565" href="#t565">565</a></span><span class="t">                <span class="str">"installation_method"</span><span class="op">:</span> <span class="str">"DIRECT_BURIED"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t566" href="#t566">566</a></span><span class="t">                <span class="str">"ambient_temperature_c"</span><span class="op">:</span> <span class="num">25.0</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t567" href="#t567">567</a></span><span class="t">                <span class="str">"ground_temperature_c"</span><span class="op">:</span> <span class="num">20.0</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t568" href="#t568">568</a></span><span class="t">                <span class="str">"burial_depth_m"</span><span class="op">:</span> <span class="num">0.8</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t569" href="#t569">569</a></span><span class="t">                <span class="str">"conductor_size_mm2"</span><span class="op">:</span> <span class="num">2.5</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t570" href="#t570">570</a></span><span class="t">                <span class="str">"insulation_type"</span><span class="op">:</span> <span class="str">"XLPE"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t571" href="#t571">571</a></span><span class="t">                <span class="str">"sheath_type"</span><span class="op">:</span> <span class="str">"PVC"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t572" href="#t572">572</a></span><span class="t">                <span class="str">"armour_type"</span><span class="op">:</span> <span class="str">"SWA"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t573" href="#t573">573</a></span><span class="t">            <span class="op">}</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t574" href="#t574">574</a></span><span class="t">        <span class="op">}</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t575" href="#t575">575</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t576" href="#t576">576</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t577" href="#t577">577</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t578" href="#t578">578</a></span><span class="t"><span class="key">class</span> <span class="nam">CableSegmentUpdateSchema</span><span class="op">(</span><span class="nam">BaseModel</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"><label for="ctxs578" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t579" href="#t579">579</a></span><span class="t">    <span class="str">"""Schema for updating an existing cable segment."""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t580" href="#t580">580</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t581" href="#t581">581</a></span><span class="t">    <span class="nam">name</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">str</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs581" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t582" href="#t582">582</a></span><span class="t">        <span class="key">None</span><span class="op">,</span> <span class="nam">min_length</span><span class="op">=</span><span class="num">3</span><span class="op">,</span> <span class="nam">max_length</span><span class="op">=</span><span class="num">100</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Cable segment name/identifier"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t583" href="#t583">583</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t584" href="#t584">584</a></span><span class="t">    <span class="nam">cable_component_id</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">int</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="key">None</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Cable component ID"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs584" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t585" href="#t585">585</a></span><span class="t">    <span class="nam">segment_order</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">int</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs585" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t586" href="#t586">586</a></span><span class="t">        <span class="key">None</span><span class="op">,</span> <span class="nam">ge</span><span class="op">=</span><span class="num">1</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Segment order in route"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t587" href="#t587">587</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t588" href="#t588">588</a></span><span class="t">    <span class="nam">length_m</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">float</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs588" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t589" href="#t589">589</a></span><span class="t">        <span class="key">None</span><span class="op">,</span> <span class="nam">gt</span><span class="op">=</span><span class="num">0</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Segment length in meters"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t590" href="#t590">590</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t591" href="#t591">591</a></span><span class="t">    <span class="nam">installation_method</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">CableInstallationMethod</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs591" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t592" href="#t592">592</a></span><span class="t">        <span class="key">None</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Cable installation method"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t593" href="#t593">593</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t594" href="#t594">594</a></span><span class="t">    <span class="nam">ambient_temperature_c</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">float</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs594" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t595" href="#t595">595</a></span><span class="t">        <span class="key">None</span><span class="op">,</span> <span class="nam">ge</span><span class="op">=</span><span class="op">-</span><span class="num">50</span><span class="op">,</span> <span class="nam">le</span><span class="op">=</span><span class="num">80</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Ambient temperature in Celsius"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t596" href="#t596">596</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t597" href="#t597">597</a></span><span class="t">    <span class="nam">ground_temperature_c</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">float</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs597" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t598" href="#t598">598</a></span><span class="t">        <span class="key">None</span><span class="op">,</span> <span class="nam">ge</span><span class="op">=</span><span class="op">-</span><span class="num">50</span><span class="op">,</span> <span class="nam">le</span><span class="op">=</span><span class="num">80</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Ground temperature in Celsius"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t599" href="#t599">599</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t600" href="#t600">600</a></span><span class="t">    <span class="nam">burial_depth_m</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">float</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs600" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t601" href="#t601">601</a></span><span class="t">        <span class="key">None</span><span class="op">,</span> <span class="nam">ge</span><span class="op">=</span><span class="num">0</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Burial depth in meters"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t602" href="#t602">602</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t603" href="#t603">603</a></span><span class="t">    <span class="nam">conductor_size_mm2</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">float</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs603" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t604" href="#t604">604</a></span><span class="t">        <span class="key">None</span><span class="op">,</span> <span class="nam">gt</span><span class="op">=</span><span class="num">0</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Conductor size in mm&#178;"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t605" href="#t605">605</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t606" href="#t606">606</a></span><span class="t">    <span class="nam">insulation_type</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">str</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs606" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t607" href="#t607">607</a></span><span class="t">        <span class="key">None</span><span class="op">,</span> <span class="nam">max_length</span><span class="op">=</span><span class="num">50</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Insulation type"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t608" href="#t608">608</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t609" href="#t609">609</a></span><span class="t">    <span class="nam">sheath_type</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">str</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="key">None</span><span class="op">,</span> <span class="nam">max_length</span><span class="op">=</span><span class="num">50</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Sheath type"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs609" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t610" href="#t610">610</a></span><span class="t">    <span class="nam">armour_type</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">str</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="key">None</span><span class="op">,</span> <span class="nam">max_length</span><span class="op">=</span><span class="num">50</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Armour type"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs610" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t611" href="#t611">611</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t612" href="#t612">612</a></span><span class="t">    <span class="com"># Apply the same validators as create schema</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t613" href="#t613">613</a></span><span class="t">    <span class="nam">_validate_name</span> <span class="op">=</span> <span class="nam">field_validator</span><span class="op">(</span><span class="str">"name"</span><span class="op">)</span><span class="op">(</span><span class="nam">CableSegmentBaseSchema</span><span class="op">.</span><span class="nam">validate_name</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs613" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t614" href="#t614">614</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t615" href="#t615">615</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t616" href="#t616">616</a></span><span class="t"><span class="key">class</span> <span class="nam">CableSegmentReadSchema</span><span class="op">(</span><span class="nam">CableSegmentBaseSchema</span><span class="op">,</span> <span class="nam">BaseSoftDeleteSchema</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"><label for="ctxs616" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t617" href="#t617">617</a></span><span class="t">    <span class="str">"""Schema for reading/displaying cable segment data."""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t618" href="#t618">618</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t619" href="#t619">619</a></span><span class="t">    <span class="nam">project_id</span><span class="op">:</span> <span class="nam">int</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="op">...</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Project ID"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs619" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t620" href="#t620">620</a></span><span class="t">    <span class="nam">cable_route_id</span><span class="op">:</span> <span class="nam">int</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="op">...</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Cable route ID"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs620" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t621" href="#t621">621</a></span><span class="t">    <span class="nam">cable_component_id</span><span class="op">:</span> <span class="nam">int</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="op">...</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Cable component ID"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs621" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t622" href="#t622">622</a></span><span class="t">    <span class="nam">calculated_resistance_ohm_per_m</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">float</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs622" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t623" href="#t623">623</a></span><span class="t">        <span class="key">None</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Calculated resistance in ohm/m"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t624" href="#t624">624</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t625" href="#t625">625</a></span><span class="t">    <span class="nam">calculated_reactance_ohm_per_m</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">float</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs625" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t626" href="#t626">626</a></span><span class="t">        <span class="key">None</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Calculated reactance in ohm/m"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t627" href="#t627">627</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t628" href="#t628">628</a></span><span class="t">    <span class="nam">calculated_current_capacity_a</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">float</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs628" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t629" href="#t629">629</a></span><span class="t">        <span class="key">None</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Calculated current capacity in amperes"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t630" href="#t630">630</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t631" href="#t631">631</a></span><span class="t">    <span class="nam">calculated_voltage_drop_v</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">float</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs631" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t632" href="#t632">632</a></span><span class="t">        <span class="key">None</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Calculated voltage drop in volts"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t633" href="#t633">633</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t634" href="#t634">634</a></span><span class="t">    <span class="nam">calculated_power_loss_w</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">float</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs634" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t635" href="#t635">635</a></span><span class="t">        <span class="key">None</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Calculated power loss in watts"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t636" href="#t636">636</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t637" href="#t637">637</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t638" href="#t638">638</a></span><span class="t">    <span class="nam">model_config</span> <span class="op">=</span> <span class="nam">ConfigDict</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs638" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t639" href="#t639">639</a></span><span class="t">        <span class="nam">from_attributes</span><span class="op">=</span><span class="key">True</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t640" href="#t640">640</a></span><span class="t">        <span class="nam">json_schema_extra</span><span class="op">=</span><span class="op">{</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t641" href="#t641">641</a></span><span class="t">            <span class="str">"example"</span><span class="op">:</span> <span class="op">{</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t642" href="#t642">642</a></span><span class="t">                <span class="str">"id"</span><span class="op">:</span> <span class="num">1</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t643" href="#t643">643</a></span><span class="t">                <span class="str">"name"</span><span class="op">:</span> <span class="str">"Segment 1 - Underground"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t644" href="#t644">644</a></span><span class="t">                <span class="str">"project_id"</span><span class="op">:</span> <span class="num">1</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t645" href="#t645">645</a></span><span class="t">                <span class="str">"cable_route_id"</span><span class="op">:</span> <span class="num">1</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t646" href="#t646">646</a></span><span class="t">                <span class="str">"cable_component_id"</span><span class="op">:</span> <span class="num">25</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t647" href="#t647">647</a></span><span class="t">                <span class="str">"segment_order"</span><span class="op">:</span> <span class="num">1</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t648" href="#t648">648</a></span><span class="t">                <span class="str">"length_m"</span><span class="op">:</span> <span class="num">50.0</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t649" href="#t649">649</a></span><span class="t">                <span class="str">"installation_method"</span><span class="op">:</span> <span class="str">"DIRECT_BURIED"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t650" href="#t650">650</a></span><span class="t">                <span class="str">"calculated_resistance_ohm_per_m"</span><span class="op">:</span> <span class="num">0.0184</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t651" href="#t651">651</a></span><span class="t">                <span class="str">"calculated_current_capacity_a"</span><span class="op">:</span> <span class="num">32.0</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t652" href="#t652">652</a></span><span class="t">                <span class="str">"calculated_voltage_drop_v"</span><span class="op">:</span> <span class="num">4.2</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t653" href="#t653">653</a></span><span class="t">                <span class="str">"created_at"</span><span class="op">:</span> <span class="str">"2024-01-15T10:30:00Z"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t654" href="#t654">654</a></span><span class="t">                <span class="str">"updated_at"</span><span class="op">:</span> <span class="str">"2024-01-15T10:30:00Z"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t655" href="#t655">655</a></span><span class="t">                <span class="str">"is_deleted"</span><span class="op">:</span> <span class="key">False</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t656" href="#t656">656</a></span><span class="t">                <span class="str">"deleted_at"</span><span class="op">:</span> <span class="key">None</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t657" href="#t657">657</a></span><span class="t">                <span class="str">"deleted_by_user_id"</span><span class="op">:</span> <span class="key">None</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t658" href="#t658">658</a></span><span class="t">            <span class="op">}</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t659" href="#t659">659</a></span><span class="t">        <span class="op">}</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t660" href="#t660">660</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t661" href="#t661">661</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t662" href="#t662">662</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t663" href="#t663">663</a></span><span class="t"><span class="key">class</span> <span class="nam">CableSegmentSummarySchema</span><span class="op">(</span><span class="nam">BaseModel</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"><label for="ctxs663" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t664" href="#t664">664</a></span><span class="t">    <span class="str">"""Lightweight schema for cable segment listings."""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t665" href="#t665">665</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t666" href="#t666">666</a></span><span class="t">    <span class="nam">id</span><span class="op">:</span> <span class="nam">int</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="op">...</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Cable segment ID"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs666" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t667" href="#t667">667</a></span><span class="t">    <span class="nam">name</span><span class="op">:</span> <span class="nam">str</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="op">...</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Cable segment name"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs667" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t668" href="#t668">668</a></span><span class="t">    <span class="nam">cable_route_id</span><span class="op">:</span> <span class="nam">int</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="op">...</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Cable route ID"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs668" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t669" href="#t669">669</a></span><span class="t">    <span class="nam">segment_order</span><span class="op">:</span> <span class="nam">int</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="op">...</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Segment order in route"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs669" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t670" href="#t670">670</a></span><span class="t">    <span class="nam">length_m</span><span class="op">:</span> <span class="nam">float</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="op">...</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Segment length in meters"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs670" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t671" href="#t671">671</a></span><span class="t">    <span class="nam">installation_method</span><span class="op">:</span> <span class="nam">CableInstallationMethod</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs671" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t672" href="#t672">672</a></span><span class="t">        <span class="op">...</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Cable installation method"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t673" href="#t673">673</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t674" href="#t674">674</a></span><span class="t">    <span class="nam">calculated_voltage_drop_v</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">float</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs674" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t675" href="#t675">675</a></span><span class="t">        <span class="key">None</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Calculated voltage drop in volts"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t676" href="#t676">676</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t677" href="#t677">677</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t678" href="#t678">678</a></span><span class="t">    <span class="nam">model_config</span> <span class="op">=</span> <span class="nam">ConfigDict</span><span class="op">(</span><span class="nam">from_attributes</span><span class="op">=</span><span class="key">True</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs678" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t679" href="#t679">679</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t680" href="#t680">680</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t681" href="#t681">681</a></span><span class="t"><span class="com"># ============================================================================</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t682" href="#t682">682</a></span><span class="t"><span class="com"># LOAD CALCULATION SCHEMAS</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t683" href="#t683">683</a></span><span class="t"><span class="com"># ============================================================================</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t684" href="#t684">684</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t685" href="#t685">685</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t686" href="#t686">686</a></span><span class="t"><span class="key">class</span> <span class="nam">LoadCalculationBaseSchema</span><span class="op">(</span><span class="nam">BaseModel</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"><label for="ctxs686" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t687" href="#t687">687</a></span><span class="t">    <span class="str">"""Base schema for LoadCalculation with common fields."""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t688" href="#t688">688</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t689" href="#t689">689</a></span><span class="t">    <span class="nam">name</span><span class="op">:</span> <span class="nam">str</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs689" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t690" href="#t690">690</a></span><span class="t">        <span class="op">...</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t691" href="#t691">691</a></span><span class="t">        <span class="nam">min_length</span><span class="op">=</span><span class="num">3</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t692" href="#t692">692</a></span><span class="t">        <span class="nam">max_length</span><span class="op">=</span><span class="num">100</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t693" href="#t693">693</a></span><span class="t">        <span class="nam">description</span><span class="op">=</span><span class="str">"Load calculation name/identifier"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t694" href="#t694">694</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t695" href="#t695">695</a></span><span class="t">    <span class="nam">load_type</span><span class="op">:</span> <span class="nam">str</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs695" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t696" href="#t696">696</a></span><span class="t">        <span class="op">...</span><span class="op">,</span> <span class="nam">min_length</span><span class="op">=</span><span class="num">1</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Load type (e.g., heat_tracing, motor, lighting)"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t697" href="#t697">697</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t698" href="#t698">698</a></span><span class="t">    <span class="nam">load_description</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">str</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs698" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t699" href="#t699">699</a></span><span class="t">        <span class="key">None</span><span class="op">,</span> <span class="nam">max_length</span><span class="op">=</span><span class="num">200</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Load description"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t700" href="#t700">700</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t701" href="#t701">701</a></span><span class="t">    <span class="nam">rated_power_kw</span><span class="op">:</span> <span class="nam">float</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="op">...</span><span class="op">,</span> <span class="nam">gt</span><span class="op">=</span><span class="num">0</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Rated power in kW"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs701" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t702" href="#t702">702</a></span><span class="t">    <span class="nam">rated_voltage_v</span><span class="op">:</span> <span class="nam">float</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="op">...</span><span class="op">,</span> <span class="nam">gt</span><span class="op">=</span><span class="num">0</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Rated voltage in volts"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs702" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t703" href="#t703">703</a></span><span class="t">    <span class="nam">rated_current_a</span><span class="op">:</span> <span class="nam">float</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="op">...</span><span class="op">,</span> <span class="nam">gt</span><span class="op">=</span><span class="num">0</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Rated current in amperes"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs703" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t704" href="#t704">704</a></span><span class="t">    <span class="nam">power_factor</span><span class="op">:</span> <span class="nam">float</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="num">1.0</span><span class="op">,</span> <span class="nam">gt</span><span class="op">=</span><span class="num">0</span><span class="op">,</span> <span class="nam">le</span><span class="op">=</span><span class="num">1</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Power factor"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs704" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t705" href="#t705">705</a></span><span class="t">    <span class="nam">efficiency_percent</span><span class="op">:</span> <span class="nam">float</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs705" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t706" href="#t706">706</a></span><span class="t">        <span class="num">100.0</span><span class="op">,</span> <span class="nam">gt</span><span class="op">=</span><span class="num">0</span><span class="op">,</span> <span class="nam">le</span><span class="op">=</span><span class="num">100</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Efficiency percentage"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t707" href="#t707">707</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t708" href="#t708">708</a></span><span class="t">    <span class="nam">operating_hours_per_day</span><span class="op">:</span> <span class="nam">float</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs708" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t709" href="#t709">709</a></span><span class="t">        <span class="num">24.0</span><span class="op">,</span> <span class="nam">ge</span><span class="op">=</span><span class="num">0</span><span class="op">,</span> <span class="nam">le</span><span class="op">=</span><span class="num">24</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Operating hours per day"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t710" href="#t710">710</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t711" href="#t711">711</a></span><span class="t">    <span class="nam">load_factor_percent</span><span class="op">:</span> <span class="nam">float</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs711" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t712" href="#t712">712</a></span><span class="t">        <span class="num">100.0</span><span class="op">,</span> <span class="nam">gt</span><span class="op">=</span><span class="num">0</span><span class="op">,</span> <span class="nam">le</span><span class="op">=</span><span class="num">100</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Load factor percentage"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t713" href="#t713">713</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t714" href="#t714">714</a></span><span class="t">    <span class="nam">diversity_factor</span><span class="op">:</span> <span class="nam">float</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="num">1.0</span><span class="op">,</span> <span class="nam">gt</span><span class="op">=</span><span class="num">0</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Diversity factor"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs714" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t715" href="#t715">715</a></span><span class="t">    <span class="nam">safety_factor</span><span class="op">:</span> <span class="nam">float</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="num">1.2</span><span class="op">,</span> <span class="nam">ge</span><span class="op">=</span><span class="num">1</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Safety factor"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs715" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t716" href="#t716">716</a></span><span class="t">    <span class="nam">design_margin_percent</span><span class="op">:</span> <span class="nam">float</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs716" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t717" href="#t717">717</a></span><span class="t">        <span class="num">20.0</span><span class="op">,</span> <span class="nam">ge</span><span class="op">=</span><span class="num">0</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Design margin percentage"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t718" href="#t718">718</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t719" href="#t719">719</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t720" href="#t720">720</a></span><span class="t">    <span class="op">@</span><span class="nam">field_validator</span><span class="op">(</span><span class="str">"name"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs720" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t721" href="#t721">721</a></span><span class="t">    <span class="op">@</span><span class="nam">classmethod</span>&nbsp;</span><span class="r"><label for="ctxs721" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t722" href="#t722">722</a></span><span class="t">    <span class="key">def</span> <span class="nam">validate_name</span><span class="op">(</span><span class="nam">cls</span><span class="op">,</span> <span class="nam">v</span><span class="op">:</span> <span class="nam">str</span><span class="op">)</span> <span class="op">-></span> <span class="nam">str</span><span class="op">:</span>&nbsp;</span><span class="r"><label for="ctxs722" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t723" href="#t723">723</a></span><span class="t">        <span class="str">"""Validate load calculation name."""</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t724" href="#t724">724</a></span><span class="t">        <span class="key">if</span> <span class="key">not</span> <span class="nam">v</span> <span class="key">or</span> <span class="key">not</span> <span class="nam">v</span><span class="op">.</span><span class="nam">strip</span><span class="op">(</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t725" href="#t725">725</a></span><span class="t">            <span class="key">raise</span> <span class="nam">ValueError</span><span class="op">(</span><span class="str">"Load calculation name cannot be empty"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t726" href="#t726">726</a></span><span class="t">        <span class="key">return</span> <span class="nam">v</span><span class="op">.</span><span class="nam">strip</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t727" href="#t727">727</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t728" href="#t728">728</a></span><span class="t">    <span class="op">@</span><span class="nam">model_validator</span><span class="op">(</span><span class="nam">mode</span><span class="op">=</span><span class="str">"after"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs728" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t729" href="#t729">729</a></span><span class="t">    <span class="key">def</span> <span class="nam">validate_electrical_parameters</span><span class="op">(</span><span class="nam">self</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"><label for="ctxs729" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t730" href="#t730">730</a></span><span class="t">        <span class="str">"""Validate electrical parameters consistency."""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t731" href="#t731">731</a></span><span class="t">        <span class="com"># Check if power, voltage, and current are consistent (P = V * I * PF)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t732" href="#t732">732</a></span><span class="t">        <span class="nam">expected_power</span> <span class="op">=</span> <span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t733" href="#t733">733</a></span><span class="t">            <span class="nam">self</span><span class="op">.</span><span class="nam">rated_voltage_v</span> <span class="op">*</span> <span class="nam">self</span><span class="op">.</span><span class="nam">rated_current_a</span> <span class="op">*</span> <span class="nam">self</span><span class="op">.</span><span class="nam">power_factor</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t734" href="#t734">734</a></span><span class="t">        <span class="op">)</span> <span class="op">/</span> <span class="num">1000</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t735" href="#t735">735</a></span><span class="t">        <span class="nam">power_tolerance</span> <span class="op">=</span> <span class="num">0.1</span>  <span class="com"># 10% tolerance</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t736" href="#t736">736</a></span><span class="t">        <span class="key">if</span> <span class="nam">abs</span><span class="op">(</span><span class="nam">self</span><span class="op">.</span><span class="nam">rated_power_kw</span> <span class="op">-</span> <span class="nam">expected_power</span><span class="op">)</span> <span class="op">></span> <span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t737" href="#t737">737</a></span><span class="t">            <span class="nam">expected_power</span> <span class="op">*</span> <span class="nam">power_tolerance</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t738" href="#t738">738</a></span><span class="t">        <span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t739" href="#t739">739</a></span><span class="t">            <span class="key">raise</span> <span class="nam">ValueError</span><span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t740" href="#t740">740</a></span><span class="t">                <span class="fst">f"</span><span class="fst">Power, voltage, and current are not consistent. </span><span class="fst">"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t741" href="#t741">741</a></span><span class="t">                <span class="fst">f"</span><span class="fst">Expected power: </span><span class="op">{</span><span class="nam">expected_power</span><span class="op">:</span><span class="fst">.2f</span><span class="op">}</span><span class="fst">kW, provided: </span><span class="op">{</span><span class="nam">self</span><span class="op">.</span><span class="nam">rated_power_kw</span><span class="op">}</span><span class="fst">kW</span><span class="fst">"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t742" href="#t742">742</a></span><span class="t">            <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t743" href="#t743">743</a></span><span class="t">        <span class="key">return</span> <span class="nam">self</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t744" href="#t744">744</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t745" href="#t745">745</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t746" href="#t746">746</a></span><span class="t"><span class="key">class</span> <span class="nam">LoadCalculationCreateSchema</span><span class="op">(</span><span class="nam">LoadCalculationBaseSchema</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"><label for="ctxs746" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t747" href="#t747">747</a></span><span class="t">    <span class="str">"""Schema for creating a new load calculation."""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t748" href="#t748">748</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t749" href="#t749">749</a></span><span class="t">    <span class="nam">project_id</span><span class="op">:</span> <span class="nam">int</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="op">...</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Project ID"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs749" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t750" href="#t750">750</a></span><span class="t">    <span class="nam">electrical_node_id</span><span class="op">:</span> <span class="nam">int</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="op">...</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Electrical node ID"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs750" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t751" href="#t751">751</a></span><span class="t">    <span class="nam">related_pipe_id</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">int</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="key">None</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Related pipe ID"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs751" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t752" href="#t752">752</a></span><span class="t">    <span class="nam">related_vessel_id</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">int</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="key">None</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Related vessel ID"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs752" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t753" href="#t753">753</a></span><span class="t">    <span class="nam">related_htcircuit_id</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">int</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs753" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t754" href="#t754">754</a></span><span class="t">        <span class="key">None</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Related HT circuit ID"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t755" href="#t755">755</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t756" href="#t756">756</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t757" href="#t757">757</a></span><span class="t">    <span class="nam">model_config</span> <span class="op">=</span> <span class="nam">ConfigDict</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs757" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t758" href="#t758">758</a></span><span class="t">        <span class="nam">json_schema_extra</span><span class="op">=</span><span class="op">{</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t759" href="#t759">759</a></span><span class="t">            <span class="str">"example"</span><span class="op">:</span> <span class="op">{</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t760" href="#t760">760</a></span><span class="t">                <span class="str">"name"</span><span class="op">:</span> <span class="str">"Heat Tracing Load HTC-001"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t761" href="#t761">761</a></span><span class="t">                <span class="str">"project_id"</span><span class="op">:</span> <span class="num">1</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t762" href="#t762">762</a></span><span class="t">                <span class="str">"electrical_node_id"</span><span class="op">:</span> <span class="num">2</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t763" href="#t763">763</a></span><span class="t">                <span class="str">"load_type"</span><span class="op">:</span> <span class="str">"heat_tracing"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t764" href="#t764">764</a></span><span class="t">                <span class="str">"load_description"</span><span class="op">:</span> <span class="str">"Heat tracing for main process line"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t765" href="#t765">765</a></span><span class="t">                <span class="str">"rated_power_kw"</span><span class="op">:</span> <span class="num">2.5</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t766" href="#t766">766</a></span><span class="t">                <span class="str">"rated_voltage_v"</span><span class="op">:</span> <span class="num">240.0</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t767" href="#t767">767</a></span><span class="t">                <span class="str">"rated_current_a"</span><span class="op">:</span> <span class="num">10.4</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t768" href="#t768">768</a></span><span class="t">                <span class="str">"power_factor"</span><span class="op">:</span> <span class="num">1.0</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t769" href="#t769">769</a></span><span class="t">                <span class="str">"efficiency_percent"</span><span class="op">:</span> <span class="num">95.0</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t770" href="#t770">770</a></span><span class="t">                <span class="str">"operating_hours_per_day"</span><span class="op">:</span> <span class="num">24.0</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t771" href="#t771">771</a></span><span class="t">                <span class="str">"load_factor_percent"</span><span class="op">:</span> <span class="num">80.0</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t772" href="#t772">772</a></span><span class="t">                <span class="str">"diversity_factor"</span><span class="op">:</span> <span class="num">0.9</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t773" href="#t773">773</a></span><span class="t">                <span class="str">"safety_factor"</span><span class="op">:</span> <span class="num">1.2</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t774" href="#t774">774</a></span><span class="t">                <span class="str">"design_margin_percent"</span><span class="op">:</span> <span class="num">20.0</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t775" href="#t775">775</a></span><span class="t">                <span class="str">"related_pipe_id"</span><span class="op">:</span> <span class="num">1</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t776" href="#t776">776</a></span><span class="t">            <span class="op">}</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t777" href="#t777">777</a></span><span class="t">        <span class="op">}</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t778" href="#t778">778</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t779" href="#t779">779</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t780" href="#t780">780</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t781" href="#t781">781</a></span><span class="t"><span class="key">class</span> <span class="nam">LoadCalculationUpdateSchema</span><span class="op">(</span><span class="nam">BaseModel</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"><label for="ctxs781" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t782" href="#t782">782</a></span><span class="t">    <span class="str">"""Schema for updating an existing load calculation."""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t783" href="#t783">783</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t784" href="#t784">784</a></span><span class="t">    <span class="nam">name</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">str</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs784" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t785" href="#t785">785</a></span><span class="t">        <span class="key">None</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t786" href="#t786">786</a></span><span class="t">        <span class="nam">min_length</span><span class="op">=</span><span class="num">3</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t787" href="#t787">787</a></span><span class="t">        <span class="nam">max_length</span><span class="op">=</span><span class="num">100</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t788" href="#t788">788</a></span><span class="t">        <span class="nam">description</span><span class="op">=</span><span class="str">"Load calculation name/identifier"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t789" href="#t789">789</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t790" href="#t790">790</a></span><span class="t">    <span class="nam">electrical_node_id</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">int</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="key">None</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Electrical node ID"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs790" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t791" href="#t791">791</a></span><span class="t">    <span class="nam">load_type</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">str</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="key">None</span><span class="op">,</span> <span class="nam">min_length</span><span class="op">=</span><span class="num">1</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Load type"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs791" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t792" href="#t792">792</a></span><span class="t">    <span class="nam">load_description</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">str</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs792" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t793" href="#t793">793</a></span><span class="t">        <span class="key">None</span><span class="op">,</span> <span class="nam">max_length</span><span class="op">=</span><span class="num">200</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Load description"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t794" href="#t794">794</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t795" href="#t795">795</a></span><span class="t">    <span class="nam">rated_power_kw</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">float</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="key">None</span><span class="op">,</span> <span class="nam">gt</span><span class="op">=</span><span class="num">0</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Rated power in kW"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs795" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t796" href="#t796">796</a></span><span class="t">    <span class="nam">rated_voltage_v</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">float</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs796" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t797" href="#t797">797</a></span><span class="t">        <span class="key">None</span><span class="op">,</span> <span class="nam">gt</span><span class="op">=</span><span class="num">0</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Rated voltage in volts"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t798" href="#t798">798</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t799" href="#t799">799</a></span><span class="t">    <span class="nam">rated_current_a</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">float</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs799" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t800" href="#t800">800</a></span><span class="t">        <span class="key">None</span><span class="op">,</span> <span class="nam">gt</span><span class="op">=</span><span class="num">0</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Rated current in amperes"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t801" href="#t801">801</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t802" href="#t802">802</a></span><span class="t">    <span class="nam">power_factor</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">float</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="key">None</span><span class="op">,</span> <span class="nam">gt</span><span class="op">=</span><span class="num">0</span><span class="op">,</span> <span class="nam">le</span><span class="op">=</span><span class="num">1</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Power factor"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs802" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t803" href="#t803">803</a></span><span class="t">    <span class="nam">efficiency_percent</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">float</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs803" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t804" href="#t804">804</a></span><span class="t">        <span class="key">None</span><span class="op">,</span> <span class="nam">gt</span><span class="op">=</span><span class="num">0</span><span class="op">,</span> <span class="nam">le</span><span class="op">=</span><span class="num">100</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Efficiency percentage"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t805" href="#t805">805</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t806" href="#t806">806</a></span><span class="t">    <span class="nam">operating_hours_per_day</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">float</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs806" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t807" href="#t807">807</a></span><span class="t">        <span class="key">None</span><span class="op">,</span> <span class="nam">ge</span><span class="op">=</span><span class="num">0</span><span class="op">,</span> <span class="nam">le</span><span class="op">=</span><span class="num">24</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Operating hours per day"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t808" href="#t808">808</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t809" href="#t809">809</a></span><span class="t">    <span class="nam">load_factor_percent</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">float</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs809" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t810" href="#t810">810</a></span><span class="t">        <span class="key">None</span><span class="op">,</span> <span class="nam">gt</span><span class="op">=</span><span class="num">0</span><span class="op">,</span> <span class="nam">le</span><span class="op">=</span><span class="num">100</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Load factor percentage"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t811" href="#t811">811</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t812" href="#t812">812</a></span><span class="t">    <span class="nam">diversity_factor</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">float</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs812" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t813" href="#t813">813</a></span><span class="t">        <span class="key">None</span><span class="op">,</span> <span class="nam">gt</span><span class="op">=</span><span class="num">0</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Diversity factor"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t814" href="#t814">814</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t815" href="#t815">815</a></span><span class="t">    <span class="nam">safety_factor</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">float</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="key">None</span><span class="op">,</span> <span class="nam">ge</span><span class="op">=</span><span class="num">1</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Safety factor"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs815" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t816" href="#t816">816</a></span><span class="t">    <span class="nam">design_margin_percent</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">float</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs816" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t817" href="#t817">817</a></span><span class="t">        <span class="key">None</span><span class="op">,</span> <span class="nam">ge</span><span class="op">=</span><span class="num">0</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Design margin percentage"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t818" href="#t818">818</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t819" href="#t819">819</a></span><span class="t">    <span class="nam">related_pipe_id</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">int</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="key">None</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Related pipe ID"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs819" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t820" href="#t820">820</a></span><span class="t">    <span class="nam">related_vessel_id</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">int</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="key">None</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Related vessel ID"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs820" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t821" href="#t821">821</a></span><span class="t">    <span class="nam">related_htcircuit_id</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">int</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs821" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t822" href="#t822">822</a></span><span class="t">        <span class="key">None</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Related HT circuit ID"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t823" href="#t823">823</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t824" href="#t824">824</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t825" href="#t825">825</a></span><span class="t">    <span class="com"># Apply the same validators as create schema</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t826" href="#t826">826</a></span><span class="t">    <span class="nam">_validate_name</span> <span class="op">=</span> <span class="nam">field_validator</span><span class="op">(</span><span class="str">"name"</span><span class="op">)</span><span class="op">(</span><span class="nam">LoadCalculationBaseSchema</span><span class="op">.</span><span class="nam">validate_name</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs826" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t827" href="#t827">827</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t828" href="#t828">828</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t829" href="#t829">829</a></span><span class="t"><span class="key">class</span> <span class="nam">LoadCalculationReadSchema</span><span class="op">(</span><span class="nam">LoadCalculationBaseSchema</span><span class="op">,</span> <span class="nam">BaseSoftDeleteSchema</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"><label for="ctxs829" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t830" href="#t830">830</a></span><span class="t">    <span class="str">"""Schema for reading/displaying load calculation data."""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t831" href="#t831">831</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t832" href="#t832">832</a></span><span class="t">    <span class="nam">project_id</span><span class="op">:</span> <span class="nam">int</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="op">...</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Project ID"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs832" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t833" href="#t833">833</a></span><span class="t">    <span class="nam">electrical_node_id</span><span class="op">:</span> <span class="nam">int</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="op">...</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Electrical node ID"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs833" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t834" href="#t834">834</a></span><span class="t">    <span class="nam">related_pipe_id</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">int</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="key">None</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Related pipe ID"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs834" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t835" href="#t835">835</a></span><span class="t">    <span class="nam">related_vessel_id</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">int</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="key">None</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Related vessel ID"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs835" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t836" href="#t836">836</a></span><span class="t">    <span class="nam">related_htcircuit_id</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">int</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs836" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t837" href="#t837">837</a></span><span class="t">        <span class="key">None</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Related HT circuit ID"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t838" href="#t838">838</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t839" href="#t839">839</a></span><span class="t">    <span class="nam">calculated_operating_power_kw</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">float</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs839" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t840" href="#t840">840</a></span><span class="t">        <span class="key">None</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Calculated operating power in kW"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t841" href="#t841">841</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t842" href="#t842">842</a></span><span class="t">    <span class="nam">calculated_operating_current_a</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">float</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs842" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t843" href="#t843">843</a></span><span class="t">        <span class="key">None</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Calculated operating current in amperes"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t844" href="#t844">844</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t845" href="#t845">845</a></span><span class="t">    <span class="nam">calculated_daily_energy_kwh</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">float</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs845" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t846" href="#t846">846</a></span><span class="t">        <span class="key">None</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Calculated daily energy in kWh"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t847" href="#t847">847</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t848" href="#t848">848</a></span><span class="t">    <span class="nam">calculated_annual_energy_mwh</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">float</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs848" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t849" href="#t849">849</a></span><span class="t">        <span class="key">None</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Calculated annual energy in MWh"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t850" href="#t850">850</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t851" href="#t851">851</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t852" href="#t852">852</a></span><span class="t">    <span class="nam">model_config</span> <span class="op">=</span> <span class="nam">ConfigDict</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs852" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t853" href="#t853">853</a></span><span class="t">        <span class="nam">from_attributes</span><span class="op">=</span><span class="key">True</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t854" href="#t854">854</a></span><span class="t">        <span class="nam">json_schema_extra</span><span class="op">=</span><span class="op">{</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t855" href="#t855">855</a></span><span class="t">            <span class="str">"example"</span><span class="op">:</span> <span class="op">{</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t856" href="#t856">856</a></span><span class="t">                <span class="str">"id"</span><span class="op">:</span> <span class="num">1</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t857" href="#t857">857</a></span><span class="t">                <span class="str">"name"</span><span class="op">:</span> <span class="str">"Heat Tracing Load HTC-001"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t858" href="#t858">858</a></span><span class="t">                <span class="str">"project_id"</span><span class="op">:</span> <span class="num">1</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t859" href="#t859">859</a></span><span class="t">                <span class="str">"electrical_node_id"</span><span class="op">:</span> <span class="num">2</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t860" href="#t860">860</a></span><span class="t">                <span class="str">"load_type"</span><span class="op">:</span> <span class="str">"heat_tracing"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t861" href="#t861">861</a></span><span class="t">                <span class="str">"rated_power_kw"</span><span class="op">:</span> <span class="num">2.5</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t862" href="#t862">862</a></span><span class="t">                <span class="str">"rated_voltage_v"</span><span class="op">:</span> <span class="num">240.0</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t863" href="#t863">863</a></span><span class="t">                <span class="str">"rated_current_a"</span><span class="op">:</span> <span class="num">10.4</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t864" href="#t864">864</a></span><span class="t">                <span class="str">"calculated_operating_power_kw"</span><span class="op">:</span> <span class="num">2.0</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t865" href="#t865">865</a></span><span class="t">                <span class="str">"calculated_operating_current_a"</span><span class="op">:</span> <span class="num">8.3</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t866" href="#t866">866</a></span><span class="t">                <span class="str">"calculated_daily_energy_kwh"</span><span class="op">:</span> <span class="num">48.0</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t867" href="#t867">867</a></span><span class="t">                <span class="str">"calculated_annual_energy_mwh"</span><span class="op">:</span> <span class="num">17.5</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t868" href="#t868">868</a></span><span class="t">                <span class="str">"created_at"</span><span class="op">:</span> <span class="str">"2024-01-15T10:30:00Z"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t869" href="#t869">869</a></span><span class="t">                <span class="str">"updated_at"</span><span class="op">:</span> <span class="str">"2024-01-15T10:30:00Z"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t870" href="#t870">870</a></span><span class="t">                <span class="str">"is_deleted"</span><span class="op">:</span> <span class="key">False</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t871" href="#t871">871</a></span><span class="t">                <span class="str">"deleted_at"</span><span class="op">:</span> <span class="key">None</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t872" href="#t872">872</a></span><span class="t">                <span class="str">"deleted_by_user_id"</span><span class="op">:</span> <span class="key">None</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t873" href="#t873">873</a></span><span class="t">            <span class="op">}</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t874" href="#t874">874</a></span><span class="t">        <span class="op">}</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t875" href="#t875">875</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t876" href="#t876">876</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t877" href="#t877">877</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t878" href="#t878">878</a></span><span class="t"><span class="key">class</span> <span class="nam">LoadCalculationSummarySchema</span><span class="op">(</span><span class="nam">BaseModel</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"><label for="ctxs878" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t879" href="#t879">879</a></span><span class="t">    <span class="str">"""Lightweight schema for load calculation listings."""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t880" href="#t880">880</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t881" href="#t881">881</a></span><span class="t">    <span class="nam">id</span><span class="op">:</span> <span class="nam">int</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="op">...</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Load calculation ID"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs881" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t882" href="#t882">882</a></span><span class="t">    <span class="nam">name</span><span class="op">:</span> <span class="nam">str</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="op">...</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Load calculation name"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs882" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t883" href="#t883">883</a></span><span class="t">    <span class="nam">electrical_node_id</span><span class="op">:</span> <span class="nam">int</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="op">...</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Electrical node ID"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs883" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t884" href="#t884">884</a></span><span class="t">    <span class="nam">load_type</span><span class="op">:</span> <span class="nam">str</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="op">...</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Load type"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs884" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t885" href="#t885">885</a></span><span class="t">    <span class="nam">rated_power_kw</span><span class="op">:</span> <span class="nam">float</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="op">...</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Rated power in kW"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs885" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t886" href="#t886">886</a></span><span class="t">    <span class="nam">calculated_operating_power_kw</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">float</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs886" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t887" href="#t887">887</a></span><span class="t">        <span class="key">None</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Calculated operating power in kW"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t888" href="#t888">888</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t889" href="#t889">889</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t890" href="#t890">890</a></span><span class="t">    <span class="nam">model_config</span> <span class="op">=</span> <span class="nam">ConfigDict</span><span class="op">(</span><span class="nam">from_attributes</span><span class="op">=</span><span class="key">True</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs890" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t891" href="#t891">891</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t892" href="#t892">892</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t893" href="#t893">893</a></span><span class="t"><span class="com"># ============================================================================</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t894" href="#t894">894</a></span><span class="t"><span class="com"># VOLTAGE DROP CALCULATION SCHEMAS</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t895" href="#t895">895</a></span><span class="t"><span class="com"># ============================================================================</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t896" href="#t896">896</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t897" href="#t897">897</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t898" href="#t898">898</a></span><span class="t"><span class="key">class</span> <span class="nam">VoltageDropCalculationBaseSchema</span><span class="op">(</span><span class="nam">BaseModel</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"><label for="ctxs898" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t899" href="#t899">899</a></span><span class="t">    <span class="str">"""Base schema for VoltageDropCalculation with common fields."""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t900" href="#t900">900</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t901" href="#t901">901</a></span><span class="t">    <span class="nam">name</span><span class="op">:</span> <span class="nam">str</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs901" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t902" href="#t902">902</a></span><span class="t">        <span class="op">...</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t903" href="#t903">903</a></span><span class="t">        <span class="nam">min_length</span><span class="op">=</span><span class="num">3</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t904" href="#t904">904</a></span><span class="t">        <span class="nam">max_length</span><span class="op">=</span><span class="num">100</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t905" href="#t905">905</a></span><span class="t">        <span class="nam">description</span><span class="op">=</span><span class="str">"Voltage drop calculation name/identifier"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t906" href="#t906">906</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t907" href="#t907">907</a></span><span class="t">    <span class="nam">supply_voltage_v</span><span class="op">:</span> <span class="nam">float</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="op">...</span><span class="op">,</span> <span class="nam">gt</span><span class="op">=</span><span class="num">0</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Supply voltage in volts"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs907" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t908" href="#t908">908</a></span><span class="t">    <span class="nam">load_current_a</span><span class="op">:</span> <span class="nam">float</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="op">...</span><span class="op">,</span> <span class="nam">gt</span><span class="op">=</span><span class="num">0</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Load current in amperes"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs908" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t909" href="#t909">909</a></span><span class="t">    <span class="nam">cable_length_m</span><span class="op">:</span> <span class="nam">float</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="op">...</span><span class="op">,</span> <span class="nam">gt</span><span class="op">=</span><span class="num">0</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Cable length in meters"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs909" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t910" href="#t910">910</a></span><span class="t">    <span class="nam">cable_resistance_ohm_per_m</span><span class="op">:</span> <span class="nam">float</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs910" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t911" href="#t911">911</a></span><span class="t">        <span class="op">...</span><span class="op">,</span> <span class="nam">ge</span><span class="op">=</span><span class="num">0</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Cable resistance per meter in ohm/m"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t912" href="#t912">912</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t913" href="#t913">913</a></span><span class="t">    <span class="nam">cable_reactance_ohm_per_m</span><span class="op">:</span> <span class="nam">float</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs913" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t914" href="#t914">914</a></span><span class="t">        <span class="num">0.0</span><span class="op">,</span> <span class="nam">ge</span><span class="op">=</span><span class="num">0</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Cable reactance per meter in ohm/m"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t915" href="#t915">915</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t916" href="#t916">916</a></span><span class="t">    <span class="nam">power_factor</span><span class="op">:</span> <span class="nam">float</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="num">1.0</span><span class="op">,</span> <span class="nam">gt</span><span class="op">=</span><span class="num">0</span><span class="op">,</span> <span class="nam">le</span><span class="op">=</span><span class="num">1</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Power factor"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs916" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t917" href="#t917">917</a></span><span class="t">    <span class="nam">ambient_temperature_c</span><span class="op">:</span> <span class="nam">float</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs917" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t918" href="#t918">918</a></span><span class="t">        <span class="num">25.0</span><span class="op">,</span> <span class="nam">ge</span><span class="op">=</span><span class="op">-</span><span class="num">50</span><span class="op">,</span> <span class="nam">le</span><span class="op">=</span><span class="num">80</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Ambient temperature in Celsius"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t919" href="#t919">919</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t920" href="#t920">920</a></span><span class="t">    <span class="nam">ground_temperature_c</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">float</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs920" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t921" href="#t921">921</a></span><span class="t">        <span class="key">None</span><span class="op">,</span> <span class="nam">ge</span><span class="op">=</span><span class="op">-</span><span class="num">50</span><span class="op">,</span> <span class="nam">le</span><span class="op">=</span><span class="num">80</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Ground temperature in Celsius"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t922" href="#t922">922</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t923" href="#t923">923</a></span><span class="t">    <span class="nam">derating_factor</span><span class="op">:</span> <span class="nam">float</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="num">1.0</span><span class="op">,</span> <span class="nam">gt</span><span class="op">=</span><span class="num">0</span><span class="op">,</span> <span class="nam">le</span><span class="op">=</span><span class="num">1</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Derating factor"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs923" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t924" href="#t924">924</a></span><span class="t">    <span class="nam">max_allowed_voltage_drop_percent</span><span class="op">:</span> <span class="nam">float</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs924" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t925" href="#t925">925</a></span><span class="t">        <span class="num">5.0</span><span class="op">,</span> <span class="nam">gt</span><span class="op">=</span><span class="num">0</span><span class="op">,</span> <span class="nam">le</span><span class="op">=</span><span class="num">50</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Maximum allowed voltage drop percentage"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t926" href="#t926">926</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t927" href="#t927">927</a></span><span class="t">    <span class="nam">calculation_method</span><span class="op">:</span> <span class="nam">str</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="str">"IEC"</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Calculation method"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs927" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t928" href="#t928">928</a></span><span class="t">    <span class="nam">calculation_standard</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">str</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs928" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t929" href="#t929">929</a></span><span class="t">        <span class="key">None</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Calculation standard"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t930" href="#t930">930</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t931" href="#t931">931</a></span><span class="t">    <span class="nam">calculation_notes</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">str</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="key">None</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Calculation notes"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs931" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t932" href="#t932">932</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t933" href="#t933">933</a></span><span class="t">    <span class="op">@</span><span class="nam">field_validator</span><span class="op">(</span><span class="str">"name"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs933" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t934" href="#t934">934</a></span><span class="t">    <span class="op">@</span><span class="nam">classmethod</span>&nbsp;</span><span class="r"><label for="ctxs934" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t935" href="#t935">935</a></span><span class="t">    <span class="key">def</span> <span class="nam">validate_name</span><span class="op">(</span><span class="nam">cls</span><span class="op">,</span> <span class="nam">v</span><span class="op">:</span> <span class="nam">str</span><span class="op">)</span> <span class="op">-></span> <span class="nam">str</span><span class="op">:</span>&nbsp;</span><span class="r"><label for="ctxs935" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t936" href="#t936">936</a></span><span class="t">        <span class="str">"""Validate voltage drop calculation name."""</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t937" href="#t937">937</a></span><span class="t">        <span class="key">if</span> <span class="key">not</span> <span class="nam">v</span> <span class="key">or</span> <span class="key">not</span> <span class="nam">v</span><span class="op">.</span><span class="nam">strip</span><span class="op">(</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t938" href="#t938">938</a></span><span class="t">            <span class="key">raise</span> <span class="nam">ValueError</span><span class="op">(</span><span class="str">"Voltage drop calculation name cannot be empty"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t939" href="#t939">939</a></span><span class="t">        <span class="key">return</span> <span class="nam">v</span><span class="op">.</span><span class="nam">strip</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t940" href="#t940">940</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t941" href="#t941">941</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t942" href="#t942">942</a></span><span class="t"><span class="key">class</span> <span class="nam">VoltageDropCalculationCreateSchema</span><span class="op">(</span><span class="nam">VoltageDropCalculationBaseSchema</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"><label for="ctxs942" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t943" href="#t943">943</a></span><span class="t">    <span class="str">"""Schema for creating a new voltage drop calculation."""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t944" href="#t944">944</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t945" href="#t945">945</a></span><span class="t">    <span class="nam">project_id</span><span class="op">:</span> <span class="nam">int</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="op">...</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Project ID"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs945" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t946" href="#t946">946</a></span><span class="t">    <span class="nam">cable_route_id</span><span class="op">:</span> <span class="nam">int</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="op">...</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Cable route ID"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs946" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t947" href="#t947">947</a></span><span class="t">    <span class="nam">load_calculation_id</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">int</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="key">None</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Load calculation ID"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs947" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t948" href="#t948">948</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t949" href="#t949">949</a></span><span class="t">    <span class="nam">model_config</span> <span class="op">=</span> <span class="nam">ConfigDict</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs949" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t950" href="#t950">950</a></span><span class="t">        <span class="nam">json_schema_extra</span><span class="op">=</span><span class="op">{</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t951" href="#t951">951</a></span><span class="t">            <span class="str">"example"</span><span class="op">:</span> <span class="op">{</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t952" href="#t952">952</a></span><span class="t">                <span class="str">"name"</span><span class="op">:</span> <span class="str">"Voltage Drop Calc - Main Feed"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t953" href="#t953">953</a></span><span class="t">                <span class="str">"project_id"</span><span class="op">:</span> <span class="num">1</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t954" href="#t954">954</a></span><span class="t">                <span class="str">"cable_route_id"</span><span class="op">:</span> <span class="num">1</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t955" href="#t955">955</a></span><span class="t">                <span class="str">"load_calculation_id"</span><span class="op">:</span> <span class="num">1</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t956" href="#t956">956</a></span><span class="t">                <span class="str">"supply_voltage_v"</span><span class="op">:</span> <span class="num">240.0</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t957" href="#t957">957</a></span><span class="t">                <span class="str">"load_current_a"</span><span class="op">:</span> <span class="num">10.4</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t958" href="#t958">958</a></span><span class="t">                <span class="str">"cable_length_m"</span><span class="op">:</span> <span class="num">150.0</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t959" href="#t959">959</a></span><span class="t">                <span class="str">"cable_resistance_ohm_per_m"</span><span class="op">:</span> <span class="num">0.0184</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t960" href="#t960">960</a></span><span class="t">                <span class="str">"cable_reactance_ohm_per_m"</span><span class="op">:</span> <span class="num">0.0</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t961" href="#t961">961</a></span><span class="t">                <span class="str">"power_factor"</span><span class="op">:</span> <span class="num">1.0</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t962" href="#t962">962</a></span><span class="t">                <span class="str">"ambient_temperature_c"</span><span class="op">:</span> <span class="num">25.0</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t963" href="#t963">963</a></span><span class="t">                <span class="str">"derating_factor"</span><span class="op">:</span> <span class="num">1.0</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t964" href="#t964">964</a></span><span class="t">                <span class="str">"max_allowed_voltage_drop_percent"</span><span class="op">:</span> <span class="num">5.0</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t965" href="#t965">965</a></span><span class="t">                <span class="str">"calculation_method"</span><span class="op">:</span> <span class="str">"IEC"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t966" href="#t966">966</a></span><span class="t">                <span class="str">"calculation_standard"</span><span class="op">:</span> <span class="str">"IEC 60364"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t967" href="#t967">967</a></span><span class="t">            <span class="op">}</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t968" href="#t968">968</a></span><span class="t">        <span class="op">}</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t969" href="#t969">969</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t970" href="#t970">970</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t971" href="#t971">971</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t972" href="#t972">972</a></span><span class="t"><span class="key">class</span> <span class="nam">VoltageDropCalculationUpdateSchema</span><span class="op">(</span><span class="nam">BaseModel</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"><label for="ctxs972" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t973" href="#t973">973</a></span><span class="t">    <span class="str">"""Schema for updating an existing voltage drop calculation."""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t974" href="#t974">974</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t975" href="#t975">975</a></span><span class="t">    <span class="nam">name</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">str</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs975" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t976" href="#t976">976</a></span><span class="t">        <span class="key">None</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t977" href="#t977">977</a></span><span class="t">        <span class="nam">min_length</span><span class="op">=</span><span class="num">3</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t978" href="#t978">978</a></span><span class="t">        <span class="nam">max_length</span><span class="op">=</span><span class="num">100</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t979" href="#t979">979</a></span><span class="t">        <span class="nam">description</span><span class="op">=</span><span class="str">"Voltage drop calculation name/identifier"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t980" href="#t980">980</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t981" href="#t981">981</a></span><span class="t">    <span class="nam">cable_route_id</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">int</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="key">None</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Cable route ID"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs981" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t982" href="#t982">982</a></span><span class="t">    <span class="nam">load_calculation_id</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">int</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="key">None</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Load calculation ID"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs982" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t983" href="#t983">983</a></span><span class="t">    <span class="nam">supply_voltage_v</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">float</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs983" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t984" href="#t984">984</a></span><span class="t">        <span class="key">None</span><span class="op">,</span> <span class="nam">gt</span><span class="op">=</span><span class="num">0</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Supply voltage in volts"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t985" href="#t985">985</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t986" href="#t986">986</a></span><span class="t">    <span class="nam">load_current_a</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">float</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs986" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t987" href="#t987">987</a></span><span class="t">        <span class="key">None</span><span class="op">,</span> <span class="nam">gt</span><span class="op">=</span><span class="num">0</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Load current in amperes"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t988" href="#t988">988</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t989" href="#t989">989</a></span><span class="t">    <span class="nam">cable_length_m</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">float</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs989" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t990" href="#t990">990</a></span><span class="t">        <span class="key">None</span><span class="op">,</span> <span class="nam">gt</span><span class="op">=</span><span class="num">0</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Cable length in meters"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t991" href="#t991">991</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t992" href="#t992">992</a></span><span class="t">    <span class="nam">cable_resistance_ohm_per_m</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">float</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs992" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t993" href="#t993">993</a></span><span class="t">        <span class="key">None</span><span class="op">,</span> <span class="nam">ge</span><span class="op">=</span><span class="num">0</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Cable resistance per meter in ohm/m"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t994" href="#t994">994</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t995" href="#t995">995</a></span><span class="t">    <span class="nam">cable_reactance_ohm_per_m</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">float</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs995" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t996" href="#t996">996</a></span><span class="t">        <span class="key">None</span><span class="op">,</span> <span class="nam">ge</span><span class="op">=</span><span class="num">0</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Cable reactance per meter in ohm/m"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t997" href="#t997">997</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t998" href="#t998">998</a></span><span class="t">    <span class="nam">power_factor</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">float</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="key">None</span><span class="op">,</span> <span class="nam">gt</span><span class="op">=</span><span class="num">0</span><span class="op">,</span> <span class="nam">le</span><span class="op">=</span><span class="num">1</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Power factor"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs998" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t999" href="#t999">999</a></span><span class="t">    <span class="nam">ambient_temperature_c</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">float</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs999" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t1000" href="#t1000">1000</a></span><span class="t">        <span class="key">None</span><span class="op">,</span> <span class="nam">ge</span><span class="op">=</span><span class="op">-</span><span class="num">50</span><span class="op">,</span> <span class="nam">le</span><span class="op">=</span><span class="num">80</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Ambient temperature in Celsius"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1001" href="#t1001">1001</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t1002" href="#t1002">1002</a></span><span class="t">    <span class="nam">ground_temperature_c</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">float</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs1002" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t1003" href="#t1003">1003</a></span><span class="t">        <span class="key">None</span><span class="op">,</span> <span class="nam">ge</span><span class="op">=</span><span class="op">-</span><span class="num">50</span><span class="op">,</span> <span class="nam">le</span><span class="op">=</span><span class="num">80</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Ground temperature in Celsius"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1004" href="#t1004">1004</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t1005" href="#t1005">1005</a></span><span class="t">    <span class="nam">derating_factor</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">float</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs1005" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t1006" href="#t1006">1006</a></span><span class="t">        <span class="key">None</span><span class="op">,</span> <span class="nam">gt</span><span class="op">=</span><span class="num">0</span><span class="op">,</span> <span class="nam">le</span><span class="op">=</span><span class="num">1</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Derating factor"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1007" href="#t1007">1007</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t1008" href="#t1008">1008</a></span><span class="t">    <span class="nam">max_allowed_voltage_drop_percent</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">float</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs1008" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t1009" href="#t1009">1009</a></span><span class="t">        <span class="key">None</span><span class="op">,</span> <span class="nam">gt</span><span class="op">=</span><span class="num">0</span><span class="op">,</span> <span class="nam">le</span><span class="op">=</span><span class="num">50</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Maximum allowed voltage drop percentage"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1010" href="#t1010">1010</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t1011" href="#t1011">1011</a></span><span class="t">    <span class="nam">calculation_method</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">str</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="key">None</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Calculation method"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs1011" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t1012" href="#t1012">1012</a></span><span class="t">    <span class="nam">calculation_standard</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">str</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs1012" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t1013" href="#t1013">1013</a></span><span class="t">        <span class="key">None</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Calculation standard"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1014" href="#t1014">1014</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t1015" href="#t1015">1015</a></span><span class="t">    <span class="nam">calculation_notes</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">str</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="key">None</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Calculation notes"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs1015" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t1016" href="#t1016">1016</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1017" href="#t1017">1017</a></span><span class="t">    <span class="com"># Apply the same validators as create schema</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t1018" href="#t1018">1018</a></span><span class="t">    <span class="nam">_validate_name</span> <span class="op">=</span> <span class="nam">field_validator</span><span class="op">(</span><span class="str">"name"</span><span class="op">)</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs1018" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t1019" href="#t1019">1019</a></span><span class="t">        <span class="nam">VoltageDropCalculationBaseSchema</span><span class="op">.</span><span class="nam">validate_name</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1020" href="#t1020">1020</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1021" href="#t1021">1021</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1022" href="#t1022">1022</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t1023" href="#t1023">1023</a></span><span class="t"><span class="key">class</span> <span class="nam">VoltageDropCalculationReadSchema</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs1023" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t1024" href="#t1024">1024</a></span><span class="t">    <span class="nam">VoltageDropCalculationBaseSchema</span><span class="op">,</span> <span class="nam">BaseSoftDeleteSchema</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1025" href="#t1025">1025</a></span><span class="t"><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1026" href="#t1026">1026</a></span><span class="t">    <span class="str">"""Schema for reading/displaying voltage drop calculation data."""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1027" href="#t1027">1027</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t1028" href="#t1028">1028</a></span><span class="t">    <span class="nam">project_id</span><span class="op">:</span> <span class="nam">int</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="op">...</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Project ID"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs1028" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t1029" href="#t1029">1029</a></span><span class="t">    <span class="nam">cable_route_id</span><span class="op">:</span> <span class="nam">int</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="op">...</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Cable route ID"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs1029" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t1030" href="#t1030">1030</a></span><span class="t">    <span class="nam">load_calculation_id</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">int</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="key">None</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Load calculation ID"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs1030" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t1031" href="#t1031">1031</a></span><span class="t">    <span class="nam">calculated_voltage_drop_v</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">float</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs1031" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t1032" href="#t1032">1032</a></span><span class="t">        <span class="key">None</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Calculated voltage drop in volts"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1033" href="#t1033">1033</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t1034" href="#t1034">1034</a></span><span class="t">    <span class="nam">calculated_voltage_drop_percent</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">float</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs1034" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t1035" href="#t1035">1035</a></span><span class="t">        <span class="key">None</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Calculated voltage drop percentage"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1036" href="#t1036">1036</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t1037" href="#t1037">1037</a></span><span class="t">    <span class="nam">calculated_power_loss_w</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">float</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs1037" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t1038" href="#t1038">1038</a></span><span class="t">        <span class="key">None</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Calculated power loss in watts"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1039" href="#t1039">1039</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t1040" href="#t1040">1040</a></span><span class="t">    <span class="nam">calculated_efficiency_percent</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">float</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs1040" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t1041" href="#t1041">1041</a></span><span class="t">        <span class="key">None</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Calculated efficiency percentage"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1042" href="#t1042">1042</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t1043" href="#t1043">1043</a></span><span class="t">    <span class="nam">is_compliant</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">bool</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs1043" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t1044" href="#t1044">1044</a></span><span class="t">        <span class="key">None</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Compliance with voltage drop limits"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1045" href="#t1045">1045</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t1046" href="#t1046">1046</a></span><span class="t">    <span class="nam">compliance_margin_percent</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">float</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs1046" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t1047" href="#t1047">1047</a></span><span class="t">        <span class="key">None</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Compliance margin percentage"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1048" href="#t1048">1048</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1049" href="#t1049">1049</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t1050" href="#t1050">1050</a></span><span class="t">    <span class="nam">model_config</span> <span class="op">=</span> <span class="nam">ConfigDict</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs1050" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t1051" href="#t1051">1051</a></span><span class="t">        <span class="nam">from_attributes</span><span class="op">=</span><span class="key">True</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1052" href="#t1052">1052</a></span><span class="t">        <span class="nam">json_schema_extra</span><span class="op">=</span><span class="op">{</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1053" href="#t1053">1053</a></span><span class="t">            <span class="str">"example"</span><span class="op">:</span> <span class="op">{</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1054" href="#t1054">1054</a></span><span class="t">                <span class="str">"id"</span><span class="op">:</span> <span class="num">1</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1055" href="#t1055">1055</a></span><span class="t">                <span class="str">"name"</span><span class="op">:</span> <span class="str">"Voltage Drop Calc - Main Feed"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1056" href="#t1056">1056</a></span><span class="t">                <span class="str">"project_id"</span><span class="op">:</span> <span class="num">1</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1057" href="#t1057">1057</a></span><span class="t">                <span class="str">"cable_route_id"</span><span class="op">:</span> <span class="num">1</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1058" href="#t1058">1058</a></span><span class="t">                <span class="str">"load_calculation_id"</span><span class="op">:</span> <span class="num">1</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1059" href="#t1059">1059</a></span><span class="t">                <span class="str">"supply_voltage_v"</span><span class="op">:</span> <span class="num">240.0</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1060" href="#t1060">1060</a></span><span class="t">                <span class="str">"load_current_a"</span><span class="op">:</span> <span class="num">10.4</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1061" href="#t1061">1061</a></span><span class="t">                <span class="str">"cable_length_m"</span><span class="op">:</span> <span class="num">150.0</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1062" href="#t1062">1062</a></span><span class="t">                <span class="str">"calculated_voltage_drop_v"</span><span class="op">:</span> <span class="num">8.5</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1063" href="#t1063">1063</a></span><span class="t">                <span class="str">"calculated_voltage_drop_percent"</span><span class="op">:</span> <span class="num">3.5</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1064" href="#t1064">1064</a></span><span class="t">                <span class="str">"calculated_power_loss_w"</span><span class="op">:</span> <span class="num">88.4</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1065" href="#t1065">1065</a></span><span class="t">                <span class="str">"calculated_efficiency_percent"</span><span class="op">:</span> <span class="num">96.5</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1066" href="#t1066">1066</a></span><span class="t">                <span class="str">"is_compliant"</span><span class="op">:</span> <span class="key">True</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1067" href="#t1067">1067</a></span><span class="t">                <span class="str">"compliance_margin_percent"</span><span class="op">:</span> <span class="num">1.5</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1068" href="#t1068">1068</a></span><span class="t">                <span class="str">"created_at"</span><span class="op">:</span> <span class="str">"2024-01-15T10:30:00Z"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1069" href="#t1069">1069</a></span><span class="t">                <span class="str">"updated_at"</span><span class="op">:</span> <span class="str">"2024-01-15T10:30:00Z"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1070" href="#t1070">1070</a></span><span class="t">                <span class="str">"is_deleted"</span><span class="op">:</span> <span class="key">False</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1071" href="#t1071">1071</a></span><span class="t">                <span class="str">"deleted_at"</span><span class="op">:</span> <span class="key">None</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1072" href="#t1072">1072</a></span><span class="t">                <span class="str">"deleted_by_user_id"</span><span class="op">:</span> <span class="key">None</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1073" href="#t1073">1073</a></span><span class="t">            <span class="op">}</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1074" href="#t1074">1074</a></span><span class="t">        <span class="op">}</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1075" href="#t1075">1075</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1076" href="#t1076">1076</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1077" href="#t1077">1077</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t1078" href="#t1078">1078</a></span><span class="t"><span class="key">class</span> <span class="nam">VoltageDropCalculationSummarySchema</span><span class="op">(</span><span class="nam">BaseModel</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"><label for="ctxs1078" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t1079" href="#t1079">1079</a></span><span class="t">    <span class="str">"""Lightweight schema for voltage drop calculation listings."""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1080" href="#t1080">1080</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t1081" href="#t1081">1081</a></span><span class="t">    <span class="nam">id</span><span class="op">:</span> <span class="nam">int</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="op">...</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Voltage drop calculation ID"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs1081" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t1082" href="#t1082">1082</a></span><span class="t">    <span class="nam">name</span><span class="op">:</span> <span class="nam">str</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="op">...</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Voltage drop calculation name"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs1082" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t1083" href="#t1083">1083</a></span><span class="t">    <span class="nam">cable_route_id</span><span class="op">:</span> <span class="nam">int</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="op">...</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Cable route ID"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs1083" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t1084" href="#t1084">1084</a></span><span class="t">    <span class="nam">calculated_voltage_drop_percent</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">float</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs1084" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t1085" href="#t1085">1085</a></span><span class="t">        <span class="key">None</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Calculated voltage drop percentage"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1086" href="#t1086">1086</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t1087" href="#t1087">1087</a></span><span class="t">    <span class="nam">is_compliant</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">bool</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs1087" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t1088" href="#t1088">1088</a></span><span class="t">        <span class="key">None</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Compliance with voltage drop limits"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1089" href="#t1089">1089</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t1090" href="#t1090">1090</a></span><span class="t">    <span class="nam">max_allowed_voltage_drop_percent</span><span class="op">:</span> <span class="nam">float</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs1090" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t1091" href="#t1091">1091</a></span><span class="t">        <span class="op">...</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Maximum allowed voltage drop percentage"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1092" href="#t1092">1092</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1093" href="#t1093">1093</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t1094" href="#t1094">1094</a></span><span class="t">    <span class="nam">model_config</span> <span class="op">=</span> <span class="nam">ConfigDict</span><span class="op">(</span><span class="nam">from_attributes</span><span class="op">=</span><span class="key">True</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs1094" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t1095" href="#t1095">1095</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1096" href="#t1096">1096</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1097" href="#t1097">1097</a></span><span class="t"><span class="com"># ============================================================================</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1098" href="#t1098">1098</a></span><span class="t"><span class="com"># DESIGN WORKFLOW SCHEMAS</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1099" href="#t1099">1099</a></span><span class="t"><span class="com"># ============================================================================</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1100" href="#t1100">1100</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1101" href="#t1101">1101</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t1102" href="#t1102">1102</a></span><span class="t"><span class="key">class</span> <span class="nam">ElectricalDesignInputSchema</span><span class="op">(</span><span class="nam">BaseModel</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"><label for="ctxs1102" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t1103" href="#t1103">1103</a></span><span class="t">    <span class="str">"""Schema for electrical design workflow inputs."""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1104" href="#t1104">1104</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t1105" href="#t1105">1105</a></span><span class="t">    <span class="nam">project_id</span><span class="op">:</span> <span class="nam">int</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="op">...</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Project ID"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs1105" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t1106" href="#t1106">1106</a></span><span class="t">    <span class="nam">electrical_node_ids</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">List</span><span class="op">[</span><span class="nam">int</span><span class="op">]</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs1106" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t1107" href="#t1107">1107</a></span><span class="t">        <span class="key">None</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"List of electrical node IDs to design"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1108" href="#t1108">1108</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t1109" href="#t1109">1109</a></span><span class="t">    <span class="nam">cable_route_ids</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">List</span><span class="op">[</span><span class="nam">int</span><span class="op">]</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs1109" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t1110" href="#t1110">1110</a></span><span class="t">        <span class="key">None</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"List of cable route IDs to design"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1111" href="#t1111">1111</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t1112" href="#t1112">1112</a></span><span class="t">    <span class="nam">load_calculation_ids</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">List</span><span class="op">[</span><span class="nam">int</span><span class="op">]</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs1112" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t1113" href="#t1113">1113</a></span><span class="t">        <span class="key">None</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"List of load calculation IDs to include"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1114" href="#t1114">1114</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t1115" href="#t1115">1115</a></span><span class="t">    <span class="nam">design_parameters</span><span class="op">:</span> <span class="nam">Dict</span><span class="op">[</span><span class="nam">str</span><span class="op">,</span> <span class="nam">Any</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs1115" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t1116" href="#t1116">1116</a></span><span class="t">        <span class="nam">default_factory</span><span class="op">=</span><span class="nam">dict</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Design parameters"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1117" href="#t1117">1117</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t1118" href="#t1118">1118</a></span><span class="t">    <span class="nam">standards_context</span><span class="op">:</span> <span class="nam">ElectricalStandardsValidationInputSchema</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs1118" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t1119" href="#t1119">1119</a></span><span class="t">        <span class="op">...</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Standards validation context"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1120" href="#t1120">1120</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t1121" href="#t1121">1121</a></span><span class="t">    <span class="nam">auto_cable_sizing</span><span class="op">:</span> <span class="nam">bool</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs1121" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t1122" href="#t1122">1122</a></span><span class="t">        <span class="key">True</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Automatically perform cable sizing"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1123" href="#t1123">1123</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t1124" href="#t1124">1124</a></span><span class="t">    <span class="nam">auto_voltage_drop_calc</span><span class="op">:</span> <span class="nam">bool</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs1124" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t1125" href="#t1125">1125</a></span><span class="t">        <span class="key">True</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Automatically calculate voltage drop"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1126" href="#t1126">1126</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t1127" href="#t1127">1127</a></span><span class="t">    <span class="nam">optimization_enabled</span><span class="op">:</span> <span class="nam">bool</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="key">True</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Enable route optimization"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs1127" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t1128" href="#t1128">1128</a></span><span class="t">    <span class="nam">max_voltage_drop_percent</span><span class="op">:</span> <span class="nam">float</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs1128" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t1129" href="#t1129">1129</a></span><span class="t">        <span class="num">5.0</span><span class="op">,</span> <span class="nam">gt</span><span class="op">=</span><span class="num">0</span><span class="op">,</span> <span class="nam">le</span><span class="op">=</span><span class="num">50</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Maximum allowed voltage drop percentage"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1130" href="#t1130">1130</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1131" href="#t1131">1131</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t1132" href="#t1132">1132</a></span><span class="t">    <span class="op">@</span><span class="nam">field_validator</span><span class="op">(</span><span class="str">"electrical_node_ids"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs1132" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t1133" href="#t1133">1133</a></span><span class="t">    <span class="op">@</span><span class="nam">classmethod</span>&nbsp;</span><span class="r"><label for="ctxs1133" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t1134" href="#t1134">1134</a></span><span class="t">    <span class="key">def</span> <span class="nam">validate_node_or_route_ids</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs1134" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t1135" href="#t1135">1135</a></span><span class="t">        <span class="nam">cls</span><span class="op">,</span> <span class="nam">v</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">List</span><span class="op">[</span><span class="nam">int</span><span class="op">]</span><span class="op">]</span><span class="op">,</span> <span class="nam">info</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1136" href="#t1136">1136</a></span><span class="t">    <span class="op">)</span> <span class="op">-></span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">List</span><span class="op">[</span><span class="nam">int</span><span class="op">]</span><span class="op">]</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1137" href="#t1137">1137</a></span><span class="t">        <span class="str">"""Validate that either node_ids or route_ids is provided."""</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t1138" href="#t1138">1138</a></span><span class="t">        <span class="nam">route_ids</span> <span class="op">=</span> <span class="nam">info</span><span class="op">.</span><span class="nam">data</span><span class="op">.</span><span class="nam">get</span><span class="op">(</span><span class="str">"cable_route_ids"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t1139" href="#t1139">1139</a></span><span class="t">        <span class="key">if</span> <span class="op">(</span><span class="nam">v</span> <span class="key">is</span> <span class="key">None</span> <span class="key">or</span> <span class="nam">len</span><span class="op">(</span><span class="nam">v</span><span class="op">)</span> <span class="op">==</span> <span class="num">0</span><span class="op">)</span> <span class="key">and</span> <span class="op">(</span><span class="nam">route_ids</span> <span class="key">is</span> <span class="key">None</span> <span class="key">or</span> <span class="nam">len</span><span class="op">(</span><span class="nam">route_ids</span><span class="op">)</span> <span class="op">==</span> <span class="num">0</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t1140" href="#t1140">1140</a></span><span class="t">            <span class="key">raise</span> <span class="nam">ValueError</span><span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1141" href="#t1141">1141</a></span><span class="t">                <span class="str">"Either electrical_node_ids or cable_route_ids must be provided"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1142" href="#t1142">1142</a></span><span class="t">            <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t1143" href="#t1143">1143</a></span><span class="t">        <span class="key">return</span> <span class="nam">v</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1144" href="#t1144">1144</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1145" href="#t1145">1145</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t1146" href="#t1146">1146</a></span><span class="t"><span class="key">class</span> <span class="nam">ElectricalDesignResultSchema</span><span class="op">(</span><span class="nam">BaseModel</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"><label for="ctxs1146" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t1147" href="#t1147">1147</a></span><span class="t">    <span class="str">"""Schema for electrical design workflow results."""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1148" href="#t1148">1148</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t1149" href="#t1149">1149</a></span><span class="t">    <span class="nam">project_id</span><span class="op">:</span> <span class="nam">int</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="op">...</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Project ID"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs1149" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t1150" href="#t1150">1150</a></span><span class="t">    <span class="nam">designed_nodes</span><span class="op">:</span> <span class="nam">List</span><span class="op">[</span><span class="nam">ElectricalNodeReadSchema</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs1150" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t1151" href="#t1151">1151</a></span><span class="t">        <span class="nam">default_factory</span><span class="op">=</span><span class="nam">list</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Designed electrical nodes"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1152" href="#t1152">1152</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t1153" href="#t1153">1153</a></span><span class="t">    <span class="nam">designed_routes</span><span class="op">:</span> <span class="nam">List</span><span class="op">[</span><span class="nam">CableRouteReadSchema</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs1153" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t1154" href="#t1154">1154</a></span><span class="t">        <span class="nam">default_factory</span><span class="op">=</span><span class="nam">list</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Designed cable routes"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1155" href="#t1155">1155</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t1156" href="#t1156">1156</a></span><span class="t">    <span class="nam">created_segments</span><span class="op">:</span> <span class="nam">List</span><span class="op">[</span><span class="nam">CableSegmentReadSchema</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs1156" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t1157" href="#t1157">1157</a></span><span class="t">        <span class="nam">default_factory</span><span class="op">=</span><span class="nam">list</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Created cable segments"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1158" href="#t1158">1158</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t1159" href="#t1159">1159</a></span><span class="t">    <span class="nam">load_calculations</span><span class="op">:</span> <span class="nam">List</span><span class="op">[</span><span class="nam">LoadCalculationReadSchema</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs1159" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t1160" href="#t1160">1160</a></span><span class="t">        <span class="nam">default_factory</span><span class="op">=</span><span class="nam">list</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Load calculations"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1161" href="#t1161">1161</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t1162" href="#t1162">1162</a></span><span class="t">    <span class="nam">voltage_drop_calculations</span><span class="op">:</span> <span class="nam">List</span><span class="op">[</span><span class="nam">VoltageDropCalculationReadSchema</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs1162" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t1163" href="#t1163">1163</a></span><span class="t">        <span class="nam">default_factory</span><span class="op">=</span><span class="nam">list</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Voltage drop calculations"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1164" href="#t1164">1164</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t1165" href="#t1165">1165</a></span><span class="t">    <span class="nam">cable_sizing_results</span><span class="op">:</span> <span class="nam">List</span><span class="op">[</span><span class="nam">CableSizingCalculationResultSchema</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs1165" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t1166" href="#t1166">1166</a></span><span class="t">        <span class="nam">default_factory</span><span class="op">=</span><span class="nam">list</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Cable sizing results"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1167" href="#t1167">1167</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t1168" href="#t1168">1168</a></span><span class="t">    <span class="nam">standards_validation_results</span><span class="op">:</span> <span class="nam">List</span><span class="op">[</span><span class="nam">ElectricalStandardsValidationResultSchema</span><span class="op">]</span> <span class="op">=</span> <span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs1168" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t1169" href="#t1169">1169</a></span><span class="t">        <span class="nam">Field</span><span class="op">(</span><span class="nam">default_factory</span><span class="op">=</span><span class="nam">list</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Standards validation results"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1170" href="#t1170">1170</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t1171" href="#t1171">1171</a></span><span class="t">    <span class="nam">design_summary</span><span class="op">:</span> <span class="nam">Dict</span><span class="op">[</span><span class="nam">str</span><span class="op">,</span> <span class="nam">Any</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs1171" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t1172" href="#t1172">1172</a></span><span class="t">        <span class="nam">default_factory</span><span class="op">=</span><span class="nam">dict</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Design summary statistics"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1173" href="#t1173">1173</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t1174" href="#t1174">1174</a></span><span class="t">    <span class="nam">warnings</span><span class="op">:</span> <span class="nam">List</span><span class="op">[</span><span class="nam">str</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="nam">default_factory</span><span class="op">=</span><span class="nam">list</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Design warnings"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs1174" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t1175" href="#t1175">1175</a></span><span class="t">    <span class="nam">errors</span><span class="op">:</span> <span class="nam">List</span><span class="op">[</span><span class="nam">str</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="nam">default_factory</span><span class="op">=</span><span class="nam">list</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Design errors"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs1175" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t1176" href="#t1176">1176</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1177" href="#t1177">1177</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1178" href="#t1178">1178</a></span><span class="t"><span class="com"># ============================================================================</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1179" href="#t1179">1179</a></span><span class="t"><span class="com"># PAGINATED RESPONSE SCHEMAS</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1180" href="#t1180">1180</a></span><span class="t"><span class="com"># ============================================================================</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1181" href="#t1181">1181</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1182" href="#t1182">1182</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t1183" href="#t1183">1183</a></span><span class="t"><span class="key">class</span> <span class="nam">ElectricalNodeListResponseSchema</span><span class="op">(</span><span class="nam">PaginatedResponseSchema</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"><label for="ctxs1183" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t1184" href="#t1184">1184</a></span><span class="t">    <span class="str">"""Schema for paginated electrical node list responses."""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1185" href="#t1185">1185</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t1186" href="#t1186">1186</a></span><span class="t">    <span class="nam">electrical_nodes</span><span class="op">:</span> <span class="nam">List</span><span class="op">[</span><span class="nam">ElectricalNodeSummarySchema</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs1186" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t1187" href="#t1187">1187</a></span><span class="t">        <span class="op">...</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"List of electrical nodes"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1188" href="#t1188">1188</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1189" href="#t1189">1189</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t1190" href="#t1190">1190</a></span><span class="t">    <span class="nam">model_config</span> <span class="op">=</span> <span class="nam">ConfigDict</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs1190" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t1191" href="#t1191">1191</a></span><span class="t">        <span class="nam">json_schema_extra</span><span class="op">=</span><span class="op">{</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1192" href="#t1192">1192</a></span><span class="t">            <span class="str">"example"</span><span class="op">:</span> <span class="op">{</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1193" href="#t1193">1193</a></span><span class="t">                <span class="str">"electrical_nodes"</span><span class="op">:</span> <span class="op">[</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1194" href="#t1194">1194</a></span><span class="t">                    <span class="op">{</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1195" href="#t1195">1195</a></span><span class="t">                        <span class="str">"id"</span><span class="op">:</span> <span class="num">1</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1196" href="#t1196">1196</a></span><span class="t">                        <span class="str">"name"</span><span class="op">:</span> <span class="str">"Main Switchboard Incoming"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1197" href="#t1197">1197</a></span><span class="t">                        <span class="str">"project_id"</span><span class="op">:</span> <span class="num">1</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1198" href="#t1198">1198</a></span><span class="t">                        <span class="str">"node_type"</span><span class="op">:</span> <span class="str">"SWITCHBOARD_INCOMING"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1199" href="#t1199">1199</a></span><span class="t">                        <span class="str">"voltage_v"</span><span class="op">:</span> <span class="num">415.0</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1200" href="#t1200">1200</a></span><span class="t">                        <span class="str">"power_capacity_kva"</span><span class="op">:</span> <span class="num">100.0</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1201" href="#t1201">1201</a></span><span class="t">                    <span class="op">}</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1202" href="#t1202">1202</a></span><span class="t">                <span class="op">]</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1203" href="#t1203">1203</a></span><span class="t">                <span class="str">"total"</span><span class="op">:</span> <span class="num">1</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1204" href="#t1204">1204</a></span><span class="t">                <span class="str">"page"</span><span class="op">:</span> <span class="num">1</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1205" href="#t1205">1205</a></span><span class="t">                <span class="str">"per_page"</span><span class="op">:</span> <span class="num">10</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1206" href="#t1206">1206</a></span><span class="t">                <span class="str">"total_pages"</span><span class="op">:</span> <span class="num">1</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1207" href="#t1207">1207</a></span><span class="t">            <span class="op">}</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1208" href="#t1208">1208</a></span><span class="t">        <span class="op">}</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1209" href="#t1209">1209</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1210" href="#t1210">1210</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1211" href="#t1211">1211</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t1212" href="#t1212">1212</a></span><span class="t"><span class="key">class</span> <span class="nam">CableRouteListResponseSchema</span><span class="op">(</span><span class="nam">PaginatedResponseSchema</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"><label for="ctxs1212" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t1213" href="#t1213">1213</a></span><span class="t">    <span class="str">"""Schema for paginated cable route list responses."""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1214" href="#t1214">1214</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t1215" href="#t1215">1215</a></span><span class="t">    <span class="nam">cable_routes</span><span class="op">:</span> <span class="nam">List</span><span class="op">[</span><span class="nam">CableRouteSummarySchema</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs1215" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t1216" href="#t1216">1216</a></span><span class="t">        <span class="op">...</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"List of cable routes"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1217" href="#t1217">1217</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1218" href="#t1218">1218</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t1219" href="#t1219">1219</a></span><span class="t">    <span class="nam">model_config</span> <span class="op">=</span> <span class="nam">ConfigDict</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs1219" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t1220" href="#t1220">1220</a></span><span class="t">        <span class="nam">json_schema_extra</span><span class="op">=</span><span class="op">{</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1221" href="#t1221">1221</a></span><span class="t">            <span class="str">"example"</span><span class="op">:</span> <span class="op">{</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1222" href="#t1222">1222</a></span><span class="t">                <span class="str">"cable_routes"</span><span class="op">:</span> <span class="op">[</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1223" href="#t1223">1223</a></span><span class="t">                    <span class="op">{</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1224" href="#t1224">1224</a></span><span class="t">                        <span class="str">"id"</span><span class="op">:</span> <span class="num">1</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1225" href="#t1225">1225</a></span><span class="t">                        <span class="str">"name"</span><span class="op">:</span> <span class="str">"Main Feed to HT Panel"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1226" href="#t1226">1226</a></span><span class="t">                        <span class="str">"project_id"</span><span class="op">:</span> <span class="num">1</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1227" href="#t1227">1227</a></span><span class="t">                        <span class="str">"from_node_id"</span><span class="op">:</span> <span class="num">1</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1228" href="#t1228">1228</a></span><span class="t">                        <span class="str">"to_node_id"</span><span class="op">:</span> <span class="num">2</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1229" href="#t1229">1229</a></span><span class="t">                        <span class="str">"length_m"</span><span class="op">:</span> <span class="num">150.0</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1230" href="#t1230">1230</a></span><span class="t">                        <span class="str">"calculated_voltage_drop_v"</span><span class="op">:</span> <span class="num">8.5</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1231" href="#t1231">1231</a></span><span class="t">                        <span class="str">"installation_method"</span><span class="op">:</span> <span class="str">"CABLE_TRAY"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1232" href="#t1232">1232</a></span><span class="t">                    <span class="op">}</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1233" href="#t1233">1233</a></span><span class="t">                <span class="op">]</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1234" href="#t1234">1234</a></span><span class="t">                <span class="str">"total"</span><span class="op">:</span> <span class="num">1</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1235" href="#t1235">1235</a></span><span class="t">                <span class="str">"page"</span><span class="op">:</span> <span class="num">1</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1236" href="#t1236">1236</a></span><span class="t">                <span class="str">"per_page"</span><span class="op">:</span> <span class="num">10</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1237" href="#t1237">1237</a></span><span class="t">                <span class="str">"total_pages"</span><span class="op">:</span> <span class="num">1</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1238" href="#t1238">1238</a></span><span class="t">            <span class="op">}</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1239" href="#t1239">1239</a></span><span class="t">        <span class="op">}</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1240" href="#t1240">1240</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1241" href="#t1241">1241</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1242" href="#t1242">1242</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t1243" href="#t1243">1243</a></span><span class="t"><span class="key">class</span> <span class="nam">CableSegmentListResponseSchema</span><span class="op">(</span><span class="nam">PaginatedResponseSchema</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"><label for="ctxs1243" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t1244" href="#t1244">1244</a></span><span class="t">    <span class="str">"""Schema for paginated cable segment list responses."""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1245" href="#t1245">1245</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t1246" href="#t1246">1246</a></span><span class="t">    <span class="nam">cable_segments</span><span class="op">:</span> <span class="nam">List</span><span class="op">[</span><span class="nam">CableSegmentSummarySchema</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs1246" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t1247" href="#t1247">1247</a></span><span class="t">        <span class="op">...</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"List of cable segments"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1248" href="#t1248">1248</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1249" href="#t1249">1249</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t1250" href="#t1250">1250</a></span><span class="t">    <span class="nam">model_config</span> <span class="op">=</span> <span class="nam">ConfigDict</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs1250" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t1251" href="#t1251">1251</a></span><span class="t">        <span class="nam">json_schema_extra</span><span class="op">=</span><span class="op">{</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1252" href="#t1252">1252</a></span><span class="t">            <span class="str">"example"</span><span class="op">:</span> <span class="op">{</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1253" href="#t1253">1253</a></span><span class="t">                <span class="str">"cable_segments"</span><span class="op">:</span> <span class="op">[</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1254" href="#t1254">1254</a></span><span class="t">                    <span class="op">{</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1255" href="#t1255">1255</a></span><span class="t">                        <span class="str">"id"</span><span class="op">:</span> <span class="num">1</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1256" href="#t1256">1256</a></span><span class="t">                        <span class="str">"name"</span><span class="op">:</span> <span class="str">"Segment 1 - Underground"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1257" href="#t1257">1257</a></span><span class="t">                        <span class="str">"cable_route_id"</span><span class="op">:</span> <span class="num">1</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1258" href="#t1258">1258</a></span><span class="t">                        <span class="str">"segment_order"</span><span class="op">:</span> <span class="num">1</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1259" href="#t1259">1259</a></span><span class="t">                        <span class="str">"length_m"</span><span class="op">:</span> <span class="num">50.0</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1260" href="#t1260">1260</a></span><span class="t">                        <span class="str">"installation_method"</span><span class="op">:</span> <span class="str">"DIRECT_BURIED"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1261" href="#t1261">1261</a></span><span class="t">                        <span class="str">"calculated_voltage_drop_v"</span><span class="op">:</span> <span class="num">4.2</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1262" href="#t1262">1262</a></span><span class="t">                    <span class="op">}</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1263" href="#t1263">1263</a></span><span class="t">                <span class="op">]</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1264" href="#t1264">1264</a></span><span class="t">                <span class="str">"total"</span><span class="op">:</span> <span class="num">1</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1265" href="#t1265">1265</a></span><span class="t">                <span class="str">"page"</span><span class="op">:</span> <span class="num">1</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1266" href="#t1266">1266</a></span><span class="t">                <span class="str">"per_page"</span><span class="op">:</span> <span class="num">10</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1267" href="#t1267">1267</a></span><span class="t">                <span class="str">"total_pages"</span><span class="op">:</span> <span class="num">1</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1268" href="#t1268">1268</a></span><span class="t">            <span class="op">}</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1269" href="#t1269">1269</a></span><span class="t">        <span class="op">}</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1270" href="#t1270">1270</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1271" href="#t1271">1271</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1272" href="#t1272">1272</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t1273" href="#t1273">1273</a></span><span class="t"><span class="key">class</span> <span class="nam">LoadCalculationListResponseSchema</span><span class="op">(</span><span class="nam">PaginatedResponseSchema</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"><label for="ctxs1273" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t1274" href="#t1274">1274</a></span><span class="t">    <span class="str">"""Schema for paginated load calculation list responses."""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1275" href="#t1275">1275</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t1276" href="#t1276">1276</a></span><span class="t">    <span class="nam">load_calculations</span><span class="op">:</span> <span class="nam">List</span><span class="op">[</span><span class="nam">LoadCalculationSummarySchema</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs1276" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t1277" href="#t1277">1277</a></span><span class="t">        <span class="op">...</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"List of load calculations"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1278" href="#t1278">1278</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1279" href="#t1279">1279</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t1280" href="#t1280">1280</a></span><span class="t">    <span class="nam">model_config</span> <span class="op">=</span> <span class="nam">ConfigDict</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs1280" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t1281" href="#t1281">1281</a></span><span class="t">        <span class="nam">json_schema_extra</span><span class="op">=</span><span class="op">{</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1282" href="#t1282">1282</a></span><span class="t">            <span class="str">"example"</span><span class="op">:</span> <span class="op">{</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1283" href="#t1283">1283</a></span><span class="t">                <span class="str">"load_calculations"</span><span class="op">:</span> <span class="op">[</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1284" href="#t1284">1284</a></span><span class="t">                    <span class="op">{</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1285" href="#t1285">1285</a></span><span class="t">                        <span class="str">"id"</span><span class="op">:</span> <span class="num">1</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1286" href="#t1286">1286</a></span><span class="t">                        <span class="str">"name"</span><span class="op">:</span> <span class="str">"Heat Tracing Load HTC-001"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1287" href="#t1287">1287</a></span><span class="t">                        <span class="str">"electrical_node_id"</span><span class="op">:</span> <span class="num">2</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1288" href="#t1288">1288</a></span><span class="t">                        <span class="str">"load_type"</span><span class="op">:</span> <span class="str">"heat_tracing"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1289" href="#t1289">1289</a></span><span class="t">                        <span class="str">"rated_power_kw"</span><span class="op">:</span> <span class="num">2.5</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1290" href="#t1290">1290</a></span><span class="t">                        <span class="str">"calculated_operating_power_kw"</span><span class="op">:</span> <span class="num">2.0</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1291" href="#t1291">1291</a></span><span class="t">                    <span class="op">}</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1292" href="#t1292">1292</a></span><span class="t">                <span class="op">]</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1293" href="#t1293">1293</a></span><span class="t">                <span class="str">"total"</span><span class="op">:</span> <span class="num">1</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1294" href="#t1294">1294</a></span><span class="t">                <span class="str">"page"</span><span class="op">:</span> <span class="num">1</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1295" href="#t1295">1295</a></span><span class="t">                <span class="str">"per_page"</span><span class="op">:</span> <span class="num">10</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1296" href="#t1296">1296</a></span><span class="t">                <span class="str">"total_pages"</span><span class="op">:</span> <span class="num">1</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1297" href="#t1297">1297</a></span><span class="t">            <span class="op">}</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1298" href="#t1298">1298</a></span><span class="t">        <span class="op">}</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1299" href="#t1299">1299</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1300" href="#t1300">1300</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1301" href="#t1301">1301</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t1302" href="#t1302">1302</a></span><span class="t"><span class="key">class</span> <span class="nam">VoltageDropCalculationListResponseSchema</span><span class="op">(</span><span class="nam">PaginatedResponseSchema</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"><label for="ctxs1302" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t1303" href="#t1303">1303</a></span><span class="t">    <span class="str">"""Schema for paginated voltage drop calculation list responses."""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1304" href="#t1304">1304</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t1305" href="#t1305">1305</a></span><span class="t">    <span class="nam">voltage_drop_calculations</span><span class="op">:</span> <span class="nam">List</span><span class="op">[</span><span class="nam">VoltageDropCalculationSummarySchema</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs1305" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t1306" href="#t1306">1306</a></span><span class="t">        <span class="op">...</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"List of voltage drop calculations"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1307" href="#t1307">1307</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1308" href="#t1308">1308</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t1309" href="#t1309">1309</a></span><span class="t">    <span class="nam">model_config</span> <span class="op">=</span> <span class="nam">ConfigDict</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs1309" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t1310" href="#t1310">1310</a></span><span class="t">        <span class="nam">json_schema_extra</span><span class="op">=</span><span class="op">{</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1311" href="#t1311">1311</a></span><span class="t">            <span class="str">"example"</span><span class="op">:</span> <span class="op">{</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1312" href="#t1312">1312</a></span><span class="t">                <span class="str">"voltage_drop_calculations"</span><span class="op">:</span> <span class="op">[</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1313" href="#t1313">1313</a></span><span class="t">                    <span class="op">{</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1314" href="#t1314">1314</a></span><span class="t">                        <span class="str">"id"</span><span class="op">:</span> <span class="num">1</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1315" href="#t1315">1315</a></span><span class="t">                        <span class="str">"name"</span><span class="op">:</span> <span class="str">"Voltage Drop Calc - Main Feed"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1316" href="#t1316">1316</a></span><span class="t">                        <span class="str">"cable_route_id"</span><span class="op">:</span> <span class="num">1</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1317" href="#t1317">1317</a></span><span class="t">                        <span class="str">"calculated_voltage_drop_percent"</span><span class="op">:</span> <span class="num">3.5</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1318" href="#t1318">1318</a></span><span class="t">                        <span class="str">"is_compliant"</span><span class="op">:</span> <span class="key">True</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1319" href="#t1319">1319</a></span><span class="t">                        <span class="str">"max_allowed_voltage_drop_percent"</span><span class="op">:</span> <span class="num">5.0</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1320" href="#t1320">1320</a></span><span class="t">                    <span class="op">}</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1321" href="#t1321">1321</a></span><span class="t">                <span class="op">]</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1322" href="#t1322">1322</a></span><span class="t">                <span class="str">"total"</span><span class="op">:</span> <span class="num">1</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1323" href="#t1323">1323</a></span><span class="t">                <span class="str">"page"</span><span class="op">:</span> <span class="num">1</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1324" href="#t1324">1324</a></span><span class="t">                <span class="str">"per_page"</span><span class="op">:</span> <span class="num">10</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1325" href="#t1325">1325</a></span><span class="t">                <span class="str">"total_pages"</span><span class="op">:</span> <span class="num">1</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1326" href="#t1326">1326</a></span><span class="t">            <span class="op">}</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1327" href="#t1327">1327</a></span><span class="t">        <span class="op">}</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t1328" href="#t1328">1328</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="z_9b29dd34b81637e5_document_schemas_py.html">&#xab; prev</a> &nbsp; &nbsp;
            <a class="nav" href="index.html">&Hat; index</a> &nbsp; &nbsp;
            <a class="nav" href="z_9b29dd34b81637e5_error_py.html">&#xbb; next</a>
            &nbsp; &nbsp; &nbsp;
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.0">coverage.py v7.8.0</a>,
            created at 2025-06-03 23:24 +0300
        </p>
    </div>
</footer>
</body>
</html>
