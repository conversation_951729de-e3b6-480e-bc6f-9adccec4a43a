<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage for core\models\switchboard.py: 100.00%</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_718ce007.css" type="text/css">
    <script type="text/javascript">
        contexts = {
  "a": "(empty)"
}
    </script>
    <script src="coverage_html_cb_497bf287.js" defer></script>
</head>
<body class="pyfile">
<header>
    <div class="content">
        <h1>
            <span class="text">Coverage for </span><b>core\models\switchboard.py</b>:
            <span class="pc_cov">100.00%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>r</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>p</kbd>
                        &nbsp; toggle line displays
                    </p>
                    <p>
                        <kbd>j</kbd>
                        <kbd>k</kbd>
                        &nbsp; next/prev highlighted chunk
                    </p>
                    <p>
                        <kbd>0</kbd> &nbsp; (zero) top of page
                    </p>
                    <p>
                        <kbd>1</kbd> &nbsp; (one) first highlighted chunk
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>u</kbd> &nbsp; up to the index
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <h2>
            <span class="text">45 statements &nbsp;</span>
            <button type="button" class="run button_toggle_run" value="run" data-shortcut="r" title="Toggle lines run">45<span class="text"> run</span></button>
            <button type="button" class="mis show_mis button_toggle_mis" value="mis" data-shortcut="m" title="Toggle lines missing">0<span class="text"> missing</span></button>
            <button type="button" class="exc show_exc button_toggle_exc" value="exc" data-shortcut="x" title="Toggle lines excluded">8<span class="text"> excluded</span></button>
            <button type="button" class="par run show_par button_toggle_par" value="par" data-shortcut="p" title="Toggle lines partially run">0<span class="text"> partial</span></button>
        </h2>
        <p class="text">
            <a id="prevFileLink" class="nav" href="z_f2bf686f1e66ce65_project_py.html">&#xab; prev</a> &nbsp; &nbsp;
            <a id="indexLink" class="nav" href="index.html">&Hat; index</a> &nbsp; &nbsp;
            <a id="nextFileLink" class="nav" href="z_f2bf686f1e66ce65_users_py.html">&#xbb; next</a>
            &nbsp; &nbsp; &nbsp;
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.0">coverage.py v7.8.0</a>,
            created at 2025-06-03 23:24 +0300
        </p>
        <aside class="hidden">
            <button type="button" class="button_next_chunk" data-shortcut="j"></button>
            <button type="button" class="button_prev_chunk" data-shortcut="k"></button>
            <button type="button" class="button_top_of_page" data-shortcut="0"></button>
            <button type="button" class="button_first_chunk" data-shortcut="1"></button>
            <button type="button" class="button_prev_file" data-shortcut="["></button>
            <button type="button" class="button_next_file" data-shortcut="]"></button>
            <button type="button" class="button_to_index" data-shortcut="u"></button>
            <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
        </aside>
    </div>
</header>
<main id="source">
    <p class="run"><span class="n"><a id="t1" href="#t1">1</a></span><span class="t"><span class="key">from</span> <span class="nam">sqlalchemy</span> <span class="key">import</span> <span class="nam">ForeignKey</span><span class="op">,</span> <span class="nam">UniqueConstraint</span>&nbsp;</span><span class="r"><label for="ctxs1" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t2" href="#t2">2</a></span><span class="t"><span class="key">from</span> <span class="nam">sqlalchemy</span><span class="op">.</span><span class="nam">orm</span> <span class="key">import</span> <span class="nam">Mapped</span><span class="op">,</span> <span class="nam">mapped_column</span><span class="op">,</span> <span class="nam">relationship</span>&nbsp;</span><span class="r"><label for="ctxs2" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t3" href="#t3">3</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t4" href="#t4">4</a></span><span class="t"><span class="key">from</span> <span class="op">.</span><span class="nam">base</span> <span class="key">import</span> <span class="nam">Base</span><span class="op">,</span> <span class="nam">CommonColumns</span><span class="op">,</span> <span class="nam">EnumType</span><span class="op">,</span> <span class="nam">SoftDeleteColumns</span>&nbsp;</span><span class="r"><label for="ctxs4" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t5" href="#t5">5</a></span><span class="t"><span class="key">from</span> <span class="op">.</span><span class="nam">components</span> <span class="key">import</span> <span class="nam">Component</span>&nbsp;</span><span class="r"><label for="ctxs5" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t6" href="#t6">6</a></span><span class="t"><span class="com"># from .electrical import ElectricalNode</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t7" href="#t7">7</a></span><span class="t"><span class="key">from</span> <span class="op">.</span><span class="nam">enums</span> <span class="key">import</span> <span class="nam">SwitchboardType</span>&nbsp;</span><span class="r"><label for="ctxs7" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t8" href="#t8">8</a></span><span class="t"><span class="com"># from .heat_tracing import HTCircuit</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t9" href="#t9">9</a></span><span class="t"><span class="com"># from .project import Project</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t10" href="#t10">10</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t11" href="#t11">11</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t12" href="#t12">12</a></span><span class="t"><span class="key">class</span> <span class="nam">Switchboard</span><span class="op">(</span><span class="nam">CommonColumns</span><span class="op">,</span> <span class="nam">SoftDeleteColumns</span><span class="op">,</span> <span class="nam">Base</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"><label for="ctxs12" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t13" href="#t13">13</a></span><span class="t">    <span class="nam">__tablename__</span> <span class="op">=</span> <span class="str">"Switchboard"</span>&nbsp;</span><span class="r"><label for="ctxs13" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t14" href="#t14">14</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t15" href="#t15">15</a></span><span class="t">    <span class="nam">project_id</span><span class="op">:</span> <span class="nam">Mapped</span><span class="op">[</span><span class="nam">int</span><span class="op">]</span> <span class="op">=</span> <span class="nam">mapped_column</span><span class="op">(</span><span class="nam">ForeignKey</span><span class="op">(</span><span class="str">"Project.id"</span><span class="op">)</span><span class="op">,</span> <span class="nam">nullable</span><span class="op">=</span><span class="key">False</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs15" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t16" href="#t16">16</a></span><span class="t">    <span class="nam">location</span><span class="op">:</span> <span class="nam">Mapped</span><span class="op">[</span><span class="nam">str</span> <span class="op">|</span> <span class="key">None</span><span class="op">]</span> <span class="op">=</span> <span class="nam">mapped_column</span><span class="op">(</span><span class="nam">nullable</span><span class="op">=</span><span class="key">True</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs16" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t17" href="#t17">17</a></span><span class="t">    <span class="nam">voltage_level_v</span><span class="op">:</span> <span class="nam">Mapped</span><span class="op">[</span><span class="nam">int</span><span class="op">]</span> <span class="op">=</span> <span class="nam">mapped_column</span><span class="op">(</span><span class="nam">nullable</span><span class="op">=</span><span class="key">False</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs17" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t18" href="#t18">18</a></span><span class="t">    <span class="nam">number_of_phases</span><span class="op">:</span> <span class="nam">Mapped</span><span class="op">[</span><span class="nam">int</span><span class="op">]</span> <span class="op">=</span> <span class="nam">mapped_column</span><span class="op">(</span><span class="nam">nullable</span><span class="op">=</span><span class="key">False</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs18" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t19" href="#t19">19</a></span><span class="t">    <span class="nam">type</span><span class="op">:</span> <span class="nam">Mapped</span><span class="op">[</span><span class="nam">SwitchboardType</span> <span class="op">|</span> <span class="key">None</span><span class="op">]</span> <span class="op">=</span> <span class="nam">mapped_column</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs19" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t20" href="#t20">20</a></span><span class="t">        <span class="nam">EnumType</span><span class="op">(</span><span class="nam">SwitchboardType</span><span class="op">)</span><span class="op">,</span> <span class="nam">nullable</span><span class="op">=</span><span class="key">True</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t21" href="#t21">21</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t22" href="#t22">22</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t23" href="#t23">23</a></span><span class="t">    <span class="com"># Relationships</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t24" href="#t24">24</a></span><span class="t">    <span class="nam">project</span><span class="op">:</span> <span class="nam">Mapped</span><span class="op">[</span><span class="str">"Project"</span><span class="op">]</span> <span class="op">=</span> <span class="nam">relationship</span><span class="op">(</span><span class="nam">back_populates</span><span class="op">=</span><span class="str">"switchboards"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs24" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t25" href="#t25">25</a></span><span class="t">    <span class="nam">feeders</span><span class="op">:</span> <span class="nam">Mapped</span><span class="op">[</span><span class="nam">list</span><span class="op">[</span><span class="str">"Feeder"</span><span class="op">]</span><span class="op">]</span> <span class="op">=</span> <span class="nam">relationship</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs25" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t26" href="#t26">26</a></span><span class="t">        <span class="nam">back_populates</span><span class="op">=</span><span class="str">"switchboard"</span><span class="op">,</span> <span class="nam">cascade</span><span class="op">=</span><span class="str">"all, delete-orphan"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t27" href="#t27">27</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t28" href="#t28">28</a></span><span class="t">    <span class="nam">switchboard_components</span><span class="op">:</span> <span class="nam">Mapped</span><span class="op">[</span><span class="nam">list</span><span class="op">[</span><span class="str">"SwitchboardComponent"</span><span class="op">]</span><span class="op">]</span> <span class="op">=</span> <span class="nam">relationship</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs28" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t29" href="#t29">29</a></span><span class="t">        <span class="nam">back_populates</span><span class="op">=</span><span class="str">"switchboard"</span><span class="op">,</span> <span class="nam">cascade</span><span class="op">=</span><span class="str">"all, delete-orphan"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t30" href="#t30">30</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t31" href="#t31">31</a></span><span class="t">    <span class="nam">electrical_nodes</span><span class="op">:</span> <span class="nam">Mapped</span><span class="op">[</span><span class="nam">list</span><span class="op">[</span><span class="str">"ElectricalNode"</span><span class="op">]</span><span class="op">]</span> <span class="op">=</span> <span class="nam">relationship</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs31" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t32" href="#t32">32</a></span><span class="t">        <span class="nam">back_populates</span><span class="op">=</span><span class="str">"related_switchboard"</span><span class="op">,</span> <span class="nam">cascade</span><span class="op">=</span><span class="str">"all, delete-orphan"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t33" href="#t33">33</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t34" href="#t34">34</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t35" href="#t35">35</a></span><span class="t">    <span class="nam">__table_args__</span> <span class="op">=</span> <span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs35" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t36" href="#t36">36</a></span><span class="t">        <span class="nam">UniqueConstraint</span><span class="op">(</span><span class="str">"project_id"</span><span class="op">,</span> <span class="str">"name"</span><span class="op">,</span> <span class="nam">name</span><span class="op">=</span><span class="str">"uq_switchboard_project_name"</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t37" href="#t37">37</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t38" href="#t38">38</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="exc show_exc"><span class="n"><a id="t39" href="#t39">39</a></span><span class="t">    <span class="key">def</span> <span class="nam">__repr__</span><span class="op">(</span><span class="nam">self</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"><label for="ctxs39" class="ctx">(empty)</label></span></p>
    <p class="exc show_exc"><span class="n"><a id="t40" href="#t40">40</a></span><span class="t">        <span class="key">return</span> <span class="fst">f"</span><span class="fst">&lt;Switchboard(id=</span><span class="op">{</span><span class="nam">self</span><span class="op">.</span><span class="nam">id</span><span class="op">}</span><span class="fst">, name='</span><span class="op">{</span><span class="nam">self</span><span class="op">.</span><span class="nam">name</span><span class="op">}</span><span class="fst">', project_id=</span><span class="op">{</span><span class="nam">self</span><span class="op">.</span><span class="nam">project_id</span><span class="op">}</span><span class="fst">)></span><span class="fst">"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t41" href="#t41">41</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t42" href="#t42">42</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t43" href="#t43">43</a></span><span class="t"><span class="key">class</span> <span class="nam">Feeder</span><span class="op">(</span><span class="nam">CommonColumns</span><span class="op">,</span> <span class="nam">SoftDeleteColumns</span><span class="op">,</span> <span class="nam">Base</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"><label for="ctxs43" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t44" href="#t44">44</a></span><span class="t">    <span class="nam">__tablename__</span> <span class="op">=</span> <span class="str">"Feeder"</span>&nbsp;</span><span class="r"><label for="ctxs44" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t45" href="#t45">45</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t46" href="#t46">46</a></span><span class="t">    <span class="nam">switchboard_id</span><span class="op">:</span> <span class="nam">Mapped</span><span class="op">[</span><span class="nam">int</span><span class="op">]</span> <span class="op">=</span> <span class="nam">mapped_column</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs46" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t47" href="#t47">47</a></span><span class="t">        <span class="nam">ForeignKey</span><span class="op">(</span><span class="str">"Switchboard.id"</span><span class="op">)</span><span class="op">,</span> <span class="nam">nullable</span><span class="op">=</span><span class="key">False</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t48" href="#t48">48</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t49" href="#t49">49</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t50" href="#t50">50</a></span><span class="t">    <span class="com"># Relationships</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t51" href="#t51">51</a></span><span class="t">    <span class="nam">switchboard</span><span class="op">:</span> <span class="nam">Mapped</span><span class="op">[</span><span class="str">"Switchboard"</span><span class="op">]</span> <span class="op">=</span> <span class="nam">relationship</span><span class="op">(</span><span class="nam">back_populates</span><span class="op">=</span><span class="str">"feeders"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs51" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t52" href="#t52">52</a></span><span class="t">    <span class="nam">htcircuits</span><span class="op">:</span> <span class="nam">Mapped</span><span class="op">[</span><span class="nam">list</span><span class="op">[</span><span class="str">"HTCircuit"</span><span class="op">]</span><span class="op">]</span> <span class="op">=</span> <span class="nam">relationship</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs52" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t53" href="#t53">53</a></span><span class="t">        <span class="nam">back_populates</span><span class="op">=</span><span class="str">"feeder"</span><span class="op">,</span> <span class="nam">cascade</span><span class="op">=</span><span class="str">"all, delete-orphan"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t54" href="#t54">54</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t55" href="#t55">55</a></span><span class="t">    <span class="nam">feeder_components</span><span class="op">:</span> <span class="nam">Mapped</span><span class="op">[</span><span class="nam">list</span><span class="op">[</span><span class="str">"FeederComponent"</span><span class="op">]</span><span class="op">]</span> <span class="op">=</span> <span class="nam">relationship</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs55" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t56" href="#t56">56</a></span><span class="t">        <span class="nam">back_populates</span><span class="op">=</span><span class="str">"feeder"</span><span class="op">,</span> <span class="nam">cascade</span><span class="op">=</span><span class="str">"all, delete-orphan"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t57" href="#t57">57</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t58" href="#t58">58</a></span><span class="t">    <span class="nam">electrical_nodes</span><span class="op">:</span> <span class="nam">Mapped</span><span class="op">[</span><span class="nam">list</span><span class="op">[</span><span class="str">"ElectricalNode"</span><span class="op">]</span><span class="op">]</span> <span class="op">=</span> <span class="nam">relationship</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs58" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t59" href="#t59">59</a></span><span class="t">        <span class="nam">back_populates</span><span class="op">=</span><span class="str">"related_feeder"</span><span class="op">,</span> <span class="nam">cascade</span><span class="op">=</span><span class="str">"all, delete-orphan"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t60" href="#t60">60</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t61" href="#t61">61</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t62" href="#t62">62</a></span><span class="t">    <span class="nam">__table_args__</span> <span class="op">=</span> <span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs62" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t63" href="#t63">63</a></span><span class="t">        <span class="nam">UniqueConstraint</span><span class="op">(</span><span class="str">"switchboard_id"</span><span class="op">,</span> <span class="str">"name"</span><span class="op">,</span> <span class="nam">name</span><span class="op">=</span><span class="str">"uq_feeder_switchboard_name"</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t64" href="#t64">64</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t65" href="#t65">65</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="exc show_exc"><span class="n"><a id="t66" href="#t66">66</a></span><span class="t">    <span class="key">def</span> <span class="nam">__repr__</span><span class="op">(</span><span class="nam">self</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"><label for="ctxs66" class="ctx">(empty)</label></span></p>
    <p class="exc show_exc"><span class="n"><a id="t67" href="#t67">67</a></span><span class="t">        <span class="key">return</span> <span class="fst">f"</span><span class="fst">&lt;Feeder(id=</span><span class="op">{</span><span class="nam">self</span><span class="op">.</span><span class="nam">id</span><span class="op">}</span><span class="fst">, name='</span><span class="op">{</span><span class="nam">self</span><span class="op">.</span><span class="nam">name</span><span class="op">}</span><span class="fst">', switchboard_id=</span><span class="op">{</span><span class="nam">self</span><span class="op">.</span><span class="nam">switchboard_id</span><span class="op">}</span><span class="fst">)></span><span class="fst">"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t68" href="#t68">68</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t69" href="#t69">69</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t70" href="#t70">70</a></span><span class="t"><span class="com"># Junction tables for components specific to switchboards and feeders</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t71" href="#t71">71</a></span><span class="t"><span class="key">class</span> <span class="nam">SwitchboardComponent</span><span class="op">(</span><span class="nam">Base</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"><label for="ctxs71" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t72" href="#t72">72</a></span><span class="t">    <span class="nam">__tablename__</span> <span class="op">=</span> <span class="str">"SwitchboardComponent"</span>&nbsp;</span><span class="r"><label for="ctxs72" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t73" href="#t73">73</a></span><span class="t">    <span class="nam">id</span><span class="op">:</span> <span class="nam">Mapped</span><span class="op">[</span><span class="nam">int</span><span class="op">]</span> <span class="op">=</span> <span class="nam">mapped_column</span><span class="op">(</span><span class="nam">primary_key</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">autoincrement</span><span class="op">=</span><span class="key">True</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs73" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t74" href="#t74">74</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t75" href="#t75">75</a></span><span class="t">    <span class="nam">switchboard_id</span><span class="op">:</span> <span class="nam">Mapped</span><span class="op">[</span><span class="nam">int</span><span class="op">]</span> <span class="op">=</span> <span class="nam">mapped_column</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs75" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t76" href="#t76">76</a></span><span class="t">        <span class="nam">ForeignKey</span><span class="op">(</span><span class="str">"Switchboard.id"</span><span class="op">)</span><span class="op">,</span> <span class="nam">nullable</span><span class="op">=</span><span class="key">False</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t77" href="#t77">77</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t78" href="#t78">78</a></span><span class="t">    <span class="nam">component_id</span><span class="op">:</span> <span class="nam">Mapped</span><span class="op">[</span><span class="nam">int</span><span class="op">]</span> <span class="op">=</span> <span class="nam">mapped_column</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs78" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t79" href="#t79">79</a></span><span class="t">        <span class="nam">ForeignKey</span><span class="op">(</span><span class="str">"Component.id"</span><span class="op">)</span><span class="op">,</span> <span class="nam">nullable</span><span class="op">=</span><span class="key">False</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t80" href="#t80">80</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t81" href="#t81">81</a></span><span class="t">    <span class="nam">quantity</span><span class="op">:</span> <span class="nam">Mapped</span><span class="op">[</span><span class="nam">int</span><span class="op">]</span> <span class="op">=</span> <span class="nam">mapped_column</span><span class="op">(</span><span class="nam">default</span><span class="op">=</span><span class="num">1</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs81" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t82" href="#t82">82</a></span><span class="t">    <span class="nam">position</span><span class="op">:</span> <span class="nam">Mapped</span><span class="op">[</span><span class="nam">str</span> <span class="op">|</span> <span class="key">None</span><span class="op">]</span> <span class="op">=</span> <span class="nam">mapped_column</span><span class="op">(</span><span class="nam">nullable</span><span class="op">=</span><span class="key">True</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs82" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t83" href="#t83">83</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t84" href="#t84">84</a></span><span class="t">    <span class="nam">switchboard</span><span class="op">:</span> <span class="nam">Mapped</span><span class="op">[</span><span class="str">"Switchboard"</span><span class="op">]</span> <span class="op">=</span> <span class="nam">relationship</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs84" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t85" href="#t85">85</a></span><span class="t">        <span class="nam">back_populates</span><span class="op">=</span><span class="str">"switchboard_components"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t86" href="#t86">86</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t87" href="#t87">87</a></span><span class="t">    <span class="nam">component</span><span class="op">:</span> <span class="nam">Mapped</span><span class="op">[</span><span class="str">"Component"</span><span class="op">]</span> <span class="op">=</span> <span class="nam">relationship</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs87" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t88" href="#t88">88</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t89" href="#t89">89</a></span><span class="t">    <span class="nam">__table_args__</span> <span class="op">=</span> <span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs89" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t90" href="#t90">90</a></span><span class="t">        <span class="nam">UniqueConstraint</span><span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t91" href="#t91">91</a></span><span class="t">            <span class="str">"switchboard_id"</span><span class="op">,</span> <span class="str">"component_id"</span><span class="op">,</span> <span class="str">"position"</span><span class="op">,</span> <span class="nam">name</span><span class="op">=</span><span class="str">"uq_swbd_comp"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t92" href="#t92">92</a></span><span class="t">        <span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t93" href="#t93">93</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t94" href="#t94">94</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="exc show_exc"><span class="n"><a id="t95" href="#t95">95</a></span><span class="t">    <span class="key">def</span> <span class="nam">__repr__</span><span class="op">(</span><span class="nam">self</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"><label for="ctxs95" class="ctx">(empty)</label></span></p>
    <p class="exc show_exc"><span class="n"><a id="t96" href="#t96">96</a></span><span class="t">        <span class="key">return</span> <span class="fst">f"</span><span class="fst">&lt;SwitchboardComponent(swbd_id=</span><span class="op">{</span><span class="nam">self</span><span class="op">.</span><span class="nam">switchboard_id</span><span class="op">}</span><span class="fst">, comp_id=</span><span class="op">{</span><span class="nam">self</span><span class="op">.</span><span class="nam">component_id</span><span class="op">}</span><span class="fst">, qty=</span><span class="op">{</span><span class="nam">self</span><span class="op">.</span><span class="nam">quantity</span><span class="op">}</span><span class="fst">)></span><span class="fst">"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t97" href="#t97">97</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t98" href="#t98">98</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t99" href="#t99">99</a></span><span class="t"><span class="key">class</span> <span class="nam">FeederComponent</span><span class="op">(</span><span class="nam">Base</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"><label for="ctxs99" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t100" href="#t100">100</a></span><span class="t">    <span class="nam">__tablename__</span> <span class="op">=</span> <span class="str">"FeederComponent"</span>&nbsp;</span><span class="r"><label for="ctxs100" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t101" href="#t101">101</a></span><span class="t">    <span class="nam">id</span><span class="op">:</span> <span class="nam">Mapped</span><span class="op">[</span><span class="nam">int</span><span class="op">]</span> <span class="op">=</span> <span class="nam">mapped_column</span><span class="op">(</span><span class="nam">primary_key</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">autoincrement</span><span class="op">=</span><span class="key">True</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs101" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t102" href="#t102">102</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t103" href="#t103">103</a></span><span class="t">    <span class="nam">feeder_id</span><span class="op">:</span> <span class="nam">Mapped</span><span class="op">[</span><span class="nam">int</span><span class="op">]</span> <span class="op">=</span> <span class="nam">mapped_column</span><span class="op">(</span><span class="nam">ForeignKey</span><span class="op">(</span><span class="str">"Feeder.id"</span><span class="op">)</span><span class="op">,</span> <span class="nam">nullable</span><span class="op">=</span><span class="key">False</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs103" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t104" href="#t104">104</a></span><span class="t">    <span class="nam">component_id</span><span class="op">:</span> <span class="nam">Mapped</span><span class="op">[</span><span class="nam">int</span><span class="op">]</span> <span class="op">=</span> <span class="nam">mapped_column</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs104" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t105" href="#t105">105</a></span><span class="t">        <span class="nam">ForeignKey</span><span class="op">(</span><span class="str">"Component.id"</span><span class="op">)</span><span class="op">,</span> <span class="nam">nullable</span><span class="op">=</span><span class="key">False</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t106" href="#t106">106</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t107" href="#t107">107</a></span><span class="t">    <span class="nam">quantity</span><span class="op">:</span> <span class="nam">Mapped</span><span class="op">[</span><span class="nam">int</span><span class="op">]</span> <span class="op">=</span> <span class="nam">mapped_column</span><span class="op">(</span><span class="nam">default</span><span class="op">=</span><span class="num">1</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs107" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t108" href="#t108">108</a></span><span class="t">    <span class="nam">position</span><span class="op">:</span> <span class="nam">Mapped</span><span class="op">[</span><span class="nam">str</span> <span class="op">|</span> <span class="key">None</span><span class="op">]</span> <span class="op">=</span> <span class="nam">mapped_column</span><span class="op">(</span><span class="nam">nullable</span><span class="op">=</span><span class="key">True</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs108" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t109" href="#t109">109</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t110" href="#t110">110</a></span><span class="t">    <span class="nam">feeder</span><span class="op">:</span> <span class="nam">Mapped</span><span class="op">[</span><span class="str">"Feeder"</span><span class="op">]</span> <span class="op">=</span> <span class="nam">relationship</span><span class="op">(</span><span class="nam">back_populates</span><span class="op">=</span><span class="str">"feeder_components"</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs110" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t111" href="#t111">111</a></span><span class="t">    <span class="nam">component</span><span class="op">:</span> <span class="nam">Mapped</span><span class="op">[</span><span class="str">"Component"</span><span class="op">]</span> <span class="op">=</span> <span class="nam">relationship</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs111" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t112" href="#t112">112</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t113" href="#t113">113</a></span><span class="t">    <span class="nam">__table_args__</span> <span class="op">=</span> <span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs113" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t114" href="#t114">114</a></span><span class="t">        <span class="nam">UniqueConstraint</span><span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t115" href="#t115">115</a></span><span class="t">            <span class="str">"feeder_id"</span><span class="op">,</span> <span class="str">"component_id"</span><span class="op">,</span> <span class="str">"position"</span><span class="op">,</span> <span class="nam">name</span><span class="op">=</span><span class="str">"uq_feeder_comp"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t116" href="#t116">116</a></span><span class="t">        <span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t117" href="#t117">117</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t118" href="#t118">118</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="exc show_exc"><span class="n"><a id="t119" href="#t119">119</a></span><span class="t">    <span class="key">def</span> <span class="nam">__repr__</span><span class="op">(</span><span class="nam">self</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"><label for="ctxs119" class="ctx">(empty)</label></span></p>
    <p class="exc show_exc"><span class="n"><a id="t120" href="#t120">120</a></span><span class="t">        <span class="key">return</span> <span class="fst">f"</span><span class="fst">&lt;FeederComponent(feeder_id=</span><span class="op">{</span><span class="nam">self</span><span class="op">.</span><span class="nam">feeder_id</span><span class="op">}</span><span class="fst">, comp_id=</span><span class="op">{</span><span class="nam">self</span><span class="op">.</span><span class="nam">component_id</span><span class="op">}</span><span class="fst">, qty=</span><span class="op">{</span><span class="nam">self</span><span class="op">.</span><span class="nam">quantity</span><span class="op">}</span><span class="fst">)></span><span class="fst">"</span>&nbsp;</span><span class="r"></span></p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="z_f2bf686f1e66ce65_project_py.html">&#xab; prev</a> &nbsp; &nbsp;
            <a class="nav" href="index.html">&Hat; index</a> &nbsp; &nbsp;
            <a class="nav" href="z_f2bf686f1e66ce65_users_py.html">&#xbb; next</a>
            &nbsp; &nbsp; &nbsp;
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.0">coverage.py v7.8.0</a>,
            created at 2025-06-03 23:24 +0300
        </p>
    </div>
</footer>
</body>
</html>
