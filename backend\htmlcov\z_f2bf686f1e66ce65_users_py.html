<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage for core\models\users.py: 100.00%</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_718ce007.css" type="text/css">
    <script type="text/javascript">
        contexts = {
  "a": "(empty)"
}
    </script>
    <script src="coverage_html_cb_497bf287.js" defer></script>
</head>
<body class="pyfile">
<header>
    <div class="content">
        <h1>
            <span class="text">Coverage for </span><b>core\models\users.py</b>:
            <span class="pc_cov">100.00%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>r</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>p</kbd>
                        &nbsp; toggle line displays
                    </p>
                    <p>
                        <kbd>j</kbd>
                        <kbd>k</kbd>
                        &nbsp; next/prev highlighted chunk
                    </p>
                    <p>
                        <kbd>0</kbd> &nbsp; (zero) top of page
                    </p>
                    <p>
                        <kbd>1</kbd> &nbsp; (one) first highlighted chunk
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>u</kbd> &nbsp; up to the index
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <h2>
            <span class="text">24 statements &nbsp;</span>
            <button type="button" class="run button_toggle_run" value="run" data-shortcut="r" title="Toggle lines run">24<span class="text"> run</span></button>
            <button type="button" class="mis show_mis button_toggle_mis" value="mis" data-shortcut="m" title="Toggle lines missing">0<span class="text"> missing</span></button>
            <button type="button" class="exc show_exc button_toggle_exc" value="exc" data-shortcut="x" title="Toggle lines excluded">7<span class="text"> excluded</span></button>
            <button type="button" class="par run show_par button_toggle_par" value="par" data-shortcut="p" title="Toggle lines partially run">0<span class="text"> partial</span></button>
        </h2>
        <p class="text">
            <a id="prevFileLink" class="nav" href="z_f2bf686f1e66ce65_switchboard_py.html">&#xab; prev</a> &nbsp; &nbsp;
            <a id="indexLink" class="nav" href="index.html">&Hat; index</a> &nbsp; &nbsp;
            <a id="nextFileLink" class="nav" href="z_a6617d05fb563bba___init___py.html">&#xbb; next</a>
            &nbsp; &nbsp; &nbsp;
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.0">coverage.py v7.8.0</a>,
            created at 2025-06-03 23:24 +0300
        </p>
        <aside class="hidden">
            <button type="button" class="button_next_chunk" data-shortcut="j"></button>
            <button type="button" class="button_prev_chunk" data-shortcut="k"></button>
            <button type="button" class="button_top_of_page" data-shortcut="0"></button>
            <button type="button" class="button_first_chunk" data-shortcut="1"></button>
            <button type="button" class="button_prev_file" data-shortcut="["></button>
            <button type="button" class="button_next_file" data-shortcut="]"></button>
            <button type="button" class="button_to_index" data-shortcut="u"></button>
            <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
        </aside>
    </div>
</header>
<main id="source">
    <p class="run"><span class="n"><a id="t1" href="#t1">1</a></span><span class="t"><span class="key">from</span> <span class="nam">typing</span> <span class="key">import</span> <span class="nam">TYPE_CHECKING</span>&nbsp;</span><span class="r"><label for="ctxs1" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t2" href="#t2">2</a></span><span class="t"><span class="key">from</span> <span class="nam">sqlalchemy</span> <span class="key">import</span> <span class="nam">Boolean</span><span class="op">,</span> <span class="nam">ForeignKey</span><span class="op">,</span> <span class="nam">UniqueConstraint</span>&nbsp;</span><span class="r"><label for="ctxs2" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t3" href="#t3">3</a></span><span class="t"><span class="key">from</span> <span class="nam">sqlalchemy</span><span class="op">.</span><span class="nam">orm</span> <span class="key">import</span> <span class="nam">Mapped</span><span class="op">,</span> <span class="nam">mapped_column</span><span class="op">,</span> <span class="nam">relationship</span>&nbsp;</span><span class="r"><label for="ctxs3" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t4" href="#t4">4</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t5" href="#t5">5</a></span><span class="t"><span class="com"># from .activity_log import ActivityLog</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t6" href="#t6">6</a></span><span class="t"><span class="key">from</span> <span class="op">.</span><span class="nam">base</span> <span class="key">import</span> <span class="op">(</span>  <span class="com"># SoftDeleteColumns is used by UserPreference</span>&nbsp;</span><span class="r"><label for="ctxs6" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t7" href="#t7">7</a></span><span class="t">    <span class="nam">Base</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t8" href="#t8">8</a></span><span class="t">    <span class="nam">CommonColumns</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t9" href="#t9">9</a></span><span class="t">    <span class="nam">SoftDeleteColumns</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t10" href="#t10">10</a></span><span class="t"><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t11" href="#t11">11</a></span><span class="t"><span class="key">from</span> <span class="nam">core</span><span class="op">.</span><span class="nam">utils</span><span class="op">.</span><span class="nam">json_validation</span> <span class="key">import</span> <span class="nam">FlexibleJSON</span>&nbsp;</span><span class="r"><label for="ctxs11" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t12" href="#t12">12</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="exc show_exc"><span class="n"><a id="t13" href="#t13">13</a></span><span class="t"><span class="key">if</span> <span class="nam">TYPE_CHECKING</span><span class="op">:</span>&nbsp;</span><span class="r"><label for="ctxs13" class="ctx">(empty)</label></span></p>
    <p class="exc show_exc"><span class="n"><a id="t14" href="#t14">14</a></span><span class="t">    <span class="key">from</span> <span class="op">.</span><span class="nam">documents</span> <span class="key">import</span> <span class="nam">ExportedDocument</span><span class="op">,</span> <span class="nam">ImportedDataRevision</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t15" href="#t15">15</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t16" href="#t16">16</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t17" href="#t17">17</a></span><span class="t"><span class="key">class</span> <span class="nam">User</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs17" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t18" href="#t18">18</a></span><span class="t">    <span class="nam">CommonColumns</span><span class="op">,</span> <span class="nam">Base</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t19" href="#t19">19</a></span><span class="t"><span class="op">)</span><span class="op">:</span>  <span class="com"># User itself does not need soft delete, as it's the actor</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t20" href="#t20">20</a></span><span class="t">    <span class="nam">__tablename__</span> <span class="op">=</span> <span class="str">"User"</span>&nbsp;</span><span class="r"><label for="ctxs20" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t21" href="#t21">21</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t22" href="#t22">22</a></span><span class="t">    <span class="nam">email</span><span class="op">:</span> <span class="nam">Mapped</span><span class="op">[</span><span class="nam">str</span> <span class="op">|</span> <span class="key">None</span><span class="op">]</span> <span class="op">=</span> <span class="nam">mapped_column</span><span class="op">(</span><span class="nam">unique</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">nullable</span><span class="op">=</span><span class="key">True</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs22" class="ctx">(empty)</label></span></p>
    <p class="exc show_exc"><span class="n"><a id="t23" href="#t23">23</a></span><span class="t">    <span class="nam">password_hash</span><span class="op">:</span> <span class="nam">Mapped</span><span class="op">[</span><span class="nam">str</span> <span class="op">|</span> <span class="key">None</span><span class="op">]</span> <span class="op">=</span> <span class="nam">mapped_column</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs23" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t24" href="#t24">24</a></span><span class="t">        <span class="nam">nullable</span><span class="op">=</span><span class="key">True</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t25" href="#t25">25</a></span><span class="t">    <span class="op">)</span>  <span class="com"># Store hashed passwords, not plain text</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t26" href="#t26">26</a></span><span class="t">    <span class="nam">is_active</span><span class="op">:</span> <span class="nam">Mapped</span><span class="op">[</span><span class="nam">bool</span><span class="op">]</span> <span class="op">=</span> <span class="nam">mapped_column</span><span class="op">(</span><span class="nam">Boolean</span><span class="op">,</span> <span class="nam">default</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">nullable</span><span class="op">=</span><span class="key">False</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs26" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t27" href="#t27">27</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t28" href="#t28">28</a></span><span class="t">    <span class="com"># Relationships</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t29" href="#t29">29</a></span><span class="t">    <span class="nam">preferences</span><span class="op">:</span> <span class="nam">Mapped</span><span class="op">[</span><span class="str">"UserPreference | None"</span><span class="op">]</span> <span class="op">=</span> <span class="nam">relationship</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs29" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t30" href="#t30">30</a></span><span class="t">        <span class="str">"UserPreference"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t31" href="#t31">31</a></span><span class="t">        <span class="nam">back_populates</span><span class="op">=</span><span class="str">"user"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t32" href="#t32">32</a></span><span class="t">        <span class="nam">cascade</span><span class="op">=</span><span class="str">"all, delete-orphan"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t33" href="#t33">33</a></span><span class="t">        <span class="nam">uselist</span><span class="op">=</span><span class="key">False</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t34" href="#t34">34</a></span><span class="t">        <span class="nam">foreign_keys</span><span class="op">=</span><span class="str">"[UserPreference.user_id]"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t35" href="#t35">35</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t36" href="#t36">36</a></span><span class="t">    <span class="com"># TODO: Uncomment when ActivityLog model is implemented</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t37" href="#t37">37</a></span><span class="t">    <span class="com"># activity_logs: Mapped[list["ActivityLog"]] = relationship(</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t38" href="#t38">38</a></span><span class="t">    <span class="com">#     "ActivityLog", back_populates="user"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t39" href="#t39">39</a></span><span class="t">    <span class="com"># )</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t40" href="#t40">40</a></span><span class="t">    <span class="nam">imported_data_revisions</span><span class="op">:</span> <span class="nam">Mapped</span><span class="op">[</span><span class="nam">list</span><span class="op">[</span><span class="str">"ImportedDataRevision"</span><span class="op">]</span><span class="op">]</span> <span class="op">=</span> <span class="nam">relationship</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs40" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t41" href="#t41">41</a></span><span class="t">        <span class="str">"ImportedDataRevision"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t42" href="#t42">42</a></span><span class="t">        <span class="nam">back_populates</span><span class="op">=</span><span class="str">"imported_by_user"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t43" href="#t43">43</a></span><span class="t">        <span class="nam">foreign_keys</span><span class="op">=</span><span class="str">"ImportedDataRevision.imported_by_user_id"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t44" href="#t44">44</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t45" href="#t45">45</a></span><span class="t">    <span class="nam">exported_documents</span><span class="op">:</span> <span class="nam">Mapped</span><span class="op">[</span><span class="nam">list</span><span class="op">[</span><span class="str">"ExportedDocument"</span><span class="op">]</span><span class="op">]</span> <span class="op">=</span> <span class="nam">relationship</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs45" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t46" href="#t46">46</a></span><span class="t">        <span class="str">"ExportedDocument"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t47" href="#t47">47</a></span><span class="t">        <span class="nam">back_populates</span><span class="op">=</span><span class="str">"generated_by_user"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t48" href="#t48">48</a></span><span class="t">        <span class="nam">foreign_keys</span><span class="op">=</span><span class="str">"ExportedDocument.generated_by_user_id"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t49" href="#t49">49</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t50" href="#t50">50</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t51" href="#t51">51</a></span><span class="t">    <span class="nam">__table_args__</span> <span class="op">=</span> <span class="op">(</span><span class="nam">UniqueConstraint</span><span class="op">(</span><span class="str">"name"</span><span class="op">,</span> <span class="nam">name</span><span class="op">=</span><span class="str">"uq_user_name"</span><span class="op">)</span><span class="op">,</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs51" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t52" href="#t52">52</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="exc show_exc"><span class="n"><a id="t53" href="#t53">53</a></span><span class="t">    <span class="key">def</span> <span class="nam">__repr__</span><span class="op">(</span><span class="nam">self</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"><label for="ctxs53" class="ctx">(empty)</label></span></p>
    <p class="exc show_exc"><span class="n"><a id="t54" href="#t54">54</a></span><span class="t">        <span class="key">return</span> <span class="fst">f"</span><span class="fst">&lt;User(id=</span><span class="op">{</span><span class="nam">self</span><span class="op">.</span><span class="nam">id</span><span class="op">}</span><span class="fst">, name='</span><span class="op">{</span><span class="nam">self</span><span class="op">.</span><span class="nam">name</span><span class="op">}</span><span class="fst">', email='</span><span class="op">{</span><span class="nam">self</span><span class="op">.</span><span class="nam">email</span><span class="op">}</span><span class="fst">')></span><span class="fst">"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t55" href="#t55">55</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t56" href="#t56">56</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t57" href="#t57">57</a></span><span class="t"><span class="key">class</span> <span class="nam">UserPreference</span><span class="op">(</span><span class="nam">CommonColumns</span><span class="op">,</span> <span class="nam">SoftDeleteColumns</span><span class="op">,</span> <span class="nam">Base</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"><label for="ctxs57" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t58" href="#t58">58</a></span><span class="t">    <span class="nam">__tablename__</span> <span class="op">=</span> <span class="str">"UserPreference"</span>&nbsp;</span><span class="r"><label for="ctxs58" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t59" href="#t59">59</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t60" href="#t60">60</a></span><span class="t">    <span class="nam">user_id</span><span class="op">:</span> <span class="nam">Mapped</span><span class="op">[</span><span class="nam">int</span><span class="op">]</span> <span class="op">=</span> <span class="nam">mapped_column</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs60" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t61" href="#t61">61</a></span><span class="t">        <span class="nam">ForeignKey</span><span class="op">(</span><span class="str">"User.id"</span><span class="op">)</span><span class="op">,</span> <span class="nam">nullable</span><span class="op">=</span><span class="key">False</span><span class="op">,</span> <span class="nam">unique</span><span class="op">=</span><span class="key">True</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t62" href="#t62">62</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t63" href="#t63">63</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t64" href="#t64">64</a></span><span class="t">    <span class="nam">ui_theme</span><span class="op">:</span> <span class="nam">Mapped</span><span class="op">[</span><span class="nam">str</span><span class="op">]</span> <span class="op">=</span> <span class="nam">mapped_column</span><span class="op">(</span><span class="nam">default</span><span class="op">=</span><span class="str">"light"</span><span class="op">,</span> <span class="nam">nullable</span><span class="op">=</span><span class="key">False</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs64" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t65" href="#t65">65</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t66" href="#t66">66</a></span><span class="t">    <span class="nam">default_min_ambient_temp_c</span><span class="op">:</span> <span class="nam">Mapped</span><span class="op">[</span><span class="nam">float</span> <span class="op">|</span> <span class="key">None</span><span class="op">]</span> <span class="op">=</span> <span class="nam">mapped_column</span><span class="op">(</span><span class="nam">nullable</span><span class="op">=</span><span class="key">True</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs66" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t67" href="#t67">67</a></span><span class="t">    <span class="nam">default_max_ambient_temp_c</span><span class="op">:</span> <span class="nam">Mapped</span><span class="op">[</span><span class="nam">float</span> <span class="op">|</span> <span class="key">None</span><span class="op">]</span> <span class="op">=</span> <span class="nam">mapped_column</span><span class="op">(</span><span class="nam">nullable</span><span class="op">=</span><span class="key">True</span><span class="op">)</span>&nbsp;</span><span class="r"><label for="ctxs67" class="ctx">(empty)</label></span></p>
    <p class="run"><span class="n"><a id="t68" href="#t68">68</a></span><span class="t">    <span class="nam">default_desired_maintenance_temp_c</span><span class="op">:</span> <span class="nam">Mapped</span><span class="op">[</span><span class="nam">float</span> <span class="op">|</span> <span class="key">None</span><span class="op">]</span> <span class="op">=</span> <span class="nam">mapped_column</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs68" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t69" href="#t69">69</a></span><span class="t">        <span class="nam">nullable</span><span class="op">=</span><span class="key">True</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t70" href="#t70">70</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t71" href="#t71">71</a></span><span class="t">    <span class="nam">default_safety_margin_percent</span><span class="op">:</span> <span class="nam">Mapped</span><span class="op">[</span><span class="nam">float</span><span class="op">]</span> <span class="op">=</span> <span class="nam">mapped_column</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs71" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t72" href="#t72">72</a></span><span class="t">        <span class="nam">default</span><span class="op">=</span><span class="num">0.0</span><span class="op">,</span> <span class="nam">nullable</span><span class="op">=</span><span class="key">False</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t73" href="#t73">73</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t74" href="#t74">74</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t75" href="#t75">75</a></span><span class="t">    <span class="nam">preferred_cable_manufacturers_json</span><span class="op">:</span> <span class="nam">Mapped</span><span class="op">[</span><span class="nam">str</span> <span class="op">|</span> <span class="key">None</span><span class="op">]</span> <span class="op">=</span> <span class="nam">mapped_column</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs75" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t76" href="#t76">76</a></span><span class="t">        <span class="nam">FlexibleJSON</span><span class="op">,</span> <span class="nam">nullable</span><span class="op">=</span><span class="key">True</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t77" href="#t77">77</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t78" href="#t78">78</a></span><span class="t">    <span class="nam">preferred_control_device_manufacturers_json</span><span class="op">:</span> <span class="nam">Mapped</span><span class="op">[</span><span class="nam">str</span> <span class="op">|</span> <span class="key">None</span><span class="op">]</span> <span class="op">=</span> <span class="nam">mapped_column</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs78" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t79" href="#t79">79</a></span><span class="t">        <span class="nam">FlexibleJSON</span><span class="op">,</span> <span class="nam">nullable</span><span class="op">=</span><span class="key">True</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t80" href="#t80">80</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t81" href="#t81">81</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t82" href="#t82">82</a></span><span class="t">    <span class="com"># Relationships</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t83" href="#t83">83</a></span><span class="t">    <span class="nam">user</span><span class="op">:</span> <span class="nam">Mapped</span><span class="op">[</span><span class="str">"User"</span><span class="op">]</span> <span class="op">=</span> <span class="nam">relationship</span><span class="op">(</span>&nbsp;</span><span class="r"><label for="ctxs83" class="ctx">(empty)</label></span></p>
    <p class="pln"><span class="n"><a id="t84" href="#t84">84</a></span><span class="t">        <span class="nam">back_populates</span><span class="op">=</span><span class="str">"preferences"</span><span class="op">,</span> <span class="nam">foreign_keys</span><span class="op">=</span><span class="op">[</span><span class="nam">user_id</span><span class="op">]</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t85" href="#t85">85</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t86" href="#t86">86</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="exc show_exc"><span class="n"><a id="t87" href="#t87">87</a></span><span class="t">    <span class="key">def</span> <span class="nam">__repr__</span><span class="op">(</span><span class="nam">self</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"><label for="ctxs87" class="ctx">(empty)</label></span></p>
    <p class="exc show_exc"><span class="n"><a id="t88" href="#t88">88</a></span><span class="t">        <span class="key">return</span> <span class="fst">f"</span><span class="fst">&lt;UserPreference(id=</span><span class="op">{</span><span class="nam">self</span><span class="op">.</span><span class="nam">id</span><span class="op">}</span><span class="fst">, user_id=</span><span class="op">{</span><span class="nam">self</span><span class="op">.</span><span class="nam">user_id</span><span class="op">}</span><span class="fst">, ui_theme='</span><span class="op">{</span><span class="nam">self</span><span class="op">.</span><span class="nam">ui_theme</span><span class="op">}</span><span class="fst">')></span><span class="fst">"</span>&nbsp;</span><span class="r"></span></p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="z_f2bf686f1e66ce65_switchboard_py.html">&#xab; prev</a> &nbsp; &nbsp;
            <a class="nav" href="index.html">&Hat; index</a> &nbsp; &nbsp;
            <a class="nav" href="z_a6617d05fb563bba___init___py.html">&#xbb; next</a>
            &nbsp; &nbsp; &nbsp;
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.0">coverage.py v7.8.0</a>,
            created at 2025-06-03 23:24 +0300
        </p>
    </div>
</footer>
</body>
</html>
