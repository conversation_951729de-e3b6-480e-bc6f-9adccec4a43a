# backend/main.py

import logging
import os

# Ensure absolute imports work correctly from the project root
# This is crucial when running 'python main.py' from the project root
# If you run it from 'backend/' directly, you might need to adjust or rely on IDE settings.
import sys

import typer
import uvicorn

sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))

from app import app
from config.logging_config import setup_logging
from config.settings import Settings, settings
# from core.services.seed_service import seed_initial_data # Commented out for now

# Initialize Typer application
cli_app = typer.Typer(
    name="heat-tracing-app",
    help="A CLI for managing the Heat Tracing Design Application backend.",
    pretty_exceptions_show_locals=False,  # Hide locals for cleaner exception messages
)

# Set up logging early in the entry point
setup_logging()
logger = logging.getLogger(__name__)


def get_alembic_config() -> str:
    """
    Returns the absolute path to the alembic.ini file.
    """
    current_dir = os.path.dirname(__file__)
    project_root = os.path.abspath(os.path.join(current_dir, ".."))
    alembic_cfg_path = os.path.join(project_root, "alembic.ini")
    return alembic_cfg_path


@cli_app.command()
def run(
    host: str = typer.Option("0.0.0.0", help="Host address to bind the server to."),
    port: int = typer.Option(settings.APP_PORT, help="Port to run the server on."),
    reload: bool = typer.Option(
        settings.DEBUG, help="Enable auto-reload for development."
    ),
):
    """
    Runs the FastAPI web server.
    """
    logger.info(f"Starting FastAPI server on http://{host}:{port}")
    logger.info(f"Application environment: {settings.ENVIRONMENT}")
    logger.info(f"Debug mode: {settings.DEBUG}")

    uvicorn.run(
        app,
        host=host,
        port=port,
        reload=reload,
        log_level=settings.LOG_LEVEL.lower(),  # Use log_level from settings
    )


@cli_app.command()
def migrate():
    """
    Runs database migrations using Alembic.
    """
    logger.info("Starting database migrations...")
    original_dir = os.getcwd()
    try:
        # Change to the backend directory to ensure alembic.ini is found
        os.chdir("backend")
        logger.debug(f"Changed current directory to: {os.getcwd()}")

        from alembic import command
        from alembic.config import Config

        # Alembic will now find alembic.ini in the current directory
        alembic_cfg = Config("alembic.ini")
        # Use upgrade to apply migrations
        command.upgrade(alembic_cfg, "head")
        logger.info("Database migrations completed successfully.")

    except Exception as e:
        logger.error(f"Error during database migration: {e}", exc_info=True)
        typer.echo(f"Migration failed: {e}", err=True)
        raise typer.Exit(code=1)
    finally:
        # Change back to the original directory
        os.chdir(original_dir)
        logger.debug(f"Changed back to original directory: {os.getcwd()}")

    logger.info(
        "Database migration command finished (check logs for actual Alembic output)."
    )


# @cli_app.command("seed-data") # Commented out for now
# def seed_data_command():
#     """
#     Populates the database with initial seed data.
#     """
#     logger.info("Starting database seeding...")
#     try:
#         seed_initial_data()
#         logger.info("Database seeding completed successfully (placeholder).")
#         typer.echo("Database seeding initiated. Check logs for details.")
#     except Exception as e:
#         logger.error(f"Error during database seeding: {e}", exc_info=True)
#         typer.echo(f"Seeding failed: {e}", err=True)
#         raise typer.Exit(code=1)


# @cli_app.command("create-superuser") # Commented out for now
# def create_superuser_command(
#     username: str = typer.Argument(..., help="Username for the superuser."),
#     password: str = typer.Argument(..., help="Password for the superuser."),
#     email: str = typer.Argument(..., help="Email for the superuser."),
# ):
#     """
#     Creates a new superuser in the database.
#     """
#     logger.info(f"Attempting to create superuser: {username}")
#     try:
#         from core.database.session import (
#             get_db_session,  # Use the standard session context manager
#         )
#         from core.repositories.user_repository import UserRepository
#         from core.services.user_service import UserService

#         with get_db_session() as db_session:
#             user_repo = UserRepository(db_session)
#             user_service = UserService(user_repo)
#             superuser = user_service.create_superuser(
#                 username=username, password=password, email=email
#             )
#         typer.echo(f"Superuser '{superuser.username}' created successfully!")
#     except Exception as e:
#         logger.error(f"Error creating superuser: {e}", exc_info=True)
#         typer.echo(f"Failed to create superuser: {e}", err=True)
#         raise typer.Exit(code=1)


# This block makes the Typer app runnable when the script is executed
if __name__ == "__main__":
    cli_app()

# Instantiate the settings object to make it globally available
settings = Settings()  # type: ignore
