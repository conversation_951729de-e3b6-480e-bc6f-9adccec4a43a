[tool:pytest]
# Pytest configuration for Ultimate Electrical Designer Backend

# Test discovery
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*

# Output options
addopts =
    --verbose
    --tb=short
    --strict-markers
    --disable-warnings
    --cov=backend/core
    --cov=backend/api
    --cov-report=term-missing
    --cov-report=html:htmlcov
    --cov-fail-under=85

# Markers
markers =
    # Test Categories
    unit: Unit tests for individual components
    integration: Integration tests for component interactions
    api: API endpoint tests
    database: Database-related tests
    security: Security and authentication tests
    performance: Performance and load tests
    smoke: Quick smoke tests for basic functionality
    regression: Regression tests for bug fixes

    # Component-specific markers
    project: Project entity tests
    component: Component entity tests
    heat_tracing: Heat tracing entity tests
    electrical: Electrical entity tests
    switchboard: Switchboard entity tests
    user: User entity tests
    document: Document entity tests
    activity_log: Activity log tests

    # Infrastructure markers
    calculations: Calculation engine tests
    standards: Standards compliance tests
    repository: Repository layer tests
    service: Service layer tests
    schema: Schema validation tests

    # Test execution markers
    slow: Tests that take more than 5 seconds
    fast: Tests that complete in under 1 second
    external: Tests requiring external services
    mock: Tests using mocked dependencies

    # Additional markers
    import_export: Tests for import/export functionality
    reports: Tests for report generation

# Minimum version
minversion = 6.0

# Test session configuration
log_cli = true
log_cli_level = INFO
log_cli_format = %(asctime)s [%(levelname)8s] %(name)s: %(message)s
log_cli_date_format = %Y-%m-%d %H:%M:%S

# Warnings
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore::UserWarning:sqlalchemy.*
    ignore::UserWarning:pydantic.*

# Test timeout (in seconds)
timeout = 300

# Asyncio configuration
asyncio_mode = auto

# Test collection
collect_ignore =
    setup.py
    migrations/
    alembic/
    venv/
    .venv/
    node_modules/
