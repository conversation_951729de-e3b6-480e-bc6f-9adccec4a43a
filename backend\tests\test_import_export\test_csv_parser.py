# backend/tests/test_import_export/test_csv_parser.py
"""
Tests for CSV Parser.

Tests the CSV file parsing functionality including
data validation and error handling.
"""

import pytest
import tempfile
import os
from unittest.mock import Mock, patch

from core.data_import.parsers.csv_parser import CSVParser
from core.errors.exceptions import InvalidInputError, CalculationError
from tests.fixtures.test_data import (
    SAMPLE_CSV_DATA,
    get_sample_csv_file,
    get_invalid_csv_file,
    cleanup_temp_file,
)


class TestCSVParser:
    """Test suite for CSVParser."""

    def setup_method(self):
        """Set up test fixtures."""
        self.parser = CSVParser()

    def test_initialization(self):
        """Test parser initialization."""
        assert self.parser is not None
        assert hasattr(self.parser, "parse_file")
        assert hasattr(self.parser, "validate_structure")

    def test_parse_valid_csv_file(self):
        """Test parsing a valid CSV file."""
        csv_file = get_sample_csv_file()

        try:
            result = self.parser.parse_file(csv_file)

            assert "success" in result
            assert result["success"] is True
            assert "data" in result
            assert "metadata" in result
            assert len(result["data"]) == 3  # 3 data rows

            # Check first row data
            first_row = result["data"][0]
            assert first_row["Circuit ID"] == "HT-001"
            assert first_row["Circuit Name"] == "Main Process Line"
            assert float(first_row["Pipe Diameter (m)"]) == 0.1

        finally:
            cleanup_temp_file(csv_file)

    def test_parse_csv_with_custom_delimiter(self):
        """Test parsing CSV with custom delimiter."""
        # Create CSV with semicolon delimiter
        csv_content = "Circuit ID;Circuit Name;Power (W)\nHT-001;Main Line;2500\nHT-002;Secondary Line;1500"

        temp_file = tempfile.NamedTemporaryFile(mode="w", suffix=".csv", delete=False)
        temp_file.write(csv_content)
        temp_file.close()

        try:
            result = self.parser.parse_file(temp_file.name, delimiter=";")

            assert result["success"] is True
            assert len(result["data"]) == 2
            assert result["data"][0]["Circuit ID"] == "HT-001"

        finally:
            cleanup_temp_file(temp_file.name)

    def test_parse_csv_with_missing_headers(self):
        """Test parsing CSV with missing headers."""
        csv_content = "HT-001,Main Line,0.1\nHT-002,Secondary Line,0.05"

        temp_file = tempfile.NamedTemporaryFile(mode="w", suffix=".csv", delete=False)
        temp_file.write(csv_content)
        temp_file.close()

        try:
            # Parse normally - the CSV parser will handle it
            result = self.parser.parse_file(temp_file.name)

            assert "data" in result
            assert len(result["data"]) == 2
            # Check that data was parsed correctly
            assert len(result["columns"]) >= 2

        finally:
            cleanup_temp_file(temp_file.name)

    def test_parse_csv_with_empty_rows(self):
        """Test parsing CSV with empty rows."""
        csv_content = """Circuit ID,Circuit Name,Power (W)
HT-001,Main Line,2500

HT-002,Secondary Line,1500
,,,
HT-003,Third Line,3000"""

        temp_file = tempfile.NamedTemporaryFile(mode="w", suffix=".csv", delete=False)
        temp_file.write(csv_content)
        temp_file.close()

        try:
            # The CSV parser automatically removes empty rows
            result = self.parser.parse_file(temp_file.name)

            assert "data" in result
            assert (
                len(result["data"]) == 3
            )  # Empty rows should be skipped automatically

        finally:
            cleanup_temp_file(temp_file.name)

    def test_parse_csv_with_data_type_conversion(self):
        """Test parsing CSV with automatic data type conversion."""
        csv_content = """Circuit ID,Power (W),Length (m),Active
HT-001,2500.5,100.0,true
HT-002,1500,50,false"""

        temp_file = tempfile.NamedTemporaryFile(mode="w", suffix=".csv", delete=False)
        temp_file.write(csv_content)
        temp_file.close()

        try:
            # Parse first, then convert types separately
            result = self.parser.parse_file(temp_file.name)

            type_mapping = {
                "Power (W)": "float",
                "Length (m)": "float",
                "Active": "bool",
            }

            converted_data = self.parser.convert_data_types(
                result["data"], type_mapping
            )
            first_row = converted_data[0]

            # Check type conversions
            assert isinstance(first_row["Power (W)"], float)
            assert isinstance(first_row["Length (m)"], float)
            assert isinstance(first_row["Active"], bool)

        finally:
            cleanup_temp_file(temp_file.name)

    def test_parse_nonexistent_file(self):
        """Test parsing a nonexistent file."""
        with pytest.raises(InvalidInputError, match="File not found"):
            self.parser.parse_file("nonexistent_file.csv")

    def test_parse_invalid_csv_file(self):
        """Test parsing an invalid CSV file."""
        invalid_csv = get_invalid_csv_file()

        try:
            with pytest.raises((InvalidInputError, CalculationError)):
                self.parser.parse_file(invalid_csv)

        finally:
            cleanup_temp_file(invalid_csv)

    def test_parse_empty_csv_file(self):
        """Test parsing an empty CSV file."""
        temp_file = tempfile.NamedTemporaryFile(mode="w", suffix=".csv", delete=False)
        temp_file.write("")
        temp_file.close()

        try:
            # Empty file should return empty data, not raise an error
            result = self.parser.parse_file(temp_file.name)
            assert "data" in result
            assert len(result["data"]) == 0

        finally:
            cleanup_temp_file(temp_file.name)

    def test_validate_data_types(self):
        """Test data type validation."""
        # Valid data
        valid_data = [
            {"Circuit ID": "HT-001", "Power": "2500"},
            {"Circuit ID": "HT-002", "Power": "1500"},
        ]

        type_mapping = {"Circuit ID": "str", "Power": "int"}
        result = self.parser.validate_data_types(valid_data, type_mapping)

        assert result["is_valid"] is True
        assert len(result["errors"]) == 0

    def test_validate_data_types_invalid(self):
        """Test data type validation with invalid data."""
        invalid_data = [
            {"Circuit ID": "HT-001", "Power": "invalid_number"},
            {"Circuit ID": "HT-002", "Power": "1500"},
        ]

        type_mapping = {"Circuit ID": "str", "Power": "int"}
        result = self.parser.validate_data_types(invalid_data, type_mapping)

        assert result["is_valid"] is False
        assert len(result["errors"]) > 0

    def test_parse_csv_with_encoding_issues(self):
        """Test parsing CSV with different encodings."""
        # Create CSV with UTF-8 BOM
        csv_content = "\ufeffCircuit ID,Circuit Name\nHT-001,Main Line"

        temp_file = tempfile.NamedTemporaryFile(
            mode="w", suffix=".csv", delete=False, encoding="utf-8-sig"
        )
        temp_file.write(csv_content)
        temp_file.close()

        try:
            result = self.parser.parse_file(temp_file.name, encoding="utf-8-sig")

            assert "data" in result
            assert result["data"][0]["Circuit ID"] == "HT-001"

        finally:
            cleanup_temp_file(temp_file.name)

    def test_parse_large_csv_file(self):
        """Test parsing a large CSV file."""
        # Create large CSV file
        csv_content = "Circuit ID,Power (W)\n"
        for i in range(10000):
            csv_content += f"HT-{i:05d},{1000 + i}\n"

        temp_file = tempfile.NamedTemporaryFile(mode="w", suffix=".csv", delete=False)
        temp_file.write(csv_content)
        temp_file.close()

        try:
            import time

            start_time = time.time()

            result = self.parser.parse_file(temp_file.name)

            end_time = time.time()
            parse_time = end_time - start_time

            assert "data" in result
            assert len(result["data"]) == 10000
            # Should parse reasonably quickly
            assert parse_time < 10.0  # 10 seconds max

        finally:
            cleanup_temp_file(temp_file.name)

    def test_parse_csv_with_special_characters(self):
        """Test parsing CSV with special characters."""
        csv_content = (
            "Circuit ID,Description,Notes\n"
            'HT-001,"Line with, comma","Notes with ""quotes"""\n'
            'HT-002,Line with newline,"Notes with newline"\n'
            "HT-003,Line with semicolon;,\"Notes with 'apostrophe'\""
        )

        temp_file = tempfile.NamedTemporaryFile(mode="w", suffix=".csv", delete=False)
        temp_file.write(csv_content)
        temp_file.close()

        try:
            result = self.parser.parse_file(temp_file.name)

            # Check that parsing was successful
            assert "data" in result
            assert len(result["data"]) == 3

            # Check special character handling
            assert "comma" in result["data"][0]["Description"]
            assert '"quotes"' in result["data"][0]["Notes"]

        finally:
            cleanup_temp_file(temp_file.name)

    def test_convert_data_types(self):
        """Test data type conversion functionality."""
        csv_content = """Circuit ID,Power (W),Voltage (V)
HT-001,2500,240
HT-002,1500,240
HT-003,3000,480"""

        temp_file = tempfile.NamedTemporaryFile(mode="w", suffix=".csv", delete=False)
        temp_file.write(csv_content)
        temp_file.close()

        type_mapping = {
            "Power (W)": "int",
            "Voltage (V)": "int",
        }

        try:
            result = self.parser.parse_file(temp_file.name)
            converted_data = self.parser.convert_data_types(
                result["data"], type_mapping
            )

            assert len(converted_data) == 3
            assert isinstance(converted_data[0]["Power (W)"], int)
            assert isinstance(converted_data[0]["Voltage (V)"], int)

        finally:
            cleanup_temp_file(temp_file.name)

    def test_get_file_info(self):
        """Test getting CSV file information."""
        csv_file = get_sample_csv_file()

        try:
            file_info = self.parser.get_file_info(csv_file)

            assert "file_size" in file_info
            assert "estimated_row_count" in file_info
            assert "column_count" in file_info
            assert "encoding" in file_info
            assert file_info["estimated_row_count"] >= 3  # At least 3 data rows

        finally:
            cleanup_temp_file(csv_file)

    @patch("core.data_import.parsers.csv_parser.logger")
    def test_logging_functionality(self, mock_logger):
        """Test that appropriate logging occurs during parsing."""
        csv_file = get_sample_csv_file()

        try:
            self.parser.parse_file(csv_file)

            # Verify logging calls were made
            assert mock_logger.info.called
            assert mock_logger.debug.called

        finally:
            cleanup_temp_file(csv_file)
