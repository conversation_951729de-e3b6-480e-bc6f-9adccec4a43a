# backend/tests/test_import_export/test_import_service.py
"""
Tests for Import Service.

Tests the main import service that orchestrates all import operations.
"""

import pytest
from unittest.mock import Mock

from core.data_import.import_service import ImportService
from core.errors.exceptions import CalculationError
from tests.fixtures.test_data import (
    get_sample_csv_file,
    cleanup_temp_file,
)


class TestImportService:
    """Test suite for ImportService."""

    def setup_method(self):
        """Set up test fixtures."""
        self.service = ImportService()

    def test_initialization(self):
        """Test service initialization."""
        assert self.service is not None
        assert hasattr(self.service, "import_project_data")
        assert hasattr(self.service, "import_global_data")

    def test_import_project_data_basic(self):
        """Test basic project data import functionality."""
        csv_file = get_sample_csv_file()

        try:
            result = self.service.import_project_data(
                project_id="test_project_001",
                file_path=csv_file,
                data_type="heat_tracing_circuits",
            )

            assert "success" in result
            assert result["success"] is True
            # The service returns 'data' instead of 'imported_data'
            assert "data" in result or "imported_data" in result

        finally:
            cleanup_temp_file(csv_file)

    def test_import_global_data_basic(self):
        """Test basic global data import functionality."""
        csv_file = get_sample_csv_file()

        try:
            result = self.service.import_global_data(
                file_path=csv_file,
                catalog_type="cables",
            )

            assert "success" in result
            assert result["success"] is True

        finally:
            cleanup_temp_file(csv_file)

    def test_import_history_functionality(self):
        """Test import history tracking."""
        # Test that import history is initialized
        history = self.service.get_import_history()
        assert isinstance(history, list)

    def test_generate_import_template(self):
        """Test template generation functionality."""
        # Test template generation for project data
        template_path = self.service.generate_import_template(
            template_type="project",
            data_type="heat_tracing_circuits",
            project_id="test_project_001",
        )
        assert template_path is not None
        assert isinstance(template_path, str)

    def test_error_handling_invalid_data_type(self):
        """Test error handling for invalid data types."""
        csv_file = get_sample_csv_file()

        try:
            # The service wraps InvalidInputError in CalculationError
            with pytest.raises(CalculationError):
                self.service.import_project_data(
                    project_id="test_project_001",
                    file_path=csv_file,
                    data_type="invalid_data_type",
                )
        finally:
            cleanup_temp_file(csv_file)

    def test_cleanup_temp_files(self):
        """Test temporary file cleanup functionality."""
        # Test cleanup method exists and returns count
        cleanup_count = self.service.cleanup_temp_files(max_age_hours=24)
        assert isinstance(cleanup_count, int)
        assert cleanup_count >= 0

    def test_get_import_status(self):
        """Test import status tracking."""
        # Test with non-existent transaction ID
        status = self.service.get_import_status("non_existent_id")
        assert status is None

    def test_service_methods_exist(self):
        """Test that required service methods exist."""
        # Test that the service has the expected methods
        assert hasattr(self.service, "import_project_data")
        assert hasattr(self.service, "import_global_data")
        assert hasattr(self.service, "generate_import_template")
        assert hasattr(self.service, "get_import_history")
        assert hasattr(self.service, "get_import_status")
        assert hasattr(self.service, "cleanup_temp_files")
