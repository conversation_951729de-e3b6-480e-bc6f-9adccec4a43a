# backend/tests/test_integration/test_end_to_end_workflow.py
"""
End-to-End Workflow Integration Tests.

Tests complete workflows that span multiple phases and services.
"""

import pytest
import tempfile
import os
from unittest.mock import Mock, patch

from core.calculations.calculation_service import CalculationService
from core.data_import.import_service import ImportService
from core.reports.report_service import ReportService
from core.standards.standards_service import StandardsService
from tests.fixtures.test_data import (
    SAMPLE_HEAT_TRACING_DATA,
    SAMPLE_STANDARDS_DATA,
    get_sample_csv_file,
    cleanup_temp_file,
)


class TestEndToEndWorkflow:
    """Test suite for end-to-end workflows."""

    def setup_method(self):
        """Set up test fixtures."""
        self.calculation_service = CalculationService()
        self.import_service = ImportService()
        self.report_service = ReportService()
        self.standards_service = StandardsService()

    @pytest.mark.asyncio
    async def test_complete_design_workflow(self):
        """Test complete design workflow from import to report generation."""
        # Step 1: Import project data
        csv_file = get_sample_csv_file()

        try:
            import_result = self.import_service.import_project_data(
                project_id="test_project_001",
                file_path=csv_file,
                data_type="heat_tracing_circuits",
            )

            assert import_result["success"] is True
            imported_circuits = import_result["imported_data"]["circuits"]

            # Step 2: Perform calculations
            calculation_result = (
                await self.calculation_service.calculate_heat_tracing_system(
                    circuits=imported_circuits, design_parameters={"safety_factor": 1.3}
                )
            )

            assert calculation_result["success"] is True
            circuit_results = calculation_result["circuit_results"]

            # Step 3: Validate against standards
            design_data = {
                "voltage": 240,
                "power_density": 25.0,
                "maintain_temperature": 60.0,
                "ambient_temperature": 0.0,
                "pipe_diameter": 0.1,
                "insulation_type": "mineral_wool",
                "application_type": "freeze_protection",
            }

            validation_result = (
                self.standards_service.validate_design_against_standards(
                    design_data=design_data, standard_ids=["IEEE-515-2017"]
                )
            )

            assert (
                validation_result["validation_summary"]["total_standards_checked"] > 0
            )

            # Step 4: Generate report
            report_result = self.report_service.generate_calculation_report(
                project_id="test_workflow_001",
                project_data={"project_name": "Test Workflow Project"},
                circuits_data=imported_circuits,
                calculations_data=circuit_results,
                design_parameters={"safety_factor": 1.3},
                output_format="html",
            )

            assert report_result["success"] is True
            assert os.path.exists(report_result["file_path"])

        finally:
            cleanup_temp_file(csv_file)

    @pytest.mark.asyncio
    async def test_import_calculate_export_workflow(self):
        """Test workflow: import data -> calculate -> export results."""
        # Step 1: Import data
        csv_file = get_sample_csv_file()

        try:
            import_result = self.import_service.import_project_data(
                project_id="test_project_002",
                file_path=csv_file,
                data_type="heat_tracing_circuits",
            )

            imported_circuits = import_result["imported_data"]["circuits"]

            # Step 2: Perform calculations
            calculation_result = (
                await self.calculation_service.calculate_heat_tracing_system(
                    circuits=imported_circuits, design_parameters={"safety_factor": 1.3}
                )
            )

            circuit_results = calculation_result["circuit_results"]

            # Step 3: Export results
            export_data = {
                "circuits": imported_circuits,
                "calculations": circuit_results,
            }

            export_result = self.report_service.export_data(
                project_id="test_export_001",
                data=export_data,
                export_type="calculation_report",
                format_type="json",
            )

            assert export_result["success"] is True
            assert os.path.exists(export_result["file_path"])

        finally:
            cleanup_temp_file(csv_file)

    @pytest.mark.asyncio
    async def test_standards_validation_calculation_workflow(self):
        """Test workflow: standards validation -> parameter calculation -> compliance report."""
        design_data = SAMPLE_STANDARDS_DATA["design_data"]
        input_data = SAMPLE_STANDARDS_DATA["input_data"]

        # Step 1: Validate design against standards
        validation_result = self.standards_service.validate_design_against_standards(
            design_data=design_data, standard_ids=["IEEE-515-2017", "IEC-62395"]
        )

        assert validation_result["validation_summary"]["total_standards_checked"] == 2

        # Step 2: Calculate parameters using standards
        calculation_result = self.standards_service.calculate_standards_parameters(
            input_data=input_data, standard_ids=["IEEE-515-2017", "IEC-62395"]
        )

        assert calculation_result["calculation_summary"]["total_standards_used"] == 2

        # Step 3: Generate compliance report
        compliance_data = {
            "validation_results": validation_result["individual_results"],
            "calculation_results": calculation_result["individual_results"],
            "design_data": design_data,
        }

        # This would be implemented in the report service
        # For now, just verify the data structure
        assert "validation_results" in compliance_data
        assert "calculation_results" in compliance_data

    @pytest.mark.asyncio
    async def test_multi_format_report_generation_workflow(self):
        """Test workflow: calculate -> generate multiple report formats."""
        # Step 1: Perform calculations
        calculation_result = (
            await self.calculation_service.calculate_heat_tracing_system(
                circuits=SAMPLE_HEAT_TRACING_DATA["circuits"],
                design_parameters=SAMPLE_HEAT_TRACING_DATA["design_parameters"],
            )
        )

        circuit_results = calculation_result["circuit_results"]

        # Step 2: Generate multiple report formats
        formats = ["pdf", "html", "excel"]
        generated_reports = {}

        for format_type in formats:
            report_result = self.report_service.generate_calculation_report(
                project_id=f"test_multi_format_{format_type}",
                project_data={"project_name": "Multi-Format Test"},
                circuits_data=SAMPLE_HEAT_TRACING_DATA["circuits"],
                calculations_data=circuit_results,
                design_parameters=SAMPLE_HEAT_TRACING_DATA["design_parameters"],
                output_format=format_type,
            )

            assert report_result["success"] is True
            generated_reports[format_type] = report_result["file_path"]

        # Verify all formats were generated
        assert len(generated_reports) == len(formats)
        for file_path in generated_reports.values():
            assert os.path.exists(file_path)

    @pytest.mark.asyncio
    async def test_error_recovery_workflow(self):
        """Test workflow with error recovery across services."""
        # Step 1: Import with some invalid data
        invalid_csv_content = """Circuit ID,Power (W),Voltage (V)
HT-001,2500,240
HT-002,invalid_power,240
HT-003,3000,480"""

        temp_file = tempfile.NamedTemporaryFile(mode="w", suffix=".csv", delete=False)
        temp_file.write(invalid_csv_content)
        temp_file.close()

        try:
            import_result = self.import_service.import_project_data(
                project_id="test_project_003",
                file_path=temp_file.name,
                data_type="heat_tracing_circuits",
            )

            # Should have imported valid records despite errors
            assert "imported_data" in import_result
            assert "import_errors" in import_result

            # Step 2: Continue with valid data
            if import_result["imported_data"]["circuits"]:
                calculation_result = (
                    await self.calculation_service.calculate_heat_tracing_system(
                        circuits=import_result["imported_data"]["circuits"],
                        design_parameters={"safety_factor": 1.3},
                        calculation_options={"continue_on_error": True},
                    )
                )

                # Should handle partial calculations
                assert "circuit_results" in calculation_result

        finally:
            cleanup_temp_file(temp_file.name)

    @pytest.mark.asyncio
    async def test_performance_large_dataset_workflow(self):
        """Test workflow performance with large dataset."""
        # Create large dataset
        large_circuits = []
        for i in range(50):  # Reduced from 1000 for test performance
            circuit = SAMPLE_HEAT_TRACING_DATA["circuits"][0].copy()
            circuit["circuit_id"] = f"HT-{i:03d}"
            large_circuits.append(circuit)

        import time

        start_time = time.time()

        # Step 1: Calculate large dataset
        calculation_result = (
            await self.calculation_service.calculate_heat_tracing_system(
                circuits=large_circuits,
                design_parameters=SAMPLE_HEAT_TRACING_DATA["design_parameters"],
            )
        )

        # Step 2: Generate report
        report_result = self.report_service.generate_calculation_report(
            project_id="test_performance_large",
            project_data={"project_name": "Large Dataset Test"},
            circuits_data=large_circuits,
            calculations_data=calculation_result["circuit_results"],
            design_parameters=SAMPLE_HEAT_TRACING_DATA["design_parameters"],
            output_format="html",
        )

        end_time = time.time()
        total_time = end_time - start_time

        assert calculation_result["success"] is True
        assert report_result["success"] is True
        assert len(calculation_result["circuit_results"]) == 50
        # Should complete reasonably quickly
        assert total_time < 60.0  # 60 seconds max

    @pytest.mark.asyncio
    async def test_caching_across_services_workflow(self):
        """Test caching behavior across different services."""
        # Step 1: Perform calculation (should cache results)
        calculation_result1 = (
            await self.calculation_service.calculate_heat_tracing_system(
                circuits=SAMPLE_HEAT_TRACING_DATA["circuits"],
                design_parameters=SAMPLE_HEAT_TRACING_DATA["design_parameters"],
            )
        )

        # Step 2: Perform same calculation (should use cache)
        calculation_result2 = (
            await self.calculation_service.calculate_heat_tracing_system(
                circuits=SAMPLE_HEAT_TRACING_DATA["circuits"],
                design_parameters=SAMPLE_HEAT_TRACING_DATA["design_parameters"],
            )
        )

        # Results should be identical
        assert (
            calculation_result1["circuit_results"]
            == calculation_result2["circuit_results"]
        )

        # Step 3: Generate report (should cache report)
        report_result1 = self.report_service.generate_calculation_report(
            project_id="test_cache_001",
            project_data={"project_name": "Cache Test"},
            circuits_data=SAMPLE_HEAT_TRACING_DATA["circuits"],
            calculations_data=calculation_result1["circuit_results"],
            design_parameters=SAMPLE_HEAT_TRACING_DATA["design_parameters"],
            output_format="html",
            use_cache=True,
        )

        # Step 4: Generate same report (should use cache)
        report_result2 = self.report_service.generate_calculation_report(
            project_id="test_cache_001",
            project_data={"project_name": "Cache Test"},
            circuits_data=SAMPLE_HEAT_TRACING_DATA["circuits"],
            calculations_data=calculation_result1["circuit_results"],
            design_parameters=SAMPLE_HEAT_TRACING_DATA["design_parameters"],
            output_format="html",
            use_cache=True,
        )

        assert report_result1["success"] is True
        assert report_result2["success"] is True

    @pytest.mark.asyncio
    async def test_data_consistency_workflow(self):
        """Test data consistency across service boundaries."""
        # Step 1: Import data
        csv_file = get_sample_csv_file()

        try:
            import_result = self.import_service.import_project_data(
                project_id="test_project_004",
                file_path=csv_file,
                data_type="heat_tracing_circuits",
            )

            original_circuits = import_result["imported_data"]["circuits"]

            # Step 2: Perform calculations
            calculation_result = (
                await self.calculation_service.calculate_heat_tracing_system(
                    circuits=original_circuits, design_parameters={"safety_factor": 1.3}
                )
            )

            calculated_circuits = calculation_result["circuit_results"]

            # Step 3: Verify data consistency
            assert len(original_circuits) == len(calculated_circuits)

            for i, (original, calculated) in enumerate(
                zip(original_circuits, calculated_circuits)
            ):
                # Circuit IDs should match
                assert original.get("Circuit ID") == calculated.get("circuit_id")

                # Original data should be preserved in calculations
                if "Pipe Diameter (m)" in original:
                    assert float(original["Pipe Diameter (m)"]) == calculated.get(
                        "pipe_diameter", 0
                    )

        finally:
            cleanup_temp_file(csv_file)

    @pytest.mark.asyncio
    async def test_service_integration_error_handling(self):
        """Test error handling across service integrations."""
        # Test calculation service with invalid data
        invalid_circuits = [
            {
                "circuit_id": "INVALID",
                "pipe_diameter": -0.1,  # Invalid
                "pipe_length": 0,  # Invalid
            }
        ]

        # Should handle errors gracefully
        calculation_result = (
            await self.calculation_service.calculate_heat_tracing_system(
                circuits=invalid_circuits,
                design_parameters={"safety_factor": 1.3},
                calculation_options={"continue_on_error": True},
            )
        )

        # Should have error information
        assert (
            "calculation_errors" in calculation_result
            or not calculation_result["success"]
        )

        # Test standards service with invalid data
        invalid_design_data = {}

        try:
            validation_result = (
                self.standards_service.validate_design_against_standards(
                    design_data=invalid_design_data, standard_ids=["IEEE-515-2017"]
                )
            )
            # Should either succeed with errors or raise exception
            assert "validation_summary" in validation_result
        except Exception:
            # Exception is acceptable for invalid input
            pass
