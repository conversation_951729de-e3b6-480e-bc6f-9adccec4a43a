# backend/tests/test_schemas/test_document_schemas.py
"""
Unit tests for Document schemas.

Tests validation, serialization, and deserialization of Document-related
Pydantic schemas.
"""

import pytest
from pydantic import ValidationError

# Mark all tests in this file
pytestmark = [pytest.mark.unit, pytest.mark.schema, pytest.mark.document]

from core.schemas.document_schemas import (
    ImportedDataRevisionCreateSchema,
    ImportedDataRevisionUpdateSchema,
    ImportedDataRevisionReadSchema,
    ExportedDocumentCreateSchema,
    ExportedDocumentUpdateSchema,
    ExportedDocumentReadSchema,
    CalculationStandardCreateSchema,
    CalculationStandardUpdateSchema,
    CalculationStandardReadSchema,
    ImportType,
    DocumentType,
    FileFormat,
)


class TestImportedDataRevisionSchemas:
    """Test ImportedDataRevision schema validation and functionality."""

    def test_valid_imported_data_revision_creation(self):
        """Test creating a valid imported data revision schema."""
        data = {
            "project_id": 1,
            "imported_by_user_id": 1,
            "source_filename": "pipe_data_v2.xlsx",
            "revision_identifier": "REV-002",
            "import_type": "pipe_data",
            "is_active_revision": True,
        }
        schema = ImportedDataRevisionCreateSchema(**data)

        assert schema.project_id == 1
        assert schema.imported_by_user_id == 1
        assert schema.source_filename == "pipe_data_v2.xlsx"
        assert schema.revision_identifier == "REV-002"
        assert schema.import_type == ImportType.PIPE_DATA
        assert schema.is_active_revision is True

    def test_filename_validation_empty(self):
        """Test validation fails for empty filename."""
        data = {
            "project_id": 1,
            "source_filename": "",
            "import_type": "pipe_data",
        }
        with pytest.raises(ValidationError) as exc_info:
            ImportedDataRevisionCreateSchema(**data)

        assert "String should have at least 1 character" in str(exc_info.value)

    def test_filename_validation_invalid_extension(self):
        """Test validation fails for invalid file extension."""
        data = {
            "project_id": 1,
            "source_filename": "data.txt",
            "import_type": "pipe_data",
        }
        with pytest.raises(ValidationError) as exc_info:
            ImportedDataRevisionCreateSchema(**data)

        assert "File must have one of these extensions" in str(exc_info.value)

    def test_filename_validation_valid_extensions(self):
        """Test validation passes for valid file extensions."""
        valid_extensions = [".xlsx", ".xls", ".csv", ".json"]

        for ext in valid_extensions:
            data = {
                "project_id": 1,
                "source_filename": f"data{ext}",
                "import_type": "pipe_data",
            }
            schema = ImportedDataRevisionCreateSchema(**data)
            assert schema.source_filename == f"data{ext}"

    def test_revision_identifier_validation_invalid_chars(self):
        """Test validation fails for invalid characters in revision identifier."""
        data = {
            "project_id": 1,
            "source_filename": "data.xlsx",
            "revision_identifier": "REV@001!",
            "import_type": "pipe_data",
        }
        with pytest.raises(ValidationError) as exc_info:
            ImportedDataRevisionCreateSchema(**data)

        assert (
            "can only contain letters, numbers, dots, hyphens, and underscores"
            in str(exc_info.value)
        )

    def test_revision_identifier_validation_valid_chars(self):
        """Test validation passes for valid revision identifier characters."""
        valid_identifiers = ["REV-001", "REV_001", "REV.001", "REV001", "rev-1.2_test"]

        for identifier in valid_identifiers:
            data = {
                "project_id": 1,
                "source_filename": "data.xlsx",
                "revision_identifier": identifier,
                "import_type": "pipe_data",
            }
            schema = ImportedDataRevisionCreateSchema(**data)
            assert schema.revision_identifier == identifier

    def test_update_schema_partial_fields(self):
        """Test update schema with only some fields."""
        data = {
            "revision_identifier": "REV-003",
            "is_active_revision": False,
        }
        schema = ImportedDataRevisionUpdateSchema(**data)

        assert schema.revision_identifier == "REV-003"
        assert schema.is_active_revision is False


class TestExportedDocumentSchemas:
    """Test ExportedDocument schema validation and functionality."""

    def test_valid_exported_document_creation(self):
        """Test creating a valid exported document schema."""
        data = {
            "project_id": 1,
            "generated_by_user_id": 1,
            "document_type": "heat_tracing_report",
            "filename": "HT_Report_Project_Alpha_v1.pdf",
            "revision": "v1.0",
            "file_path_or_url": "/documents/reports/ht_report_1.pdf",
            "is_latest_revision": True,
        }
        schema = ExportedDocumentCreateSchema(**data)

        assert schema.project_id == 1
        assert schema.generated_by_user_id == 1
        assert schema.document_type == DocumentType.HEAT_TRACING_REPORT
        assert schema.filename == "HT_Report_Project_Alpha_v1.pdf"
        assert schema.revision == "v1.0"
        assert schema.file_path_or_url == "/documents/reports/ht_report_1.pdf"
        assert schema.is_latest_revision is True

    def test_document_filename_validation_empty(self):
        """Test validation fails for empty document filename."""
        data = {
            "name": "Test Document",
            "project_id": 1,
            "document_type": "heat_tracing_report",
            "filename": "",
        }
        with pytest.raises(ValidationError) as exc_info:
            ExportedDocumentCreateSchema(**data)

        assert "String should have at least 1 character" in str(exc_info.value)

    def test_document_filename_validation_invalid_extension(self):
        """Test validation fails for invalid document file extension."""
        data = {
            "name": "Test Document",
            "project_id": 1,
            "document_type": "heat_tracing_report",
            "filename": "report.txt",
        }
        with pytest.raises(ValidationError) as exc_info:
            ExportedDocumentCreateSchema(**data)

        assert "File must have one of these extensions" in str(exc_info.value)

    def test_document_filename_validation_valid_extensions(self):
        """Test validation passes for valid document file extensions."""
        valid_extensions = [".pdf", ".xlsx", ".xls", ".csv", ".json"]

        for ext in valid_extensions:
            data = {
                "name": "Test Document",
                "project_id": 1,
                "document_type": "heat_tracing_report",
                "filename": f"report{ext}",
            }
            schema = ExportedDocumentCreateSchema(**data)
            assert schema.filename == f"report{ext}"


class TestCalculationStandardSchemas:
    """Test CalculationStandard schema validation and functionality."""

    def test_valid_calculation_standard_creation(self):
        """Test creating a valid calculation standard schema."""
        data = {
            "name": "TR 50410 Heat Tracing Standard",
            "standard_code": "TR-50410",
            "description": "Technical Report 50410 for heat tracing calculations",
            "parameters_json": '{"safety_factor": 1.2, "max_temp_rating": 250, "min_bend_radius": 50}',
        }
        schema = CalculationStandardCreateSchema(**data)

        assert schema.name == "TR 50410 Heat Tracing Standard"
        assert schema.standard_code == "TR-50410"
        assert (
            schema.description == "Technical Report 50410 for heat tracing calculations"
        )
        assert (
            schema.parameters_json
            == '{"safety_factor": 1.2, "max_temp_rating": 250, "min_bend_radius": 50}'
        )

    def test_standard_name_validation_empty(self):
        """Test validation fails for empty standard name."""
        data = {
            "name": "",
            "standard_code": "TR-50410",
        }
        with pytest.raises(ValidationError) as exc_info:
            CalculationStandardCreateSchema(**data)

        assert "String should have at least 1 character" in str(exc_info.value)

    def test_standard_code_validation_empty(self):
        """Test validation fails for empty standard code."""
        data = {
            "name": "Test Standard",
            "standard_code": "",
        }
        with pytest.raises(ValidationError) as exc_info:
            CalculationStandardCreateSchema(**data)

        assert "String should have at least 1 character" in str(exc_info.value)

    def test_standard_code_normalization(self):
        """Test that standard code is normalized to uppercase."""
        data = {
            "name": "Test Standard",
            "standard_code": "tr-50410",
        }
        schema = CalculationStandardCreateSchema(**data)
        assert schema.standard_code == "TR-50410"

    def test_standard_code_validation_invalid_chars(self):
        """Test validation fails for invalid characters in standard code."""
        data = {
            "name": "Test Standard",
            "standard_code": "TR@50410!",
        }
        with pytest.raises(ValidationError) as exc_info:
            CalculationStandardCreateSchema(**data)

        assert (
            "can only contain letters, numbers, dots, hyphens, and underscores"
            in str(exc_info.value)
        )

    def test_parameters_json_validation_invalid_json(self):
        """Test validation fails for invalid JSON format."""
        data = {
            "name": "Test Standard",
            "standard_code": "TR-50410",
            "parameters_json": "invalid json",
        }
        with pytest.raises(ValidationError) as exc_info:
            CalculationStandardCreateSchema(**data)

        assert "Invalid JSON format" in str(exc_info.value)

    def test_parameters_json_validation_not_object(self):
        """Test validation fails when parameters is not a JSON object."""
        data = {
            "name": "Test Standard",
            "standard_code": "TR-50410",
            "parameters_json": '["not", "an", "object"]',
        }
        with pytest.raises(ValidationError) as exc_info:
            CalculationStandardCreateSchema(**data)

        assert "Parameters must be a JSON object" in str(exc_info.value)

    def test_parameters_json_validation_valid_object(self):
        """Test validation passes for valid JSON object."""
        data = {
            "name": "Test Standard",
            "standard_code": "TR-50410",
            "parameters_json": '{"key": "value", "number": 123}',
        }
        schema = CalculationStandardCreateSchema(**data)
        assert schema.parameters_json == '{"key": "value", "number": 123}'


class TestEnums:
    """Test enum values and validation."""

    def test_import_type_enum_values(self):
        """Test ImportType enum has expected values."""
        expected_values = [
            "pipe_data",
            "vessel_data",
            "component_data",
            "electrical_data",
            "switchboard_data",
        ]

        for value in expected_values:
            assert ImportType(value) == value

    def test_document_type_enum_values(self):
        """Test DocumentType enum has expected values."""
        expected_values = [
            "heat_tracing_report",
            "electrical_report",
            "switchboard_report",
            "calculation_summary",
            "project_overview",
            "compliance_report",
        ]

        for value in expected_values:
            assert DocumentType(value) == value

    def test_file_format_enum_values(self):
        """Test FileFormat enum has expected values."""
        expected_values = ["pdf", "excel", "csv", "json"]

        for value in expected_values:
            assert FileFormat(value) == value
