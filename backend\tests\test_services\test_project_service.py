# backend/tests/test_services/test_project_service.py
"""
Unit tests for ProjectService.

Tests business logic layer functionality including CRUD operations,
validation, error handling, and orchestration. Enhanced with utility and DI patterns.
"""

from datetime import datetime, timezone
from unittest.mock import Mock, patch

import pytest

# Mark all tests in this file
pytestmark = [pytest.mark.unit, pytest.mark.service, pytest.mark.project]

from core.errors.exceptions import (
    DataValidationError,
    DuplicateEntryError,
    ProjectNotFoundError,
)
from core.models.project import Project
from core.repositories.project_repository import ProjectRepository
from core.schemas.project_schemas import (
    ProjectCreateSchema,
    ProjectListResponseSchema,
    ProjectReadSchema,
    ProjectUpdateSchema,
)
from core.services.project_service import ProjectService


class TestProjectService:
    """Test ProjectService functionality."""

    @pytest.fixture
    def mock_repository(self):
        """Create a mock ProjectRepository for testing."""
        mock_repo = Mock(spec=ProjectRepository)
        mock_repo.db_session = Mock()
        return mock_repo

    @pytest.fixture
    def project_service(self, mock_repository):
        """Create a ProjectService instance with mocked repository."""
        return ProjectService(mock_repository)

    @pytest.fixture
    def sample_project_create_schema(self, sample_project_create_data):
        """Create a ProjectCreateSchema for testing."""
        return ProjectCreateSchema(**sample_project_create_data)

    @pytest.fixture
    def sample_project_update_schema(self, sample_project_update_data):
        """Create a ProjectUpdateSchema for testing."""
        return ProjectUpdateSchema(**sample_project_update_data)

    @pytest.fixture
    def mock_project_orm(self, sample_project_data):
        """Create a mock Project ORM object."""
        project = Mock(spec=Project)
        project.id = 1
        project.name = sample_project_data["name"]
        project.project_number = sample_project_data["project_number"]
        project.description = sample_project_data["description"]
        project.designer = sample_project_data["designer"]
        project.notes = sample_project_data["notes"]
        project.min_ambient_temp_c = sample_project_data["min_ambient_temp_c"]
        project.max_ambient_temp_c = sample_project_data["max_ambient_temp_c"]
        project.desired_maintenance_temp_c = sample_project_data[
            "desired_maintenance_temp_c"
        ]
        project.wind_speed_ms = sample_project_data["wind_speed_ms"]
        project.installation_environment = sample_project_data[
            "installation_environment"
        ]
        project.available_voltages_json = sample_project_data["available_voltages_json"]
        project.default_cable_manufacturer = sample_project_data[
            "default_cable_manufacturer"
        ]
        project.default_control_device_manufacturer = sample_project_data[
            "default_control_device_manufacturer"
        ]
        project.created_at = datetime.now(timezone.utc)
        project.updated_at = datetime.now(timezone.utc)
        project.is_deleted = False
        project.deleted_at = None
        project.deleted_by_user_id = None
        return project

    def test_create_project_success(
        self,
        project_service,
        mock_repository,
        sample_project_create_schema,
        mock_project_orm,
    ):
        """Test successful project creation."""
        # Setup mock
        mock_repository.create.return_value = mock_project_orm
        mock_repository.db_session.commit.return_value = None
        mock_repository.db_session.refresh.return_value = None

        # Execute
        result = project_service.create_project(sample_project_create_schema)

        # Verify
        assert isinstance(result, ProjectReadSchema)
        assert result.name == sample_project_create_schema.name
        assert result.project_number == sample_project_create_schema.project_number

        # Verify repository was called correctly
        mock_repository.create.assert_called_once()
        mock_repository.db_session.commit.assert_called_once()
        mock_repository.db_session.refresh.assert_called_once_with(mock_project_orm)

    def test_create_project_validation_error(self, project_service, mock_repository):
        """Test project creation with validation error."""
        # Create a valid schema but test service-level validation
        valid_data = {
            "name": "Test Project",
            "project_number": "TEST-001",
            "min_ambient_temp_c": -20.0,
            "max_ambient_temp_c": 40.0,
            "desired_maintenance_temp_c": 30.0,  # Invalid: maintenance temp <= max ambient
        }

        schema = ProjectCreateSchema(**valid_data)

        # Execute and verify - service should catch this business rule violation
        with pytest.raises(DataValidationError) as exc_info:
            project_service.create_project(schema)

        assert "maintenance_temperature" in exc_info.value.metadata["validation_errors"]

        # Repository should not be called
        mock_repository.create.assert_not_called()

    def test_create_project_duplicate_name_error(
        self, project_service, mock_repository, sample_project_create_schema
    ):
        """Test project creation with duplicate name."""
        from sqlalchemy.exc import IntegrityError

        # Setup mock to raise IntegrityError
        integrity_error = IntegrityError(
            "statement", "params", Exception("uq_project_name")
        )
        integrity_error.orig = Mock()
        integrity_error.orig.__str__ = Mock(return_value="uq_project_name")
        mock_repository.create.side_effect = integrity_error
        mock_repository.db_session.rollback.return_value = None

        # Execute and verify
        with pytest.raises(DuplicateEntryError) as exc_info:
            project_service.create_project(sample_project_create_schema)

        assert "name" in str(exc_info.value).lower()
        mock_repository.db_session.rollback.assert_called_once()

    def test_get_project_details_by_id(
        self, project_service, mock_repository, mock_project_orm
    ):
        """Test getting project details by numeric ID."""
        # Setup mock
        mock_repository.get_by_id.return_value = mock_project_orm

        # Execute
        result = project_service.get_project_details("1")

        # Verify
        assert isinstance(result, ProjectReadSchema)
        assert result.id == mock_project_orm.id
        mock_repository.get_by_id.assert_called_once_with(1)

    def test_get_project_details_by_code(
        self, project_service, mock_repository, mock_project_orm
    ):
        """Test getting project details by project number."""
        # Setup mock
        mock_repository.get_by_id.return_value = None
        mock_repository.get_by_code.return_value = mock_project_orm

        # Execute
        result = project_service.get_project_details("HT-TEST-001")

        # Verify
        assert isinstance(result, ProjectReadSchema)
        assert result.id == mock_project_orm.id
        mock_repository.get_by_code.assert_called_once_with("HT-TEST-001")

    def test_get_project_details_not_found(self, project_service, mock_repository):
        """Test getting project details when project doesn't exist."""
        # Setup mock
        mock_repository.get_by_id.return_value = None
        mock_repository.get_by_code.side_effect = ProjectNotFoundError("TEST-001")

        # Execute and verify
        with pytest.raises(ProjectNotFoundError):
            project_service.get_project_details("TEST-001")

    def test_update_project_success(
        self,
        project_service,
        mock_repository,
        sample_project_update_schema,
        mock_project_orm,
    ):
        """Test successful project update."""
        # Setup mock
        mock_repository.get_by_id.return_value = mock_project_orm
        mock_repository.get_by_code.return_value = None
        mock_repository.db_session.commit.return_value = None
        mock_repository.db_session.refresh.return_value = None

        # Execute
        result = project_service.update_project("1", sample_project_update_schema)

        # Verify
        assert isinstance(result, ProjectReadSchema)
        mock_repository.db_session.commit.assert_called_once()

    def test_delete_project_success(
        self, project_service, mock_repository, mock_project_orm
    ):
        """Test successful project deletion."""
        # Setup mock
        mock_repository.get_by_id.return_value = mock_project_orm
        mock_repository.get_by_code.return_value = None
        mock_repository.db_session.commit.return_value = None

        # Execute
        project_service.delete_project("1", deleted_by_user_id=123)

        # Verify
        assert mock_project_orm.is_deleted is True
        assert mock_project_orm.deleted_at is not None
        assert mock_project_orm.deleted_by_user_id == 123
        mock_repository.db_session.commit.assert_called_once()

    def test_get_projects_list_success(self, project_service, mock_repository):
        """Test getting projects list with pagination."""
        # Setup mock data
        mock_projects = []
        for i in range(5):
            project = Mock(spec=Project)
            project.id = i + 1
            project.name = f"Test Project {i + 1}"
            project.project_number = f"TEST-{i + 1:03d}"
            project.description = f"Description {i + 1}"
            project.designer = f"Engineer {i + 1}"
            project.created_at = datetime.now(timezone.utc)
            project.updated_at = datetime.now(timezone.utc)
            project.is_deleted = False
            mock_projects.append(project)

        # Setup mock repository
        mock_repository.get_all.return_value = mock_projects

        # Execute
        result = project_service.get_projects_list(page=1, per_page=10)

        # Verify
        assert isinstance(result, ProjectListResponseSchema)
        assert len(result.projects) == 5
        assert result.total == 5
        assert result.page == 1
        assert result.per_page == 10
        assert result.total_pages == 1


class TestProjectServiceWithUtilities:
    """Test ProjectService with utility integration."""

    @pytest.fixture
    def mock_repository_with_utils(self):
        """Create a mock ProjectRepository with utility support."""
        mock_repo = Mock(spec=ProjectRepository)
        mock_repo.db_session = Mock()
        return mock_repo

    @pytest.fixture
    def project_service_with_utils(self, mock_repository_with_utils):
        """Create a ProjectService instance with mocked repository and utilities."""
        return ProjectService(mock_repository_with_utils)

    def test_create_project_with_string_sanitization(
        self, project_service_with_utils, mock_repository_with_utils, mock_string_utils
    ):
        """Test project creation with string sanitization utilities."""
        # Setup mock project data with potentially unsafe strings
        project_data = ProjectCreateSchema(
            name="<script>alert('xss')</script>Test Project",
            project_number="TEST-001",
            description="Test description with <b>HTML</b>",
            designer="Test Engineer",
            min_ambient_temp_c=-20.0,
            max_ambient_temp_c=40.0,
            desired_maintenance_temp_c=65.0,
        )

        # Mock the repository response
        mock_project = Mock(spec=Project)
        mock_project.id = 1
        mock_project.name = "sanitized_text"  # Sanitized version
        mock_project.description = "sanitized_text"  # Sanitized version
        mock_repository_with_utils.create.return_value = mock_project

        # Mock string utilities
        with patch(
            "core.utils.string_utils.sanitize_text", mock_string_utils.sanitize_text
        ):
            # Execute
            result = project_service_with_utils.create_project(project_data)

            # Verify string sanitization was called
            mock_string_utils.sanitize_text.assert_called()

            # Verify repository was called
            mock_repository_with_utils.create.assert_called_once()

    def test_get_projects_with_pagination_utils(
        self,
        project_service_with_utils,
        mock_repository_with_utils,
        mock_pagination_utils,
    ):
        """Test getting projects list with pagination utilities."""
        from datetime import datetime

        # Setup mock projects with realistic data for schema validation
        mock_projects = []
        for i in range(5):
            mock_project = Mock(spec=Project)
            mock_project.id = i + 1
            mock_project.name = f"Test Project {i + 1}"
            mock_project.project_number = f"PRJ-{i + 1:03d}"
            mock_project.description = f"Test project description {i + 1}"
            mock_project.designer = f"Designer {i + 1}"
            mock_project.created_at = datetime.now()
            mock_project.updated_at = datetime.now()
            mock_project.is_deleted = False
            mock_projects.append(mock_project)

        # Configure repository to return list of projects
        mock_repository_with_utils.get_all.return_value = mock_projects

        # Mock pagination utilities
        with patch(
            "core.utils.pagination_utils.parse_pagination_params",
            mock_pagination_utils.parse_pagination_params,
        ):
            # Execute
            result = project_service_with_utils.get_projects_list(page=1, per_page=10)

            # Verify repository was called multiple times (for pagination and total count)
            assert mock_repository_with_utils.get_all.call_count == 2
            # First call for paginated results
            mock_repository_with_utils.get_all.assert_any_call(skip=0, limit=10)
            # Second call for total count
            mock_repository_with_utils.get_all.assert_any_call(skip=0, limit=1000)

            # Verify result structure
            assert hasattr(result, "projects")
            assert hasattr(result, "total")
            assert len(result.projects) == 5

    def test_update_project_with_datetime_utils(
        self,
        project_service_with_utils,
        mock_repository_with_utils,
        mock_datetime_utils,
    ):
        """Test project update with datetime utilities."""
        # Setup mock project
        mock_project = Mock(spec=Project)
        mock_project.id = 1
        mock_project.updated_at = mock_datetime_utils.utcnow_aware.return_value
        mock_repository_with_utils.get_by_id.return_value = mock_project

        # Setup update data
        update_data = ProjectUpdateSchema(name="Updated Project Name")

        # Mock datetime utilities
        with patch(
            "core.utils.datetime_utils.utcnow_aware", mock_datetime_utils.utcnow_aware
        ):
            # Execute
            result = project_service_with_utils.update_project("1", update_data)

            # Verify datetime utilities were used for timestamp
            mock_datetime_utils.utcnow_aware.assert_called()
            mock_repository_with_utils.get_by_id.assert_called_once_with(1)


class TestProjectServiceWithDependencyInjection:
    """Test ProjectService with dependency injection patterns."""

    def test_service_creation_with_di(self, mock_repository_dependencies):
        """Test service creation using dependency injection pattern."""
        # Get repository from DI container
        project_repo = mock_repository_dependencies["project_repo"]

        # Create service with injected dependency
        service = ProjectService(project_repo)

        # Verify service was created with correct repository
        assert service.project_repository == project_repo

    def test_service_method_with_repository_di(self, mock_repository_dependencies):
        """Test service method using repository dependency injection."""
        # Setup
        project_repo = mock_repository_dependencies["project_repo"]
        service = ProjectService(project_repo)

        # Mock repository response
        mock_project = Mock(spec=Project)
        mock_project.id = 1
        project_repo.get_by_id.return_value = mock_project

        # Execute
        result = service.get_project_details("1")

        # Verify repository dependency was used
        project_repo.get_by_id.assert_called_once_with(1)

    def test_multiple_services_with_shared_dependencies(
        self, mock_repository_dependencies
    ):
        """Test multiple services sharing repository dependencies."""
        # Create multiple services with shared dependencies
        project_repo = mock_repository_dependencies["project_repo"]
        user_repo = mock_repository_dependencies["user_repo"]

        project_service = ProjectService(project_repo)

        # Verify each service has its own repository
        assert project_service.project_repository == project_repo
        assert project_service.project_repository != user_repo


class TestProjectServiceIntegrationPatterns:
    """Test integration patterns combining utilities and DI."""

    def test_complete_workflow_with_utils_and_di(
        self,
        mock_repository_dependencies,
        mock_string_utils,
        mock_datetime_utils,
        mock_pagination_utils,
    ):
        """Test complete workflow using both utilities and DI."""
        # Setup service with DI
        project_repo = mock_repository_dependencies["project_repo"]
        service = ProjectService(project_repo)

        # Mock all utilities
        with (
            patch.multiple(
                "core.utils.string_utils", sanitize_text=mock_string_utils.sanitize_text
            ),
            patch.multiple(
                "core.utils.datetime_utils",
                utcnow_aware=mock_datetime_utils.utcnow_aware,
            ),
            patch.multiple(
                "core.utils.pagination_utils",
                parse_pagination_params=mock_pagination_utils.parse_pagination_params,
            ),
        ):
            # Test create operation
            create_data = ProjectCreateSchema(
                name="Test Project",
                project_number="TEST-001",
                min_ambient_temp_c=-20.0,
                max_ambient_temp_c=40.0,
                desired_maintenance_temp_c=65.0,
            )

            mock_project = Mock(spec=Project)
            mock_project.id = 1
            project_repo.create.return_value = mock_project

            # Execute create
            created_project = service.create_project(create_data)

            # Verify DI and utilities were used
            project_repo.create.assert_called()
            mock_string_utils.sanitize_text.assert_called()
            mock_datetime_utils.utcnow_aware.assert_called()

            # Test list operation with pagination
            project_repo.get_paginated.return_value = (
                mock_pagination_utils.paginate_query.return_value
            )

            # Execute list
            projects_list = service.get_projects_list(page=1, per_page=10)

            # Verify pagination utilities and DI were used
            project_repo.get_paginated.assert_called()
            mock_pagination_utils.parse_pagination_params.assert_called()
