# backend/tests/test_user_service.py
"""
Tests for User Service

This module tests the business logic of user operations including authentication.
"""

import pytest

# Mark all tests in this file
pytestmark = [pytest.mark.unit, pytest.mark.service, pytest.mark.user]
import sys
import os
from unittest.mock import Mock, patch
from sqlalchemy.orm import Session

# Add the backend directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.errors.exceptions import InvalidInputError, NotFoundError
from core.schemas.user_schemas import (
    UserCreateSchema,
    UserUpdateSchema,
    LoginRequestSchema,
    PasswordChangeRequestSchema,
    UserPreferenceUpdateSchema,
)
from core.services.user_service import UserService


class TestUserService:
    """Test user service business logic."""

    @pytest.fixture
    def mock_db_session(self):
        """Create a mock database session."""
        return Mock(spec=Session)

    @pytest.fixture
    def mock_user_repo(self):
        """Create a mock user repository."""
        return Mock()

    @pytest.fixture
    def mock_preference_repo(self):
        """Create a mock user preference repository."""
        return Mock()

    @pytest.fixture
    def user_service(self, mock_db_session):
        """Create a user service with mocked dependencies."""
        with patch.multiple(
            "core.services.user_service",
            UserRepository=Mock(),
            UserPreferenceRepository=Mock(),
        ):
            return UserService(mock_db_session)

    def test_create_user_success(self, user_service, mock_db_session):
        """Test successful user creation."""
        # Arrange
        user_data = UserCreateSchema(
            name="John Smith",
            email="<EMAIL>",
            password="SecurePass123",
            is_active=True,
        )

        from datetime import datetime

        mock_user = Mock()
        mock_user.id = 1
        mock_user.name = "John Smith"
        mock_user.email = "<EMAIL>"
        mock_user.is_active = True
        mock_user.created_at = datetime(2024, 1, 15, 10, 30, 0)
        mock_user.updated_at = datetime(2024, 1, 15, 10, 30, 0)
        mock_user.is_deleted = False
        mock_user.deleted_at = None
        mock_user.deleted_by_user_id = None

        user_service.user_repo.get_by_email.return_value = None  # Email not exists
        user_service.user_repo.create.return_value = mock_user

        # Act
        result = user_service.create_user(user_data)

        # Assert
        assert result.name == "John Smith"
        assert result.email == "<EMAIL>"
        user_service.user_repo.create.assert_called_once()
        mock_db_session.commit.assert_called_once()

    def test_create_user_email_already_exists(self, user_service):
        """Test user creation when email already exists."""
        # Arrange
        user_data = UserCreateSchema(
            name="John Smith",
            email="<EMAIL>",
            password="SecurePass123",
            is_active=True,
        )

        mock_existing_user = Mock()
        user_service.user_repo.get_by_email.return_value = mock_existing_user

        # Act & Assert
        with pytest.raises(InvalidInputError) as exc_info:
            user_service.create_user(user_data)

        assert "already exists" in str(exc_info.value)
        assert "<EMAIL>" in exc_info.value.detail

    def test_get_user_success(self, user_service):
        """Test successful user retrieval."""
        # Arrange
        from datetime import datetime

        mock_user = Mock()
        mock_user.id = 1
        mock_user.name = "John Smith"
        mock_user.email = "<EMAIL>"
        mock_user.is_active = True
        mock_user.created_at = datetime(2024, 1, 15, 10, 30, 0)
        mock_user.updated_at = datetime(2024, 1, 15, 10, 30, 0)
        mock_user.is_deleted = False
        mock_user.deleted_at = None
        mock_user.deleted_by_user_id = None

        user_service.user_repo.get_by_id.return_value = mock_user

        # Act
        result = user_service.get_user(1)

        # Assert
        assert result.name == "John Smith"
        user_service.user_repo.get_by_id.assert_called_once_with(1)

    def test_get_user_not_found(self, user_service):
        """Test user retrieval when not found."""
        # Arrange
        user_service.user_repo.get_by_id.return_value = None

        # Act & Assert
        with pytest.raises(NotFoundError) as exc_info:
            user_service.get_user(999)

        assert exc_info.value.code == "USER_NOT_FOUND"
        assert "999" in exc_info.value.detail

    def test_get_user_inactive(self, user_service):
        """Test user retrieval when user is inactive."""
        # Arrange
        mock_user = Mock()
        mock_user.id = 1
        mock_user.is_active = False

        user_service.user_repo.get_by_id.return_value = mock_user

        # Act & Assert
        with pytest.raises(NotFoundError) as exc_info:
            user_service.get_user(1)

        assert exc_info.value.code == "USER_NOT_FOUND"
        assert "inactive" in exc_info.value.detail

    def test_login_success(self, user_service):
        """Test successful user login."""
        # Arrange
        login_data = LoginRequestSchema(
            email="<EMAIL>",
            password="SecurePass123",
        )

        mock_user = Mock()
        mock_user.id = 1
        mock_user.email = "<EMAIL>"
        mock_user.is_active = True

        user_service.user_repo.get_by_email.return_value = mock_user

        with patch.object(user_service, "verify_password", return_value=True):
            with patch.object(
                user_service, "generate_access_token", return_value="mock_token"
            ):
                # Act
                result = user_service.login(login_data)

                # Assert
                assert result.user.email == "<EMAIL>"
                assert result.access_token == "mock_token"
                assert result.token_type == "bearer"

    def test_login_invalid_email(self, user_service):
        """Test login with invalid email."""
        # Arrange
        login_data = LoginRequestSchema(
            email="<EMAIL>",
            password="SecurePass123",
        )

        user_service.user_repo.get_by_email.return_value = None

        # Act & Assert
        with pytest.raises(InvalidInputError) as exc_info:
            user_service.login(login_data)

        assert "Invalid email or password" in str(exc_info.value)

    def test_login_invalid_password(self, user_service):
        """Test login with invalid password."""
        # Arrange
        login_data = LoginRequestSchema(
            email="<EMAIL>",
            password="WrongPassword",
        )

        mock_user = Mock()
        mock_user.is_active = True
        user_service.user_repo.get_by_email.return_value = mock_user

        with patch.object(user_service, "verify_password", return_value=False):
            # Act & Assert
            with pytest.raises(InvalidInputError) as exc_info:
                user_service.login(login_data)

            assert "Invalid email or password" in str(exc_info.value)

    def test_login_inactive_user(self, user_service):
        """Test login with inactive user."""
        # Arrange
        login_data = LoginRequestSchema(
            email="<EMAIL>",
            password="SecurePass123",
        )

        mock_user = Mock()
        mock_user.is_active = False
        user_service.user_repo.get_by_email.return_value = mock_user

        # Act & Assert
        with pytest.raises(InvalidInputError) as exc_info:
            user_service.login(login_data)

        assert "Invalid email or password" in str(exc_info.value)

    def test_change_password_success(self, user_service, mock_db_session):
        """Test successful password change."""
        # Arrange
        password_data = PasswordChangeRequestSchema(
            current_password="OldPass123",
            new_password="NewSecurePass456",
        )

        mock_user = Mock()
        mock_user.is_active = True
        user_service.user_repo.get_by_id.return_value = mock_user

        with patch.object(user_service, "verify_password", return_value=True):
            with patch.object(
                user_service, "hash_password", return_value="hashed_new_password"
            ):
                # Act
                result = user_service.change_password(1, password_data)

                # Assert
                assert result is True
                user_service.user_repo.update.assert_called_once()
                mock_db_session.commit.assert_called_once()

    def test_change_password_invalid_current(self, user_service):
        """Test password change with invalid current password."""
        # Arrange
        password_data = PasswordChangeRequestSchema(
            current_password="WrongOldPass",
            new_password="NewSecurePass456",
        )

        mock_user = Mock()
        mock_user.is_active = True
        user_service.user_repo.get_by_id.return_value = mock_user

        with patch.object(user_service, "verify_password", return_value=False):
            # Act & Assert
            with pytest.raises(InvalidInputError) as exc_info:
                user_service.change_password(1, password_data)

            assert "Current password is incorrect" in str(exc_info.value)

    def test_update_user_success(self, user_service, mock_db_session):
        """Test successful user update."""
        # Arrange
        update_data = UserUpdateSchema(
            name="Updated Name",
            is_active=False,
        )

        mock_existing = Mock()
        mock_updated = Mock()
        mock_updated.id = 1
        mock_updated.name = "Updated Name"

        user_service.user_repo.get_by_id.return_value = mock_existing
        user_service.user_repo.update.return_value = mock_updated

        # Act
        result = user_service.update_user(1, update_data)

        # Assert
        assert result.name == "Updated Name"
        user_service.user_repo.update.assert_called_once()
        mock_db_session.commit.assert_called_once()

    def test_deactivate_user_success(self, user_service, mock_db_session):
        """Test successful user deactivation."""
        # Arrange
        # Configure the deactivate_user method to return True
        user_service.user_repo.deactivate_user.return_value = True

        # Act
        result = user_service.deactivate_user(1)

        # Assert
        assert result is True
        user_service.user_repo.deactivate_user.assert_called_once_with(1)
        mock_db_session.commit.assert_called_once()

    def test_get_user_preferences_success(self, user_service):
        """Test successful user preferences retrieval."""
        # Arrange
        mock_user = Mock()
        mock_user.is_active = True
        from datetime import datetime

        mock_preferences = Mock()
        mock_preferences.id = 1
        mock_preferences.user_id = 1
        mock_preferences.ui_theme = "dark"
        mock_preferences.default_min_ambient_temp_c = -10.0
        mock_preferences.default_max_ambient_temp_c = 40.0
        mock_preferences.default_desired_maintenance_temp_c = 65.0
        mock_preferences.default_safety_margin_percent = 20.0
        mock_preferences.preferred_cable_manufacturers_json = '["Manufacturer A"]'
        mock_preferences.preferred_control_device_manufacturers_json = '["Device A"]'
        mock_preferences.created_at = datetime(2024, 1, 15, 10, 30, 0)
        mock_preferences.updated_at = datetime(2024, 1, 15, 10, 30, 0)
        mock_preferences.is_deleted = False
        mock_preferences.deleted_at = None
        mock_preferences.deleted_by_user_id = None

        user_service.user_repo.get_by_id.return_value = mock_user
        user_service.preference_repo.get_by_user_id.return_value = mock_preferences

        # Act
        result = user_service.get_user_preferences(1)

        # Assert
        assert result is not None
        user_service.preference_repo.get_by_user_id.assert_called_once_with(1)

    def test_get_user_preferences_not_found(self, user_service):
        """Test user preferences retrieval when not found."""
        # Arrange
        mock_user = Mock()
        mock_user.is_active = True

        user_service.user_repo.get_by_id.return_value = mock_user
        user_service.preference_repo.get_by_user_id.return_value = None

        # Act
        result = user_service.get_user_preferences(1)

        # Assert
        assert result is None

    def test_create_or_update_user_preferences_success(
        self, user_service, mock_db_session
    ):
        """Test successful user preferences creation/update."""
        # Arrange
        preferences_data = UserPreferenceUpdateSchema(
            ui_theme="dark",
            default_safety_margin_percent=20.0,
        )

        mock_user = Mock()
        mock_user.is_active = True
        from datetime import datetime

        mock_preferences = Mock()
        mock_preferences.id = 1
        mock_preferences.user_id = 1
        mock_preferences.ui_theme = "dark"
        mock_preferences.default_min_ambient_temp_c = -10.0
        mock_preferences.default_max_ambient_temp_c = 40.0
        mock_preferences.default_desired_maintenance_temp_c = 65.0
        mock_preferences.default_safety_margin_percent = 20.0
        mock_preferences.preferred_cable_manufacturers_json = '["Manufacturer A"]'
        mock_preferences.preferred_control_device_manufacturers_json = '["Device A"]'
        mock_preferences.created_at = datetime(2024, 1, 15, 10, 30, 0)
        mock_preferences.updated_at = datetime(2024, 1, 15, 10, 30, 0)
        mock_preferences.is_deleted = False
        mock_preferences.deleted_at = None
        mock_preferences.deleted_by_user_id = None

        user_service.user_repo.get_by_id.return_value = mock_user
        user_service.preference_repo.create_or_update_preferences.return_value = (
            mock_preferences
        )

        # Act
        result = user_service.create_or_update_user_preferences(1, preferences_data)

        # Assert
        assert result is not None
        user_service.preference_repo.create_or_update_preferences.assert_called_once()
        mock_db_session.commit.assert_called_once()

    def test_search_users_success(self, user_service):
        """Test successful user search."""
        # Arrange
        from datetime import datetime

        mock_user1 = Mock()
        mock_user1.id = 1
        mock_user1.name = "John Smith"
        mock_user1.email = "<EMAIL>"
        mock_user1.is_active = True
        mock_user1.created_at = datetime(2024, 1, 15, 10, 30, 0)
        mock_user1.updated_at = datetime(2024, 1, 15, 10, 30, 0)
        mock_user1.is_deleted = False
        mock_user1.deleted_at = None
        mock_user1.deleted_by_user_id = None

        mock_user2 = Mock()
        mock_user2.id = 2
        mock_user2.name = "John Doe"
        mock_user2.email = "<EMAIL>"
        mock_user2.is_active = True
        mock_user2.created_at = datetime(2024, 1, 15, 10, 30, 0)
        mock_user2.updated_at = datetime(2024, 1, 15, 10, 30, 0)
        mock_user2.is_deleted = False
        mock_user2.deleted_at = None
        mock_user2.deleted_by_user_id = None

        mock_users = [mock_user1, mock_user2]
        user_service.user_repo.search_users.return_value = mock_users

        # Act
        result = user_service.search_users("john", 0, 10)

        # Assert
        assert len(result) == 2
        user_service.user_repo.search_users.assert_called_once_with("john", 0, 10)

    def test_count_active_users_success(self, user_service):
        """Test successful active user count."""
        # Arrange
        user_service.user_repo.count_active_users.return_value = 5

        # Act
        result = user_service.count_active_users()

        # Assert
        assert result == 5
        user_service.user_repo.count_active_users.assert_called_once()

    @patch("core.services.user_service.pwd_context")
    def test_hash_password(self, mock_pwd_context, user_service):
        """Test password hashing."""
        # Arrange
        mock_pwd_context.hash.return_value = "hashed_password"

        # Act
        result = user_service.hash_password("plain_password")

        # Assert
        assert result == "hashed_password"
        mock_pwd_context.hash.assert_called_once_with("plain_password")

    @patch("core.services.user_service.pwd_context")
    def test_verify_password(self, mock_pwd_context, user_service):
        """Test password verification."""
        # Arrange
        mock_pwd_context.verify.return_value = True

        # Act
        result = user_service.verify_password("plain_password", "hashed_password")

        # Assert
        assert result is True
        mock_pwd_context.verify.assert_called_once_with(
            "plain_password", "hashed_password"
        )
