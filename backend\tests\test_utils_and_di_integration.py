# backend/tests/test_utils_and_di_integration.py
"""
Integration tests for utility and dependency injection patterns.

This module tests the integration between utilities and dependency injection
to ensure they work correctly together in the application.
"""

import pytest
from unittest.mock import Mock, patch
from datetime import datetime, timezone

# Mark all tests in this file
pytestmark = [
    pytest.mark.unit,
    pytest.mark.integration,
    pytest.mark.utils,
    pytest.mark.dependency_injection,
]


class TestUtilityIntegration:
    """Test utility function integration patterns."""

    def test_datetime_utils_in_service_layer(self, mock_datetime_utils):
        """Test datetime utilities integration in service layer."""
        # Test that services can use datetime utilities
        with patch('core.utils.datetime_utils.utcnow_aware', mock_datetime_utils.utcnow_aware):
            from core.utils.datetime_utils import utcnow_aware
            
            result = utcnow_aware()
            assert result == datetime(2024, 1, 15, 10, 30, 0, tzinfo=timezone.utc)
            mock_datetime_utils.utcnow_aware.assert_called_once()

    def test_string_utils_in_service_layer(self, mock_string_utils):
        """Test string utilities integration in service layer."""
        with patch('core.utils.string_utils.sanitize_text', mock_string_utils.sanitize_text):
            from core.utils.string_utils import sanitize_text
            
            result = sanitize_text("test input")
            assert result == "sanitized_text"
            mock_string_utils.sanitize_text.assert_called_once_with("test input")

    def test_pagination_utils_in_service_layer(self, mock_pagination_utils):
        """Test pagination utilities integration in service layer."""
        with patch('core.utils.pagination_utils.parse_pagination_params', mock_pagination_utils.parse_pagination_params):
            from core.utils.pagination_utils import parse_pagination_params
            
            result = parse_pagination_params(page=1, per_page=10)
            assert result.page == 1
            assert result.per_page == 10
            mock_pagination_utils.parse_pagination_params.assert_called_once()

    def test_json_validation_utils_in_service_layer(self, mock_json_validation_utils):
        """Test JSON validation utilities integration in service layer."""
        with patch('core.utils.json_validation.validate_json_data', mock_json_validation_utils.validate_json_data):
            from core.utils.json_validation import validate_json_data
            
            result = validate_json_data({"test": "data"}, Mock())
            assert result == {"validated": True}
            mock_json_validation_utils.validate_json_data.assert_called_once()

    def test_file_io_utils_in_service_layer(self, mock_file_io_utils):
        """Test file I/O utilities integration in service layer."""
        with patch('core.utils.file_io_utils.read_json_file', mock_file_io_utils.read_json_file):
            from core.utils.file_io_utils import read_json_file
            
            result = read_json_file("/test/path")
            assert result == {"test": "data"}
            mock_file_io_utils.read_json_file.assert_called_once_with("/test/path")


class TestDependencyInjectionIntegration:
    """Test dependency injection integration patterns."""

    def test_repository_dependency_injection(self, mock_repository_dependencies):
        """Test repository dependency injection."""
        # Test that repositories can be injected properly
        project_repo = mock_repository_dependencies['project_repo']
        user_repo = mock_repository_dependencies['user_repo']
        
        assert project_repo is not None
        assert user_repo is not None
        assert project_repo != user_repo

    def test_service_dependency_injection(self, mock_service_dependencies):
        """Test service dependency injection."""
        # Test that services can be injected properly
        project_service = mock_service_dependencies['project_service']
        user_service = mock_service_dependencies['user_service']
        
        assert project_service is not None
        assert user_service is not None
        assert project_service != user_service

    def test_dependency_override_manager(self, dependency_override_manager):
        """Test dependency override manager functionality."""
        from fastapi import FastAPI
        
        app = FastAPI()
        dependency_override_manager.set_app(app)
        
        # Test dependency override
        def mock_dependency():
            return "mocked"
        
        def original_dependency():
            return "original"
        
        dependency_override_manager.override_dependency(original_dependency, mock_dependency)
        
        assert original_dependency in app.dependency_overrides
        assert app.dependency_overrides[original_dependency] == mock_dependency
        
        # Test cleanup
        dependency_override_manager.clear_overrides()
        assert original_dependency not in app.dependency_overrides

    def test_enhanced_test_client(self, enhanced_test_client):
        """Test enhanced test client functionality."""
        from fastapi import APIRouter
        
        router = APIRouter()
        
        @router.get("/test")
        def test_endpoint():
            return {"message": "test"}
        
        enhanced_test_client.add_router(router)
        
        response = enhanced_test_client.get("/test")
        assert response.status_code == 200
        assert response.json() == {"message": "test"}


class TestUtilityAndDIIntegration:
    """Test combined utility and dependency injection patterns."""

    def test_service_with_utilities_and_di(self, mock_repository_dependencies, mock_string_utils):
        """Test service using both utilities and dependency injection."""
        # Mock a service that uses both DI and utilities
        project_repo = mock_repository_dependencies['project_repo']
        
        with patch('core.utils.string_utils.sanitize_text', mock_string_utils.sanitize_text):
            # Simulate service method that uses both DI and utilities
            class MockService:
                def __init__(self, repository):
                    self.repository = repository
                
                def create_item(self, name):
                    from core.utils.string_utils import sanitize_text
                    sanitized_name = sanitize_text(name)
                    # Use repository (DI) and utilities together
                    self.repository.create.return_value = {"name": sanitized_name}
                    return self.repository.create({"name": sanitized_name})
            
            service = MockService(project_repo)
            result = service.create_item("test name")
            
            # Verify both DI and utilities were used
            mock_string_utils.sanitize_text.assert_called_once_with("test name")
            project_repo.create.assert_called_once()
            assert result["name"] == "sanitized_text"

    def test_api_route_with_utilities_and_di(self, enhanced_test_client, mock_service_dependencies, mock_pagination_utils):
        """Test API route using both utilities and dependency injection."""
        from fastapi import APIRouter, Depends
        
        router = APIRouter()
        
        # Mock service dependency
        def get_mock_service():
            return mock_service_dependencies['project_service']
        
        @router.get("/projects")
        def list_projects(
            page: int = 1,
            per_page: int = 10,
            service = Depends(get_mock_service)
        ):
            # Use pagination utilities
            with patch('core.utils.pagination_utils.parse_pagination_params', mock_pagination_utils.parse_pagination_params):
                from core.utils.pagination_utils import parse_pagination_params
                pagination_params = parse_pagination_params(page, per_page)
                
                # Use service (DI)
                service.get_projects_list.return_value = {
                    "projects": [],
                    "pagination": {
                        "page": pagination_params.page,
                        "per_page": pagination_params.per_page
                    }
                }
                return service.get_projects_list()
        
        enhanced_test_client.add_router(router)
        enhanced_test_client.override_dependency(get_mock_service, lambda: mock_service_dependencies['project_service'])
        
        response = enhanced_test_client.get("/projects?page=2&per_page=20")
        assert response.status_code == 200
        
        # Verify both utilities and DI were used
        mock_service_dependencies['project_service'].get_projects_list.assert_called_once()


class TestUtilityMockingPatterns:
    """Test patterns for mocking utilities in tests."""

    def test_mock_all_string_utils(self, mock_string_utils):
        """Test mocking all string utility functions."""
        assert mock_string_utils.sanitize_text.return_value == "sanitized_text"
        assert mock_string_utils.slugify.return_value == "test-slug"
        assert mock_string_utils.hash_string.return_value == "hashed_string"
        assert mock_string_utils.truncate_string.return_value == "truncated..."
        assert mock_string_utils.pad_string.return_value == "padded_string"

    def test_mock_all_datetime_utils(self, mock_datetime_utils):
        """Test mocking all datetime utility functions."""
        expected_datetime = datetime(2024, 1, 15, 10, 30, 0, tzinfo=timezone.utc)
        
        assert mock_datetime_utils.utcnow_aware.return_value == expected_datetime
        assert mock_datetime_utils.format_datetime.return_value == "2024-01-15T10:30:00Z"
        assert mock_datetime_utils.parse_datetime.return_value == expected_datetime
        assert mock_datetime_utils.convert_timezone.return_value == expected_datetime
        assert mock_datetime_utils.calculate_time_difference.return_value == 3600

    def test_mock_all_pagination_utils(self, mock_pagination_utils):
        """Test mocking all pagination utility functions."""
        from core.utils.pagination_utils import PaginationParams, SortParams
        
        # Test that classes are available
        assert mock_pagination_utils.PaginationParams == PaginationParams
        assert mock_pagination_utils.SortParams == SortParams
        
        # Test that functions return expected values
        pagination_result = mock_pagination_utils.parse_pagination_params.return_value
        assert pagination_result.page == 1
        assert pagination_result.per_page == 10
        
        sort_result = mock_pagination_utils.parse_sort_params.return_value
        assert sort_result.sort_by == "id"
        assert sort_result.sort_order == "asc"


class TestDIMockingPatterns:
    """Test patterns for mocking dependency injection in tests."""

    def test_mock_repository_dependencies(self, mock_repository_dependencies):
        """Test mocking repository dependencies."""
        # Verify all expected repositories are mocked
        expected_repos = [
            'project_repo', 'user_repo', 'component_repo', 'component_category_repo',
            'heat_tracing_repo', 'imported_data_revision_repo', 'exported_document_repo',
            'calculation_standard_repo'
        ]
        
        for repo_name in expected_repos:
            assert repo_name in mock_repository_dependencies
            assert mock_repository_dependencies[repo_name] is not None

    def test_mock_service_dependencies(self, mock_service_dependencies):
        """Test mocking service dependencies."""
        # Verify all expected services are mocked
        expected_services = [
            'project_service', 'user_service', 'component_service',
            'component_category_service', 'heat_tracing_service', 'document_service'
        ]
        
        for service_name in expected_services:
            assert service_name in mock_service_dependencies
            assert mock_service_dependencies[service_name] is not None
            # Verify service has repository attribute
            assert hasattr(mock_service_dependencies[service_name], 'repository')
