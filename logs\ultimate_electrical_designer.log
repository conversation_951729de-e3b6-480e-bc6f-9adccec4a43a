2025-05-29 20:13:08 - ultimate_electrical_designer.test_module - INFO - Test log message from module logger (<string>:9)
2025-05-29 20:13:08 - ultimate_electrical_designer - INFO - Test log message from app logger (<string>:10)
2025-05-29 20:13:08 - ultimate_electrical_designer.backend.core.database.initialization - INFO - Starting database initialization... (initialization.py:43)
2025-05-29 20:13:08 - ultimate_electrical_designer.backend.core.database.engine - INFO - Creating database engine for environment: development (engine.py:53)
2025-05-29 20:13:08 - ultimate_electrical_designer.backend.core.database.engine - INFO - Database engine created successfully using: SQLite (engine.py:70)
2025-05-29 20:13:08 - ultimate_electrical_designer.backend.core.database.initialization - INFO - Database engine created successfully (initialization.py:48)
2025-05-29 20:13:08 - ultimate_electrical_designer.backend.core.database.initialization - INFO - Session factory initialized (initialization.py:52)
2025-05-29 20:13:08 - ultimate_electrical_designer.backend.core.database.initialization - INFO - Running Alembic migrations... (initialization.py:107)
2025-05-29 20:14:32 - ultimate_electrical_designer.test - INFO - Testing logging configuration (<string>:4)
2025-05-29 20:16:28 - ultimate_electrical_designer.test_module - INFO - Test log message from module logger (final_verification.py:22)
2025-05-29 20:16:28 - ultimate_electrical_designer - INFO - Test log message from app logger (final_verification.py:26)
2025-05-29 20:16:28 - ultimate_electrical_designer.backend.core.database.initialization - INFO - Starting database initialization... (initialization.py:43)
2025-05-29 20:16:28 - ultimate_electrical_designer.backend.core.database.engine - INFO - Creating database engine for environment: development (engine.py:53)
2025-05-29 20:16:28 - ultimate_electrical_designer.backend.core.database.engine - INFO - Database engine created successfully using: SQLite (engine.py:70)
2025-05-29 20:16:28 - ultimate_electrical_designer.backend.core.database.initialization - INFO - Database engine created successfully (initialization.py:48)
2025-05-29 20:16:28 - ultimate_electrical_designer.backend.core.database.initialization - INFO - Session factory initialized (initialization.py:52)
2025-05-29 20:16:28 - ultimate_electrical_designer.backend.core.database.initialization - INFO - Running Alembic migrations... (initialization.py:107)
2025-05-29 20:17:25 - ultimate_electrical_designer.test_module - INFO - Module logger test (<string>:9)
2025-05-29 20:17:25 - ultimate_electrical_designer - INFO - App logger test (<string>:10)
2025-05-29 20:17:26 - ultimate_electrical_designer.backend.core.database.initialization - INFO - Starting database initialization... (initialization.py:43)
2025-05-29 20:17:26 - ultimate_electrical_designer.backend.core.database.engine - INFO - Creating database engine for environment: development (engine.py:53)
2025-05-29 20:17:26 - ultimate_electrical_designer.backend.core.database.engine - INFO - Database engine created successfully using: SQLite (engine.py:70)
2025-05-29 20:17:26 - ultimate_electrical_designer.backend.core.database.initialization - INFO - Database engine created successfully (initialization.py:48)
2025-05-29 20:17:26 - ultimate_electrical_designer.backend.core.database.initialization - INFO - Session factory initialized (initialization.py:52)
2025-05-29 20:17:26 - ultimate_electrical_designer.backend.core.database.initialization - INFO - Running Alembic migrations... (initialization.py:107)
2025-05-29 21:04:41 - ultimate_electrical_designer.backend.core.services.project_service - INFO - Attempting to create new project: 'Test Heat Tracing Project' (HT-TEST-001) (project_service.py:70)
2025-05-29 21:04:41 - ultimate_electrical_designer.backend.core.services.project_service - INFO - Project 'Test Heat Tracing Project' (ID: 1) created successfully (project_service.py:86)
2025-05-29 21:06:20 - ultimate_electrical_designer.backend.core.services.project_service - INFO - Attempting to create new project: 'Test Project' (TEST-001) (project_service.py:72)
2025-05-29 21:06:20 - ultimate_electrical_designer.backend.core.services.project_service - ERROR - Unexpected error creating project 'Test Project': Input validation failed. (project_service.py:133)
Traceback (most recent call last):
  File "D:\Projects\zzz_ultimate-electrical-designer\backend\core\services\project_service.py", line 78, in create_project
    self._validate_project_creation(project_data)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^
  File "D:\Projects\zzz_ultimate-electrical-designer\backend\core\services\project_service.py", line 443, in _validate_project_creation
    raise DataValidationError(
    ...<3 lines>...
    )
backend.core.errors.exceptions.DataValidationError: Input validation failed.
2025-05-29 21:06:44 - ultimate_electrical_designer.backend.core.services.project_service - INFO - Attempting to create new project: 'Test Project' (TEST-001) (project_service.py:72)
2025-05-29 21:06:44 - ultimate_electrical_designer.backend.core.services.project_service - ERROR - Unexpected error creating project 'Test Project': Input validation failed. (project_service.py:133)
Traceback (most recent call last):
  File "D:\Projects\zzz_ultimate-electrical-designer\backend\core\services\project_service.py", line 78, in create_project
    self._validate_project_creation(project_data)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^
  File "D:\Projects\zzz_ultimate-electrical-designer\backend\core\services\project_service.py", line 443, in _validate_project_creation
    raise DataValidationError(
    ...<3 lines>...
    )
backend.core.errors.exceptions.DataValidationError: Input validation failed.
2025-05-29 21:06:57 - ultimate_electrical_designer.backend.core.services.project_service - INFO - Attempting to create new project: 'Test Heat Tracing Project' (HT-TEST-001) (project_service.py:72)
2025-05-29 21:06:57 - ultimate_electrical_designer.backend.core.services.project_service - INFO - Project 'Test Heat Tracing Project' (ID: 1) created successfully (project_service.py:90)
2025-05-29 21:06:57 - ultimate_electrical_designer.backend.core.services.project_service - INFO - Attempting to create new project: 'Test Project' (TEST-001) (project_service.py:72)
2025-05-29 21:06:57 - ultimate_electrical_designer.backend.core.services.project_service - ERROR - Unexpected error creating project 'Test Project': Input validation failed. (project_service.py:133)
Traceback (most recent call last):
  File "D:\Projects\zzz_ultimate-electrical-designer\backend\core\services\project_service.py", line 78, in create_project
    self._validate_project_creation(project_data)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^
  File "D:\Projects\zzz_ultimate-electrical-designer\backend\core\services\project_service.py", line 443, in _validate_project_creation
    raise DataValidationError(
    ...<3 lines>...
    )
backend.core.errors.exceptions.DataValidationError: Input validation failed.
2025-05-29 21:06:57 - ultimate_electrical_designer.backend.core.services.project_service - INFO - Attempting to create new project: 'Test Heat Tracing Project' (HT-TEST-001) (project_service.py:72)
2025-05-29 21:06:57 - ultimate_electrical_designer.backend.core.services.project_service - WARNING - Duplicate project detected: Test Heat Tracing Project or HT-TEST-001 (project_service.py:99)
2025-05-29 21:06:57 - ultimate_electrical_designer.backend.core.services.project_service - WARNING - Project not found: TEST-001 (project_service.py:170)
2025-05-29 21:06:57 - ultimate_electrical_designer.backend.core.services.project_service - INFO - Attempting to update project: 1 (project_service.py:210)
2025-05-29 21:06:57 - ultimate_electrical_designer.backend.core.services.project_service - INFO - Project 'Updated Test Project' (ID: 1) updated successfully (project_service.py:234)
2025-05-29 21:06:57 - ultimate_electrical_designer.backend.core.services.project_service - INFO - Attempting to delete project: 1 (project_service.py:288)
2025-05-29 21:06:57 - ultimate_electrical_designer.backend.core.services.project_service - INFO - Project 'Test Heat Tracing Project' (ID: 1) deleted successfully (project_service.py:308)
2025-06-01 22:34:35 - ultimate_electrical_designer.api.v1.activity_log_routes - INFO - Creating activity log: EventTypeEnum.CREATE (activity_log_routes.py:132)
2025-06-01 22:34:35 - ultimate_electrical_designer.api.v1.activity_log_routes - INFO - Created activity log 1: EventTypeEnum.CREATE (activity_log_routes.py:144)
2025-06-01 22:34:35 - ultimate_electrical_designer.api.v1.activity_log_routes - WARNING - Not found error: Activity log 999 not found (activity_log_routes.py:84)
2025-06-01 22:34:35 - ultimate_electrical_designer.api.v1.activity_log_routes - INFO - Updating activity log 1 (activity_log_routes.py:228)
2025-06-01 22:34:35 - ultimate_electrical_designer.api.v1.activity_log_routes - INFO - Updated activity log 1 (activity_log_routes.py:234)
2025-06-01 22:34:35 - ultimate_electrical_designer.api.v1.activity_log_routes - INFO - Logging security event: event_type=UNAUTHORIZED_ACCESS, user_id=1, severity=HIGH, threat_level=MEDIUM (activity_log_routes.py:398)
2025-06-01 22:34:35 - ultimate_electrical_designer.api.v1.activity_log_routes - INFO - Security event logged successfully: 1 (activity_log_routes.py:424)
2025-06-01 22:34:35 - ultimate_electrical_designer.api.v1.activity_log_routes - INFO - Deleting activity logs older than 365 days (activity_log_routes.py:544)
2025-06-01 22:34:35 - ultimate_electrical_designer.api.v1.activity_log_routes - INFO - Deleted 10 old activity logs (activity_log_routes.py:550)
2025-06-01 22:34:35 - ultimate_electrical_designer.api.v1.activity_log_routes - WARNING - Invalid input error: Invalid input provided (activity_log_routes.py:90)
2025-06-01 22:34:35 - ultimate_electrical_designer.api.v1.activity_log_routes - ERROR - Database error: Database connection failed (activity_log_routes.py:96)
2025-06-01 22:34:45 - ultimate_electrical_designer.backend.api.v1.switchboard_routes - INFO - Creating switchboard: Test Switchboard (switchboard_routes.py:159)
2025-06-01 22:34:45 - ultimate_electrical_designer.backend.api.v1.switchboard_routes - INFO - Created switchboard 1: Test Switchboard (switchboard_routes.py:165)
2025-06-01 22:34:46 - ultimate_electrical_designer.backend.api.v1.switchboard_routes - WARNING - Resource not found: Switchboard 999 not found (switchboard_routes.py:111)
2025-06-01 22:34:46 - ultimate_electrical_designer.backend.api.v1.switchboard_routes - ERROR - Unexpected error: 'Mock' object is not iterable (switchboard_routes.py:135)
Traceback (most recent call last):
  File "D:\Projects\zzz_ultimate-electrical-designer\backend\api\v1\switchboard_routes.py", line 235, in list_switchboards
    SwitchboardSummarySchema.model_validate(sb) for sb in switchboards
                                                          ^^^^^^^^^^^^
TypeError: 'Mock' object is not iterable
2025-06-01 22:34:46 - ultimate_electrical_designer.backend.api.v1.switchboard_routes - INFO - Updating switchboard 1 (switchboard_routes.py:262)
2025-06-01 22:34:46 - ultimate_electrical_designer.backend.api.v1.switchboard_routes - INFO - Updated switchboard 1 (switchboard_routes.py:270)
2025-06-01 22:34:46 - ultimate_electrical_designer.backend.api.v1.switchboard_routes - INFO - Deleting switchboard 1 (switchboard_routes.py:291)
2025-06-01 22:34:46 - ultimate_electrical_designer.backend.api.v1.switchboard_routes - INFO - Deleted switchboard 1 (switchboard_routes.py:297)
2025-06-01 22:34:46 - ultimate_electrical_designer.backend.api.v1.switchboard_routes - INFO - Creating feeder: Test Feeder (switchboard_routes.py:320)
2025-06-01 22:34:46 - ultimate_electrical_designer.backend.api.v1.switchboard_routes - INFO - Created feeder 1: Test Feeder (switchboard_routes.py:326)
2025-06-01 22:34:47 - ultimate_electrical_designer.backend.api.v1.switchboard_routes - INFO - Adding component 15 to switchboard 1 (switchboard_routes.py:414)
2025-06-01 22:34:47 - ultimate_electrical_designer.backend.api.v1.switchboard_routes - INFO - Added component 1 to switchboard (switchboard_routes.py:422)
2025-06-01 22:34:49 - ultimate_electrical_designer.backend.api.v1.user_routes - INFO - Login attempt for: <EMAIL> (user_routes.py:156)
2025-06-01 22:34:49 - ultimate_electrical_designer.backend.api.v1.user_routes - INFO - Login successful for user: 1 (user_routes.py:162)
2025-06-01 22:34:49 - ultimate_electrical_designer.backend.api.v1.user_routes - INFO - Logout request for user: 1 (user_routes.py:181)
2025-06-01 22:34:49 - ultimate_electrical_designer.backend.api.v1.user_routes - INFO - Logout successful for user: 1 (user_routes.py:187)
2025-06-01 22:34:49 - ultimate_electrical_designer.backend.api.v1.user_routes - INFO - Password change request for user: 1 (user_routes.py:206)
2025-06-01 22:34:49 - ultimate_electrical_designer.backend.api.v1.user_routes - INFO - Password changed successfully for user: 1 (user_routes.py:213)
2025-06-01 22:34:49 - ultimate_electrical_designer.backend.api.v1.user_routes - INFO - Creating user: John Smith (user_routes.py:296)
2025-06-01 22:34:49 - ultimate_electrical_designer.backend.api.v1.user_routes - INFO - Created user 1: John Smith (user_routes.py:302)
2025-06-01 22:34:50 - ultimate_electrical_designer.backend.api.v1.user_routes - WARNING - Resource not found: User 999 not found (user_routes.py:97)
2025-06-01 22:34:50 - ultimate_electrical_designer.backend.api.v1.user_routes - ERROR - Unexpected error: 4 validation errors for UserSummarySchema
id
  Input should be a valid integer [type=int_type, input_value=<Mock name='mock.id' id='2367295436656'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/int_type
name
  Input should be a valid string [type=string_type, input_value=<Mock name='mock.name' id='2367295438000'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
email
  Input should be a valid string [type=string_type, input_value=<Mock name='mock.email' id='2367295433296'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
is_active
  Input should be a valid boolean [type=bool_type, input_value=<Mock name='mock.is_active' id='2367295424224'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/bool_type (user_routes.py:132)
Traceback (most recent call last):
  File "D:\Projects\zzz_ultimate-electrical-designer\backend\api\v1\user_routes.py", line 361, in list_users
    user_summaries = [UserSummarySchema.model_validate(user) for user in users]
                      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^
  File "C:\Users\<USER>\Python\Python313\Lib\site-packages\pydantic\main.py", line 703, in model_validate
    return cls.__pydantic_validator__.validate_python(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        obj, strict=strict, from_attributes=from_attributes, context=context, by_alias=by_alias, by_name=by_name
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
pydantic_core._pydantic_core.ValidationError: 4 validation errors for UserSummarySchema
id
  Input should be a valid integer [type=int_type, input_value=<Mock name='mock.id' id='2367295436656'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/int_type
name
  Input should be a valid string [type=string_type, input_value=<Mock name='mock.name' id='2367295438000'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
email
  Input should be a valid string [type=string_type, input_value=<Mock name='mock.email' id='2367295433296'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
is_active
  Input should be a valid boolean [type=bool_type, input_value=<Mock name='mock.is_active' id='2367295424224'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/bool_type
2025-06-01 22:34:50 - ultimate_electrical_designer.backend.api.v1.user_routes - ERROR - Unexpected error: 4 validation errors for UserSummarySchema
id
  Input should be a valid integer [type=int_type, input_value=<Mock name='mock.id' id='2367295432288'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/int_type
name
  Input should be a valid string [type=string_type, input_value=<Mock name='mock.name' id='2367295431952'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
email
  Input should be a valid string [type=string_type, input_value=<Mock name='mock.email' id='2367295431616'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
is_active
  Input should be a valid boolean [type=bool_type, input_value=<Mock name='mock.is_active' id='2367295431280'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/bool_type (user_routes.py:132)
Traceback (most recent call last):
  File "D:\Projects\zzz_ultimate-electrical-designer\backend\api\v1\user_routes.py", line 361, in list_users
    user_summaries = [UserSummarySchema.model_validate(user) for user in users]
                      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^
  File "C:\Users\<USER>\Python\Python313\Lib\site-packages\pydantic\main.py", line 703, in model_validate
    return cls.__pydantic_validator__.validate_python(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        obj, strict=strict, from_attributes=from_attributes, context=context, by_alias=by_alias, by_name=by_name
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
pydantic_core._pydantic_core.ValidationError: 4 validation errors for UserSummarySchema
id
  Input should be a valid integer [type=int_type, input_value=<Mock name='mock.id' id='2367295432288'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/int_type
name
  Input should be a valid string [type=string_type, input_value=<Mock name='mock.name' id='2367295431952'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
email
  Input should be a valid string [type=string_type, input_value=<Mock name='mock.email' id='2367295431616'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
is_active
  Input should be a valid boolean [type=bool_type, input_value=<Mock name='mock.is_active' id='2367295431280'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/bool_type
2025-06-01 22:34:50 - ultimate_electrical_designer.backend.api.v1.user_routes - INFO - Updating user 1 (user_routes.py:387)
2025-06-01 22:34:50 - ultimate_electrical_designer.backend.api.v1.user_routes - INFO - Updated user 1 (user_routes.py:393)
2025-06-01 22:34:50 - ultimate_electrical_designer.backend.api.v1.user_routes - INFO - Deactivating user 1 (user_routes.py:411)
2025-06-01 22:34:50 - ultimate_electrical_designer.backend.api.v1.user_routes - INFO - Deactivated user 1 (user_routes.py:417)
2025-06-01 22:34:51 - ultimate_electrical_designer.backend.api.v1.user_routes - ERROR - Unexpected error: 404: User preferences not found (user_routes.py:132)
Traceback (most recent call last):
  File "D:\Projects\zzz_ultimate-electrical-designer\backend\api\v1\user_routes.py", line 446, in get_user_preferences
    raise HTTPException(
    ...<2 lines>...
    )
fastapi.exceptions.HTTPException: 404: User preferences not found
2025-06-01 22:34:51 - ultimate_electrical_designer.backend.api.v1.user_routes - INFO - Updating preferences for user 1 (user_routes.py:469)
2025-06-01 22:34:51 - ultimate_electrical_designer.backend.api.v1.user_routes - INFO - Updated preferences for user 1 (user_routes.py:477)
2025-06-01 22:34:51 - ultimate_electrical_designer.backend.api.v1.user_routes - INFO - Deleting preferences for user 1 (user_routes.py:498)
2025-06-01 22:34:51 - ultimate_electrical_designer.backend.api.v1.user_routes - INFO - Deleted preferences for user 1 (user_routes.py:504)
2025-06-01 22:34:56 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - ERROR - Database error retrieving electrical nodes for project 1: Database error (electrical_repository.py:107)
2025-06-01 22:34:57 - ultimate_electrical_designer.backend.core.services.activity_log_service - INFO - Event logged successfully: CREATE, log_id=1 (activity_log_service.py:153)
2025-06-01 22:34:57 - ultimate_electrical_designer.backend.core.services.activity_log_service - INFO - Event logged successfully: SYSTEM_START, log_id=2 (activity_log_service.py:153)
2025-06-01 22:34:57 - ultimate_electrical_designer.backend.core.services.activity_log_service - INFO - Event logged successfully: CREATE, log_id=1 (activity_log_service.py:153)
2025-06-01 22:34:57 - ultimate_electrical_designer.backend.core.services.activity_log_service - INFO - Event logged successfully: LOGIN, log_id=1 (activity_log_service.py:153)
2025-06-01 22:34:57 - ultimate_electrical_designer.backend.core.services.activity_log_service - INFO - Event logged successfully: LOGIN_FAILED, log_id=3 (activity_log_service.py:153)
2025-06-01 22:34:57 - ultimate_electrical_designer.backend.core.services.activity_log_service - INFO - Event logged successfully: SYSTEM_START, log_id=1 (activity_log_service.py:153)
2025-06-01 22:34:57 - ultimate_electrical_designer.backend.core.services.activity_log_service - INFO - Event logged successfully: UNAUTHORIZED_ACCESS, log_id=1 (activity_log_service.py:153)
2025-06-01 22:34:57 - ultimate_electrical_designer.backend.core.services.activity_log_service - INFO - Activity log updated successfully: 1 (activity_log_service.py:814)
2025-06-01 22:34:57 - ultimate_electrical_designer.backend.core.services.activity_log_service - INFO - Deleting activity logs older than 365 days (activity_log_service.py:841)
2025-06-01 22:34:57 - ultimate_electrical_designer.backend.core.services.activity_log_service - INFO - Deleted 10 old activity logs (activity_log_service.py:852)
2025-06-01 22:34:57 - ultimate_electrical_designer.backend.core.services.activity_log_service - INFO - Deleting activity logs older than 0 days (activity_log_service.py:841)
2025-06-01 22:34:57 - ultimate_electrical_designer.backend.core.services.activity_log_service - INFO - Generating audit report: type=user_activity, date_range=2025-05-02 22:34:57.915899 to 2025-06-01 22:34:57.915904 (activity_log_service.py:608)
2025-06-01 22:34:57 - ultimate_electrical_designer.backend.core.services.activity_log_service - INFO - Audit report generated successfully: 2e96699c-ce31-4414-bc78-6aef70b9bea1, total_events=1 (activity_log_service.py:665)
2025-06-01 22:34:57 - ultimate_electrical_designer.backend.core.services.activity_log_service - ERROR - Failed to log event CREATE: Database error (activity_log_service.py:163)
Traceback (most recent call last):
  File "D:\Projects\zzz_ultimate-electrical-designer\backend\core\services\activity_log_service.py", line 149, in log_event
    activity_log = self.activity_log_repo.create(log_dict)
  File "C:\Users\<USER>\Python\Python313\Lib\unittest\mock.py", line 1167, in __call__
    return self._mock_call(*args, **kwargs)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Python\Python313\Lib\unittest\mock.py", line 1171, in _mock_call
    return self._execute_mock_call(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Python\Python313\Lib\unittest\mock.py", line 1226, in _execute_mock_call
    raise effect
sqlalchemy.exc.SQLAlchemyError: Database error
2025-06-01 22:34:57 - ultimate_electrical_designer.backend.core.services.activity_log_service - ERROR - Failed to log event CREATE: Repository error (activity_log_service.py:163)
Traceback (most recent call last):
  File "D:\Projects\zzz_ultimate-electrical-designer\backend\core\services\activity_log_service.py", line 149, in log_event
    activity_log = self.activity_log_repo.create(log_dict)
  File "C:\Users\<USER>\Python\Python313\Lib\unittest\mock.py", line 1167, in __call__
    return self._mock_call(*args, **kwargs)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Python\Python313\Lib\unittest\mock.py", line 1171, in _mock_call
    return self._execute_mock_call(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Python\Python313\Lib\unittest\mock.py", line 1226, in _execute_mock_call
    raise effect
Exception: Repository error
2025-06-01 22:34:58 - ultimate_electrical_designer.backend.core.services.electrical_service - INFO - Starting cable sizing calculation for 5.0kW load (electrical_service.py:167)
2025-06-01 22:34:58 - ultimate_electrical_designer.backend.core.services.electrical_service - INFO - Cable sizing calculation completed: HEATING_CABLE_20W, voltage drop: 0.9% (electrical_service.py:204)
2025-06-01 22:34:58 - ultimate_electrical_designer.backend.core.services.electrical_service - INFO - Starting voltage drop calculation for 20.8A load over 100.0m (electrical_service.py:236)
2025-06-01 22:34:58 - ultimate_electrical_designer.backend.core.services.electrical_service - INFO - Voltage drop calculation completed: 0.9%, compliant: True (electrical_service.py:299)
2025-06-01 22:34:58 - ultimate_electrical_designer.backend.core.services.electrical_service - INFO - Starting electrical standards validation for 1 standards (electrical_service.py:330)
2025-06-01 22:34:58 - ultimate_electrical_designer.backend.core.services.electrical_service - INFO - Standards validation completed: compliant=True, violations=0, warnings=1 (electrical_service.py:376)
2025-06-01 22:34:58 - ultimate_electrical_designer.backend.core.services.electrical_service - INFO - Calculating total load for electrical node 1 (electrical_service.py:457)
2025-06-01 22:34:58 - ultimate_electrical_designer.backend.core.services.electrical_service - INFO - Load calculation completed for node 1: 6.0kW diversified load (electrical_service.py:544)
2025-06-01 22:34:58 - ultimate_electrical_designer.backend.core.services.electrical_service - INFO - Calculating total load for electrical node 999 (electrical_service.py:457)
2025-06-01 22:34:58 - ultimate_electrical_designer.backend.core.services.electrical_service - INFO - Starting cable sizing calculation for 5.0kW load (electrical_service.py:167)
2025-06-01 22:34:58 - ultimate_electrical_designer.backend.core.services.electrical_service - ERROR - Cable sizing calculation failed: Calculation failed (electrical_service.py:213)
Traceback (most recent call last):
  File "D:\Projects\zzz_ultimate-electrical-designer\backend\core\services\electrical_service.py", line 183, in perform_cable_sizing_calculation
    calc_result = self.calculation_service.calculate_cable_sizing(calc_input)
  File "C:\Users\<USER>\Python\Python313\Lib\unittest\mock.py", line 1167, in __call__
    return self._mock_call(*args, **kwargs)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Python\Python313\Lib\unittest\mock.py", line 1171, in _mock_call
    return self._execute_mock_call(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Python\Python313\Lib\unittest\mock.py", line 1226, in _execute_mock_call
    raise effect
Exception: Calculation failed
2025-06-01 22:34:58 - ultimate_electrical_designer.backend.core.services.electrical_service - INFO - Starting voltage drop calculation for 20.8A load over 100.0m (electrical_service.py:236)
2025-06-01 22:34:58 - ultimate_electrical_designer.backend.core.services.electrical_service - ERROR - Voltage drop calculation failed: Voltage drop calculation failed (electrical_service.py:308)
Traceback (most recent call last):
  File "D:\Projects\zzz_ultimate-electrical-designer\backend\core\services\electrical_service.py", line 242, in perform_voltage_drop_calculation
    voltage_drop_v = calculate_voltage_drop(
        current=inputs.load_current_a,
    ...<3 lines>...
        cable_reactance=inputs.cable_reactance_ohm_per_m,
    )
  File "C:\Users\<USER>\Python\Python313\Lib\unittest\mock.py", line 1167, in __call__
    return self._mock_call(*args, **kwargs)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Python\Python313\Lib\unittest\mock.py", line 1171, in _mock_call
    return self._execute_mock_call(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Python\Python313\Lib\unittest\mock.py", line 1226, in _execute_mock_call
    raise effect
Exception: Voltage drop calculation failed
2025-06-01 22:34:58 - ultimate_electrical_designer.backend.core.services.heat_tracing_service - INFO - Attempting to create new pipe: 'Test Pipe 001' in project 1 (heat_tracing_service.py:145)
2025-06-01 22:34:58 - ultimate_electrical_designer.backend.core.services.heat_tracing_service - INFO - Pipe 'Test Pipe 001' (ID: 1) created successfully (heat_tracing_service.py:163)
2025-06-01 22:34:58 - ultimate_electrical_designer.backend.core.services.heat_tracing_service - INFO - Attempting to create new pipe: 'Test Pipe 001' in project 1 (heat_tracing_service.py:145)
2025-06-01 22:34:58 - ultimate_electrical_designer.backend.core.services.heat_tracing_service - WARNING - Duplicate pipe detected: Test Pipe 001 (heat_tracing_service.py:172)
2025-06-01 22:34:58 - ultimate_electrical_designer.backend.core.services.heat_tracing_service - WARNING - Pipe not found: 999 (heat_tracing_service.py:207)
2025-06-01 22:34:58 - ultimate_electrical_designer.backend.core.services.heat_tracing_service - WARNING - Pipe not found: 1 (heat_tracing_service.py:207)
2025-06-01 22:34:58 - ultimate_electrical_designer.backend.core.services.heat_tracing_service - INFO - Attempting to update pipe: 1 (heat_tracing_service.py:242)
2025-06-01 22:34:58 - ultimate_electrical_designer.backend.core.services.heat_tracing_service - INFO - Pipe 'Updated Pipe Name' (ID: 1) updated successfully (heat_tracing_service.py:270)
2025-06-01 22:34:58 - ultimate_electrical_designer.backend.core.services.heat_tracing_service - INFO - Attempting to update pipe: 999 (heat_tracing_service.py:242)
2025-06-01 22:34:58 - ultimate_electrical_designer.backend.core.services.heat_tracing_service - INFO - Attempting to delete pipe: 1 (heat_tracing_service.py:307)
2025-06-01 22:34:58 - ultimate_electrical_designer.backend.core.services.heat_tracing_service - INFO - Pipe 'Test Pipe 001' (ID: 1) deleted successfully (heat_tracing_service.py:327)
2025-06-01 22:34:58 - ultimate_electrical_designer.backend.core.services.heat_tracing_service - INFO - Attempting to create new vessel: 'Test Vessel T-001' in project 1 (heat_tracing_service.py:419)
2025-06-01 22:34:58 - ultimate_electrical_designer.backend.core.services.heat_tracing_service - INFO - Vessel 'Test Vessel T-001' (ID: 1) created successfully (heat_tracing_service.py:437)
2025-06-01 22:34:58 - ultimate_electrical_designer.backend.core.services.heat_tracing_service - INFO - Calculating heat loss for pipe 1 (heat_tracing_service.py:582)
2025-06-01 22:34:58 - ultimate_electrical_designer.backend.core.services.heat_tracing_service - INFO - Calculating heat loss for pipe 999 (heat_tracing_service.py:582)
2025-06-01 22:34:58 - ultimate_electrical_designer.backend.core.services.heat_tracing_service - INFO - Validating standards compliance (heat_tracing_service.py:644)
2025-06-01 22:34:58 - ultimate_electrical_designer.backend.core.services.heat_tracing_service - INFO - Executing heat tracing design workflow for project 1 (heat_tracing_service.py:691)
2025-06-01 22:34:58 - ultimate_electrical_designer.backend.core.services.heat_tracing_service - WARNING - Failed to design heat tracing for pipe 1: 24 validation errors for PipeReadSchema
created_at
  Input should be a valid datetime [type=datetime_type, input_value=<Mock name='mock.pipes.ge..._at' id='2367303370128'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/datetime_type
updated_at
  Input should be a valid datetime [type=datetime_type, input_value=<Mock name='mock.pipes.ge..._at' id='2367303383904'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/datetime_type
is_deleted
  Input should be a valid boolean [type=bool_type, input_value=<Mock name='mock.pipes.ge...ted' id='2367303375840'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/bool_type
deleted_at
  Input should be a valid datetime [type=datetime_type, input_value=<Mock name='mock.pipes.ge..._at' id='2367303384240'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/datetime_type
deleted_by_user_id
  Input should be a valid integer [type=int_type, input_value=<Mock name='mock.pipes.ge..._id' id='2367303374832'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/int_type
nominal_diameter_mm
  Input should be a valid number [type=float_type, input_value=<Mock name='mock.pipes.ge..._mm' id='2367303370800'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/float_type
wall_thickness_mm
  Input should be a valid number [type=float_type, input_value=<Mock name='mock.pipes.ge..._mm' id='2367303371136'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/float_type
fluid_type
  Input should be a valid string [type=string_type, input_value=<Mock name='mock.pipes.ge...ype' id='2367303379200'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
specific_heat_capacity_jkgc
  Input should be a valid number [type=float_type, input_value=<Mock name='mock.pipes.ge...kgc' id='2367303384576'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/float_type
viscosity_cp
  Input should be a valid number [type=float_type, input_value=<Mock name='mock.pipes.ge..._cp' id='2367303382224'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/float_type
freezing_point_c
  Input should be a valid number [type=float_type, input_value=<Mock name='mock.pipes.ge...t_c' id='2367303381888'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/float_type
safety_margin_percent
  Input should be a valid number [type=float_type, input_value=<Mock name='mock.pipes.ge...ent' id='2367303383232'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/float_type
pid
  Input should be a valid string [type=string_type, input_value=<Mock name='mock.pipes.ge...pid' id='2367303378192'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
line_tag
  Input should be a valid string [type=string_type, input_value=<Mock name='mock.pipes.ge...tag' id='2367303378528'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
from_location
  Input should be a valid string [type=string_type, input_value=<Mock name='mock.pipes.ge...ion' id='2367303375168'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
to_location
  Input should be a valid string [type=string_type, input_value=<Mock name='mock.pipes.ge...ion' id='2367303376848'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
valve_count
  Input should be a valid integer [type=int_type, input_value=<Mock name='mock.pipes.ge...unt' id='2367303376512'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/int_type
support_count
  Input should be a valid integer [type=int_type, input_value=<Mock name='mock.pipes.ge...unt' id='2367303378864'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/int_type
project_id
  Input should be a valid integer [type=int_type, input_value=<Mock name='mock.pipes.ge..._id' id='2367303377184'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/int_type
pipe_material_id
  Input should be a valid integer [type=int_type, input_value=<Mock name='mock.pipes.ge..._id' id='2367303381552'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/int_type
insulation_material_id
  Input should be a valid integer [type=int_type, input_value=<Mock name='mock.pipes.ge..._id' id='2367303372480'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/int_type
calculated_heat_loss_wm
  Input should be a valid number [type=float_type, input_value=<Mock name='mock.pipes.ge..._wm' id='2367303376176'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/float_type
required_heat_output_wm
  Input should be a valid number [type=float_type, input_value=<Mock name='mock.pipes.ge..._wm' id='2367295428256'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/float_type
imported_data_revision_id
  Input should be a valid integer [type=int_type, input_value=<Mock name='mock.pipes.ge..._id' id='2367295438336'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/int_type (heat_tracing_service.py:720)
2025-06-01 22:34:58 - ultimate_electrical_designer.backend.core.services.heat_tracing_service - WARNING - Failed to design heat tracing for pipe 2: 24 validation errors for PipeReadSchema
created_at
  Input should be a valid datetime [type=datetime_type, input_value=<Mock name='mock.pipes.ge..._at' id='2367303370128'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/datetime_type
updated_at
  Input should be a valid datetime [type=datetime_type, input_value=<Mock name='mock.pipes.ge..._at' id='2367303383904'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/datetime_type
is_deleted
  Input should be a valid boolean [type=bool_type, input_value=<Mock name='mock.pipes.ge...ted' id='2367303375840'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/bool_type
deleted_at
  Input should be a valid datetime [type=datetime_type, input_value=<Mock name='mock.pipes.ge..._at' id='2367303384240'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/datetime_type
deleted_by_user_id
  Input should be a valid integer [type=int_type, input_value=<Mock name='mock.pipes.ge..._id' id='2367303374832'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/int_type
nominal_diameter_mm
  Input should be a valid number [type=float_type, input_value=<Mock name='mock.pipes.ge..._mm' id='2367303370800'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/float_type
wall_thickness_mm
  Input should be a valid number [type=float_type, input_value=<Mock name='mock.pipes.ge..._mm' id='2367303371136'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/float_type
fluid_type
  Input should be a valid string [type=string_type, input_value=<Mock name='mock.pipes.ge...ype' id='2367303379200'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
specific_heat_capacity_jkgc
  Input should be a valid number [type=float_type, input_value=<Mock name='mock.pipes.ge...kgc' id='2367303384576'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/float_type
viscosity_cp
  Input should be a valid number [type=float_type, input_value=<Mock name='mock.pipes.ge..._cp' id='2367303382224'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/float_type
freezing_point_c
  Input should be a valid number [type=float_type, input_value=<Mock name='mock.pipes.ge...t_c' id='2367303381888'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/float_type
safety_margin_percent
  Input should be a valid number [type=float_type, input_value=<Mock name='mock.pipes.ge...ent' id='2367303383232'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/float_type
pid
  Input should be a valid string [type=string_type, input_value=<Mock name='mock.pipes.ge...pid' id='2367303378192'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
line_tag
  Input should be a valid string [type=string_type, input_value=<Mock name='mock.pipes.ge...tag' id='2367303378528'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
from_location
  Input should be a valid string [type=string_type, input_value=<Mock name='mock.pipes.ge...ion' id='2367303375168'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
to_location
  Input should be a valid string [type=string_type, input_value=<Mock name='mock.pipes.ge...ion' id='2367303376848'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
valve_count
  Input should be a valid integer [type=int_type, input_value=<Mock name='mock.pipes.ge...unt' id='2367303376512'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/int_type
support_count
  Input should be a valid integer [type=int_type, input_value=<Mock name='mock.pipes.ge...unt' id='2367303378864'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/int_type
project_id
  Input should be a valid integer [type=int_type, input_value=<Mock name='mock.pipes.ge..._id' id='2367303377184'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/int_type
pipe_material_id
  Input should be a valid integer [type=int_type, input_value=<Mock name='mock.pipes.ge..._id' id='2367303381552'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/int_type
insulation_material_id
  Input should be a valid integer [type=int_type, input_value=<Mock name='mock.pipes.ge..._id' id='2367303372480'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/int_type
calculated_heat_loss_wm
  Input should be a valid number [type=float_type, input_value=<Mock name='mock.pipes.ge..._wm' id='2367303376176'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/float_type
required_heat_output_wm
  Input should be a valid number [type=float_type, input_value=<Mock name='mock.pipes.ge..._wm' id='2367295428256'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/float_type
imported_data_revision_id
  Input should be a valid integer [type=int_type, input_value=<Mock name='mock.pipes.ge..._id' id='2367295438336'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/int_type (heat_tracing_service.py:720)
2025-06-01 22:34:58 - ultimate_electrical_designer.backend.core.services.heat_tracing_service - INFO - Heat tracing design workflow completed for project 1 (heat_tracing_service.py:749)
2025-06-01 22:34:58 - ultimate_electrical_designer.backend.core.services.project_service - INFO - Attempting to create new project: 'Test Project' (TEST-001) (project_service.py:72)
2025-06-01 22:34:58 - ultimate_electrical_designer.backend.core.services.project_service - ERROR - Unexpected error creating project 'Test Project': Input validation failed. (project_service.py:133)
Traceback (most recent call last):
  File "D:\Projects\zzz_ultimate-electrical-designer\backend\core\services\project_service.py", line 78, in create_project
    self._validate_project_creation(project_data)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^
  File "D:\Projects\zzz_ultimate-electrical-designer\backend\core\services\project_service.py", line 443, in _validate_project_creation
    raise DataValidationError(
    ...<3 lines>...
    )
backend.core.errors.exceptions.DataValidationError: Input validation failed.
2025-06-01 22:34:58 - ultimate_electrical_designer.backend.core.services.project_service - WARNING - Project not found: TEST-001 (project_service.py:170)
2025-06-01 22:34:58 - ultimate_electrical_designer.backend.core.services.project_service - INFO - Attempting to update project: 1 (project_service.py:210)
2025-06-01 22:34:58 - ultimate_electrical_designer.backend.core.services.project_service - INFO - Project 'Updated Test Project' (ID: 1) updated successfully (project_service.py:234)
2025-06-01 22:34:58 - ultimate_electrical_designer.backend.core.services.project_service - INFO - Attempting to delete project: 1 (project_service.py:288)
2025-06-01 22:34:58 - ultimate_electrical_designer.backend.core.services.project_service - INFO - Project 'Test Heat Tracing Project' (ID: 1) deleted successfully (project_service.py:308)
2025-06-01 22:34:58 - ultimate_electrical_designer.backend.core.services.switchboard_service - INFO - Creating switchboard: Test Switchboard (switchboard_service.py:132)
2025-06-01 22:34:58 - ultimate_electrical_designer.backend.core.services.switchboard_service - INFO - Switchboard created successfully: 1 (switchboard_service.py:150)
2025-06-01 22:34:58 - ultimate_electrical_designer.backend.core.services.switchboard_service - ERROR - Failed to create switchboard: 10 validation errors for SwitchboardReadSchema
created_at
  Input should be a valid datetime [type=datetime_type, input_value=<Mock name='mock().create..._at' id='2367295433632'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/datetime_type
updated_at
  Input should be a valid datetime [type=datetime_type, input_value=<Mock name='mock().create..._at' id='2367295431280'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/datetime_type
is_deleted
  Input should be a valid boolean [type=bool_type, input_value=<Mock name='mock().create...ted' id='2367295422880'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/bool_type
deleted_at
  Input should be a valid datetime [type=datetime_type, input_value=<Mock name='mock().create..._at' id='2367295438336'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/datetime_type
deleted_by_user_id
  Input should be a valid integer [type=int_type, input_value=<Mock name='mock().create..._id' id='2367295428256'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/int_type
location
  Input should be a valid string [type=string_type, input_value=<Mock name='mock().create...ion' id='2367295437328'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
voltage_level_v
  Input should be a valid integer [type=int_type, input_value=<Mock name='mock().create...l_v' id='2367295432288'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/int_type
number_of_phases
  Input should be a valid integer [type=int_type, input_value=<Mock name='mock().create...ses' id='2367295427584'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/int_type
type
  Input should be 'Main' or 'Sub-distribution' [type=enum, input_value=<Mock name='mock().create...ype' id='2367295427920'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/enum
project_id
  Input should be a valid integer [type=int_type, input_value=<Mock name='mock().create..._id' id='2367295433296'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/int_type (switchboard_service.py:158)
Traceback (most recent call last):
  File "D:\Projects\zzz_ultimate-electrical-designer\backend\core\services\switchboard_service.py", line 151, in create_switchboard
    return SwitchboardReadSchema.model_validate(switchboard)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Python\Python313\Lib\site-packages\pydantic\main.py", line 703, in model_validate
    return cls.__pydantic_validator__.validate_python(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        obj, strict=strict, from_attributes=from_attributes, context=context, by_alias=by_alias, by_name=by_name
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
pydantic_core._pydantic_core.ValidationError: 10 validation errors for SwitchboardReadSchema
created_at
  Input should be a valid datetime [type=datetime_type, input_value=<Mock name='mock().create..._at' id='2367295433632'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/datetime_type
updated_at
  Input should be a valid datetime [type=datetime_type, input_value=<Mock name='mock().create..._at' id='2367295431280'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/datetime_type
is_deleted
  Input should be a valid boolean [type=bool_type, input_value=<Mock name='mock().create...ted' id='2367295422880'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/bool_type
deleted_at
  Input should be a valid datetime [type=datetime_type, input_value=<Mock name='mock().create..._at' id='2367295438336'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/datetime_type
deleted_by_user_id
  Input should be a valid integer [type=int_type, input_value=<Mock name='mock().create..._id' id='2367295428256'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/int_type
location
  Input should be a valid string [type=string_type, input_value=<Mock name='mock().create...ion' id='2367295437328'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
voltage_level_v
  Input should be a valid integer [type=int_type, input_value=<Mock name='mock().create...l_v' id='2367295432288'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/int_type
number_of_phases
  Input should be a valid integer [type=int_type, input_value=<Mock name='mock().create...ses' id='2367295427584'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/int_type
type
  Input should be 'Main' or 'Sub-distribution' [type=enum, input_value=<Mock name='mock().create...ype' id='2367295427920'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/enum
project_id
  Input should be a valid integer [type=int_type, input_value=<Mock name='mock().create..._id' id='2367295433296'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/int_type
2025-06-01 22:34:58 - ultimate_electrical_designer.backend.core.services.switchboard_service - INFO - Updating switchboard: 1 (switchboard_service.py:205)
2025-06-01 22:34:58 - ultimate_electrical_designer.backend.core.services.switchboard_service - INFO - Switchboard updated successfully: 1 (switchboard_service.py:232)
2025-06-01 22:34:58 - ultimate_electrical_designer.backend.core.services.switchboard_service - ERROR - Failed to update switchboard 1: 10 validation errors for SwitchboardReadSchema
created_at
  Input should be a valid datetime [type=datetime_type, input_value=<Mock name='mock().update..._at' id='2367302292480'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/datetime_type
updated_at
  Input should be a valid datetime [type=datetime_type, input_value=<Mock name='mock().update..._at' id='2367302292816'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/datetime_type
is_deleted
  Input should be a valid boolean [type=bool_type, input_value=<Mock name='mock().update...ted' id='2367302293152'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/bool_type
deleted_at
  Input should be a valid datetime [type=datetime_type, input_value=<Mock name='mock().update..._at' id='2367302293488'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/datetime_type
deleted_by_user_id
  Input should be a valid integer [type=int_type, input_value=<Mock name='mock().update..._id' id='2367302293824'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/int_type
location
  Input should be a valid string [type=string_type, input_value=<Mock name='mock().update...ion' id='2367302294160'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
voltage_level_v
  Input should be a valid integer [type=int_type, input_value=<Mock name='mock().update...l_v' id='2367302294496'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/int_type
number_of_phases
  Input should be a valid integer [type=int_type, input_value=<Mock name='mock().update...ses' id='2367302294832'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/int_type
type
  Input should be 'Main' or 'Sub-distribution' [type=enum, input_value=<Mock name='mock().update...ype' id='2367302295168'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/enum
project_id
  Input should be a valid integer [type=int_type, input_value=<Mock name='mock().update..._id' id='2367302295504'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/int_type (switchboard_service.py:240)
Traceback (most recent call last):
  File "D:\Projects\zzz_ultimate-electrical-designer\backend\core\services\switchboard_service.py", line 233, in update_switchboard
    return SwitchboardReadSchema.model_validate(updated_switchboard)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Python\Python313\Lib\site-packages\pydantic\main.py", line 703, in model_validate
    return cls.__pydantic_validator__.validate_python(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        obj, strict=strict, from_attributes=from_attributes, context=context, by_alias=by_alias, by_name=by_name
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
pydantic_core._pydantic_core.ValidationError: 10 validation errors for SwitchboardReadSchema
created_at
  Input should be a valid datetime [type=datetime_type, input_value=<Mock name='mock().update..._at' id='2367302292480'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/datetime_type
updated_at
  Input should be a valid datetime [type=datetime_type, input_value=<Mock name='mock().update..._at' id='2367302292816'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/datetime_type
is_deleted
  Input should be a valid boolean [type=bool_type, input_value=<Mock name='mock().update...ted' id='2367302293152'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/bool_type
deleted_at
  Input should be a valid datetime [type=datetime_type, input_value=<Mock name='mock().update..._at' id='2367302293488'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/datetime_type
deleted_by_user_id
  Input should be a valid integer [type=int_type, input_value=<Mock name='mock().update..._id' id='2367302293824'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/int_type
location
  Input should be a valid string [type=string_type, input_value=<Mock name='mock().update...ion' id='2367302294160'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
voltage_level_v
  Input should be a valid integer [type=int_type, input_value=<Mock name='mock().update...l_v' id='2367302294496'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/int_type
number_of_phases
  Input should be a valid integer [type=int_type, input_value=<Mock name='mock().update...ses' id='2367302294832'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/int_type
type
  Input should be 'Main' or 'Sub-distribution' [type=enum, input_value=<Mock name='mock().update...ype' id='2367302295168'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/enum
project_id
  Input should be a valid integer [type=int_type, input_value=<Mock name='mock().update..._id' id='2367302295504'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/int_type
2025-06-01 22:34:58 - ultimate_electrical_designer.backend.core.services.switchboard_service - INFO - Deleting switchboard: 1 (switchboard_service.py:264)
2025-06-01 22:34:58 - ultimate_electrical_designer.backend.core.services.switchboard_service - INFO - Switchboard deleted successfully: 1 (switchboard_service.py:272)
2025-06-01 22:34:58 - ultimate_electrical_designer.backend.core.services.switchboard_service - INFO - Deleting switchboard: 999 (switchboard_service.py:264)
2025-06-01 22:34:58 - ultimate_electrical_designer.backend.core.services.switchboard_service - INFO - Creating feeder: Test Feeder (switchboard_service.py:330)
2025-06-01 22:34:58 - ultimate_electrical_designer.backend.core.services.switchboard_service - INFO - Feeder created successfully: 1 (switchboard_service.py:347)
2025-06-01 22:34:58 - ultimate_electrical_designer.backend.core.services.switchboard_service - ERROR - Failed to create feeder: 6 validation errors for FeederReadSchema
created_at
  Input should be a valid datetime [type=datetime_type, input_value=<Mock name='mock().create..._at' id='2367302289792'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/datetime_type
updated_at
  Input should be a valid datetime [type=datetime_type, input_value=<Mock name='mock().create..._at' id='2367302287776'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/datetime_type
is_deleted
  Input should be a valid boolean [type=bool_type, input_value=<Mock name='mock().create...ted' id='2367302289456'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/bool_type
deleted_at
  Input should be a valid datetime [type=datetime_type, input_value=<Mock name='mock().create..._at' id='2367302287440'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/datetime_type
deleted_by_user_id
  Input should be a valid integer [type=int_type, input_value=<Mock name='mock().create..._id' id='2367302289120'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/int_type
switchboard_id
  Input should be a valid integer [type=int_type, input_value=<Mock name='mock().create..._id' id='2367302288784'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/int_type (switchboard_service.py:355)
Traceback (most recent call last):
  File "D:\Projects\zzz_ultimate-electrical-designer\backend\core\services\switchboard_service.py", line 348, in create_feeder
    return FeederReadSchema.model_validate(feeder)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^
  File "C:\Users\<USER>\Python\Python313\Lib\site-packages\pydantic\main.py", line 703, in model_validate
    return cls.__pydantic_validator__.validate_python(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        obj, strict=strict, from_attributes=from_attributes, context=context, by_alias=by_alias, by_name=by_name
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
pydantic_core._pydantic_core.ValidationError: 6 validation errors for FeederReadSchema
created_at
  Input should be a valid datetime [type=datetime_type, input_value=<Mock name='mock().create..._at' id='2367302289792'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/datetime_type
updated_at
  Input should be a valid datetime [type=datetime_type, input_value=<Mock name='mock().create..._at' id='2367302287776'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/datetime_type
is_deleted
  Input should be a valid boolean [type=bool_type, input_value=<Mock name='mock().create...ted' id='2367302289456'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/bool_type
deleted_at
  Input should be a valid datetime [type=datetime_type, input_value=<Mock name='mock().create..._at' id='2367302287440'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/datetime_type
deleted_by_user_id
  Input should be a valid integer [type=int_type, input_value=<Mock name='mock().create..._id' id='2367302289120'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/int_type
switchboard_id
  Input should be a valid integer [type=int_type, input_value=<Mock name='mock().create..._id' id='2367302288784'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/int_type
2025-06-01 22:34:58 - ultimate_electrical_designer.backend.core.services.switchboard_service - INFO - Creating feeder: Test Feeder (switchboard_service.py:330)
2025-06-01 22:34:58 - ultimate_electrical_designer.backend.core.services.switchboard_service - INFO - Adding component 15 to switchboard 1 (switchboard_service.py:424)
2025-06-01 22:34:58 - ultimate_electrical_designer.backend.core.services.switchboard_service - INFO - Component added to switchboard successfully: 1 (switchboard_service.py:460)
2025-06-01 22:34:58 - ultimate_electrical_designer.backend.core.services.switchboard_service - ERROR - Failed to add component to switchboard: 4 validation errors for SwitchboardComponentReadSchema
quantity
  Input should be a valid integer [type=int_type, input_value=<Mock name='mock().create...ity' id='2367302299872'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/int_type
position
  Input should be a valid string [type=string_type, input_value=<Mock name='mock().create...ion' id='2367302300208'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
switchboard_id
  Input should be a valid integer [type=int_type, input_value=<Mock name='mock().create..._id' id='2367302300544'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/int_type
component_id
  Input should be a valid integer [type=int_type, input_value=<Mock name='mock().create..._id' id='2367302300880'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/int_type (switchboard_service.py:470)
Traceback (most recent call last):
  File "D:\Projects\zzz_ultimate-electrical-designer\backend\core\services\switchboard_service.py", line 463, in add_switchboard_component
    return SwitchboardComponentReadSchema.model_validate(switchboard_component)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Python\Python313\Lib\site-packages\pydantic\main.py", line 703, in model_validate
    return cls.__pydantic_validator__.validate_python(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        obj, strict=strict, from_attributes=from_attributes, context=context, by_alias=by_alias, by_name=by_name
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
pydantic_core._pydantic_core.ValidationError: 4 validation errors for SwitchboardComponentReadSchema
quantity
  Input should be a valid integer [type=int_type, input_value=<Mock name='mock().create...ity' id='2367302299872'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/int_type
position
  Input should be a valid string [type=string_type, input_value=<Mock name='mock().create...ion' id='2367302300208'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
switchboard_id
  Input should be a valid integer [type=int_type, input_value=<Mock name='mock().create..._id' id='2367302300544'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/int_type
component_id
  Input should be a valid integer [type=int_type, input_value=<Mock name='mock().create..._id' id='2367302300880'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/int_type
2025-06-01 22:34:58 - ultimate_electrical_designer.backend.core.services.switchboard_service - INFO - Adding component 15 to switchboard 999 (switchboard_service.py:424)
2025-06-01 22:34:58 - ultimate_electrical_designer.backend.core.services.switchboard_service - INFO - Adding component 999 to switchboard 1 (switchboard_service.py:424)
2025-06-01 22:34:58 - ultimate_electrical_designer.backend.core.services.user_service - INFO - Creating user: John Smith (user_service.py:126)
2025-06-01 22:34:59 - ultimate_electrical_designer.backend.core.services.user_service - INFO - User created successfully: 1 (user_service.py:149)
2025-06-01 22:34:59 - ultimate_electrical_designer.backend.core.services.user_service - ERROR - Failed to create user: 1 validation error for UserReadSchema
is_active
  Input should be a valid boolean [type=bool_type, input_value=<Mock name='mock().create...ive' id='2367299064496'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/bool_type (user_service.py:157)
Traceback (most recent call last):
  File "D:\Projects\zzz_ultimate-electrical-designer\backend\core\services\user_service.py", line 150, in create_user
    return UserReadSchema.model_validate(user)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^
  File "C:\Users\<USER>\Python\Python313\Lib\site-packages\pydantic\main.py", line 703, in model_validate
    return cls.__pydantic_validator__.validate_python(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        obj, strict=strict, from_attributes=from_attributes, context=context, by_alias=by_alias, by_name=by_name
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
pydantic_core._pydantic_core.ValidationError: 1 validation error for UserReadSchema
is_active
  Input should be a valid boolean [type=bool_type, input_value=<Mock name='mock().create...ive' id='2367299064496'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/bool_type
2025-06-01 22:34:59 - ultimate_electrical_designer.backend.core.services.user_service - INFO - Creating user: John Smith (user_service.py:126)
2025-06-01 22:34:59 - ultimate_electrical_designer.backend.core.services.user_service - INFO - Login attempt for: <EMAIL> (user_service.py:407)
2025-06-01 22:34:59 - ultimate_electrical_designer.backend.core.services.user_service - INFO - Login attempt for: <EMAIL> (user_service.py:407)
2025-06-01 22:34:59 - ultimate_electrical_designer.backend.core.services.user_service - INFO - Updating user: 1 (user_service.py:228)
2025-06-01 22:34:59 - ultimate_electrical_designer.backend.core.services.user_service - INFO - User updated successfully: 1 (user_service.py:252)
2025-06-01 22:34:59 - ultimate_electrical_designer.backend.core.services.user_service - ERROR - Failed to update user 1: 2 validation errors for UserReadSchema
email
  Input should be a valid string [type=string_type, input_value=<Mock name='mock().update...ail' id='2367299097264'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
is_active
  Input should be a valid boolean [type=bool_type, input_value=<Mock name='mock().update...ive' id='2367299102304'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/bool_type (user_service.py:260)
Traceback (most recent call last):
  File "D:\Projects\zzz_ultimate-electrical-designer\backend\core\services\user_service.py", line 253, in update_user
    return UserReadSchema.model_validate(updated_user)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Python\Python313\Lib\site-packages\pydantic\main.py", line 703, in model_validate
    return cls.__pydantic_validator__.validate_python(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        obj, strict=strict, from_attributes=from_attributes, context=context, by_alias=by_alias, by_name=by_name
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
pydantic_core._pydantic_core.ValidationError: 2 validation errors for UserReadSchema
email
  Input should be a valid string [type=string_type, input_value=<Mock name='mock().update...ail' id='2367299097264'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
is_active
  Input should be a valid boolean [type=bool_type, input_value=<Mock name='mock().update...ive' id='2367299102304'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/bool_type
2025-06-01 22:34:59 - ultimate_electrical_designer.backend.core.services.user_service - INFO - Deactivating user: 1 (user_service.py:279)
2025-06-01 22:34:59 - ultimate_electrical_designer.backend.core.services.user_service - INFO - User deactivated successfully: 1 (user_service.py:285)
2025-06-01 22:34:59 - ultimate_electrical_designer.backend.core.services.user_service - INFO - Creating/updating preferences for user: 1 (user_service.py:584)
2025-06-01 22:34:59 - ultimate_electrical_designer.backend.core.services.user_service - INFO - Preferences updated successfully for user: 1 (user_service.py:603)
2025-06-01 22:34:59 - ultimate_electrical_designer.backend.core.services.user_service - ERROR - Failed to update preferences for user 1: 14 validation errors for UserPreferenceReadSchema
id
  Input should be a valid integer [type=int_type, input_value=<Mock name='mock().create....id' id='2367299065840'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/int_type
created_at
  Input should be a valid datetime [type=datetime_type, input_value=<Mock name='mock().create..._at' id='2367299066176'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/datetime_type
updated_at
  Input should be a valid datetime [type=datetime_type, input_value=<Mock name='mock().create..._at' id='2367299066512'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/datetime_type
is_deleted
  Input should be a valid boolean [type=bool_type, input_value=<Mock name='mock().create...ted' id='2367299066848'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/bool_type
deleted_at
  Input should be a valid datetime [type=datetime_type, input_value=<Mock name='mock().create..._at' id='2367299067184'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/datetime_type
deleted_by_user_id
  Input should be a valid integer [type=int_type, input_value=<Mock name='mock().create..._id' id='2367299067520'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/int_type
ui_theme
  Input should be a valid string [type=string_type, input_value=<Mock name='mock().create...eme' id='2367299067856'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
default_min_ambient_temp_c
  Input should be a valid number [type=float_type, input_value=<Mock name='mock().create...p_c' id='2367299068192'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/float_type
default_max_ambient_temp_c
  Input should be a valid number [type=float_type, input_value=<Mock name='mock().create...p_c' id='2367299068528'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/float_type
default_desired_maintenance_temp_c
  Input should be a valid number [type=float_type, input_value=<Mock name='mock().create...p_c' id='2367299068864'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/float_type
default_safety_margin_percent
  Input should be a valid number [type=float_type, input_value=<Mock name='mock().create...ent' id='2367299069200'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/float_type
preferred_cable_manufacturers_json
  Input should be a valid string [type=string_type, input_value=<Mock name='mock().create...son' id='2367299069536'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
preferred_control_device_manufacturers_json
  Input should be a valid string [type=string_type, input_value=<Mock name='mock().create...son' id='2367299069872'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
user_id
  Input should be a valid integer [type=int_type, input_value=<Mock name='mock().create..._id' id='2367299070208'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/int_type (user_service.py:611)
Traceback (most recent call last):
  File "D:\Projects\zzz_ultimate-electrical-designer\backend\core\services\user_service.py", line 604, in create_or_update_user_preferences
    return UserPreferenceReadSchema.model_validate(preferences)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Python\Python313\Lib\site-packages\pydantic\main.py", line 703, in model_validate
    return cls.__pydantic_validator__.validate_python(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        obj, strict=strict, from_attributes=from_attributes, context=context, by_alias=by_alias, by_name=by_name
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
pydantic_core._pydantic_core.ValidationError: 14 validation errors for UserPreferenceReadSchema
id
  Input should be a valid integer [type=int_type, input_value=<Mock name='mock().create....id' id='2367299065840'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/int_type
created_at
  Input should be a valid datetime [type=datetime_type, input_value=<Mock name='mock().create..._at' id='2367299066176'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/datetime_type
updated_at
  Input should be a valid datetime [type=datetime_type, input_value=<Mock name='mock().create..._at' id='2367299066512'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/datetime_type
is_deleted
  Input should be a valid boolean [type=bool_type, input_value=<Mock name='mock().create...ted' id='2367299066848'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/bool_type
deleted_at
  Input should be a valid datetime [type=datetime_type, input_value=<Mock name='mock().create..._at' id='2367299067184'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/datetime_type
deleted_by_user_id
  Input should be a valid integer [type=int_type, input_value=<Mock name='mock().create..._id' id='2367299067520'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/int_type
ui_theme
  Input should be a valid string [type=string_type, input_value=<Mock name='mock().create...eme' id='2367299067856'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
default_min_ambient_temp_c
  Input should be a valid number [type=float_type, input_value=<Mock name='mock().create...p_c' id='2367299068192'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/float_type
default_max_ambient_temp_c
  Input should be a valid number [type=float_type, input_value=<Mock name='mock().create...p_c' id='2367299068528'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/float_type
default_desired_maintenance_temp_c
  Input should be a valid number [type=float_type, input_value=<Mock name='mock().create...p_c' id='2367299068864'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/float_type
default_safety_margin_percent
  Input should be a valid number [type=float_type, input_value=<Mock name='mock().create...ent' id='2367299069200'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/float_type
preferred_cable_manufacturers_json
  Input should be a valid string [type=string_type, input_value=<Mock name='mock().create...son' id='2367299069536'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
preferred_control_device_manufacturers_json
  Input should be a valid string [type=string_type, input_value=<Mock name='mock().create...son' id='2367299069872'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
user_id
  Input should be a valid integer [type=int_type, input_value=<Mock name='mock().create..._id' id='2367299070208'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/int_type
2025-06-03 19:10:24 - ultimate_electrical_designer.core.utils.uuid_utils - ERROR - Error generating UUID: module 'uuid' has no attribute 'uuid7' (uuid_utils.py:57)
2025-06-03 23:02:04 - ultimate_electrical_designer.core.database.engine - INFO - Creating database engine for environment: development (engine.py:60)
2025-06-03 23:02:04 - ultimate_electrical_designer.core.database.engine - INFO - Database engine created successfully using: SQLite (engine.py:73)
2025-06-03 23:02:04 - ultimate_electrical_designer.core.database.session - WARNING - All database tables dropped (session.py:179)
2025-06-03 23:02:04 - ultimate_electrical_designer.core.database.session - INFO - Database tables created successfully (session.py:155)
